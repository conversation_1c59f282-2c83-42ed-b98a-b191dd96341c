import { EventEmitter } from 'events'

// Plugin interfaces
export interface Plugin {
  id: string
  name: string
  version: string
  description: string
  author: string
  homepage?: string
  keywords?: string[]
  dependencies?: Record<string, string>
  permissions?: Permission[]
  
  // Lifecycle methods
  activate?(context: PluginContext): Promise<void> | void
  deactivate?(): Promise<void> | void
  
  // Extension points
  contributes?: PluginContributions
}

export interface PluginContext {
  subscriptions: Disposable[]
  workspaceState: Memento
  globalState: Memento
  extensionPath: string
  asAbsolutePath(relativePath: string): string
}

export interface PluginContributions {
  commands?: Command[]
  menus?: MenuContribution[]
  keybindings?: Keybinding[]
  themes?: Theme[]
  languages?: Language[]
  grammars?: Grammar[]
  snippets?: Snippet[]
  views?: ViewContribution[]
  viewsContainers?: ViewContainer[]
  problemMatchers?: ProblemMatcher[]
  taskDefinitions?: TaskDefinition[]
  debuggers?: Debugger[]
  breakpoints?: Breakpoint[]
  configuration?: Configuration
}

export interface Command {
  command: string
  title: string
  category?: string
  icon?: string | { light: string; dark: string }
  enablement?: string
  when?: string
}

export interface MenuContribution {
  commandPalette?: MenuItem[]
  editor?: {
    context?: MenuItem[]
    title?: MenuItem[]
  }
  explorer?: {
    context?: MenuItem[]
  }
  view?: {
    title?: MenuItem[]
    item?: {
      context?: MenuItem[]
    }
  }
}

export interface MenuItem {
  command: string
  when?: string
  group?: string
  alt?: string
}

export interface Keybinding {
  command: string
  key: string
  mac?: string
  linux?: string
  win?: string
  when?: string
  args?: any
}

export interface Permission {
  type: 'filesystem' | 'network' | 'clipboard' | 'notifications' | 'camera' | 'microphone'
  description: string
  required: boolean
}

export interface Disposable {
  dispose(): void
}

export interface Memento {
  get<T>(key: string): T | undefined
  get<T>(key: string, defaultValue: T): T
  update(key: string, value: any): Promise<void>
}

export class PluginManager extends EventEmitter {
  private plugins = new Map<string, Plugin>()
  private activePlugins = new Set<string>()
  private pluginContexts = new Map<string, PluginContext>()
  private commandRegistry = new Map<string, Function>()
  private menuContributions = new Map<string, MenuContribution>()
  private keybindings = new Map<string, Keybinding>()

  constructor() {
    super()
    this.setupCoreCommands()
  }

  // Plugin lifecycle management
  async registerPlugin(plugin: Plugin): Promise<void> {
    try {
      // Validate plugin
      this.validatePlugin(plugin)
      
      // Check dependencies
      await this.checkDependencies(plugin)
      
      // Check permissions
      await this.requestPermissions(plugin)
      
      // Register plugin
      this.plugins.set(plugin.id, plugin)
      
      // Create context
      const context = this.createPluginContext(plugin)
      this.pluginContexts.set(plugin.id, context)
      
      // Register contributions
      if (plugin.contributes) {
        this.registerContributions(plugin.id, plugin.contributes)
      }
      
      this.emit('pluginRegistered', plugin)
      console.log(`Plugin registered: ${plugin.name} v${plugin.version}`)
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.id}:`, error)
      throw error
    }
  }

  async activatePlugin(pluginId: string): Promise<void> {
    try {
      const plugin = this.plugins.get(pluginId)
      if (!plugin) {
        throw new Error(`Plugin not found: ${pluginId}`)
      }

      if (this.activePlugins.has(pluginId)) {
        console.warn(`Plugin already active: ${pluginId}`)
        return
      }

      // Activate dependencies first
      if (plugin.dependencies) {
        for (const depId of Object.keys(plugin.dependencies)) {
          if (!this.activePlugins.has(depId)) {
            await this.activatePlugin(depId)
          }
        }
      }

      // Call activate method
      if (plugin.activate) {
        const context = this.pluginContexts.get(pluginId)!
        await plugin.activate(context)
      }

      this.activePlugins.add(pluginId)
      this.emit('pluginActivated', plugin)
      console.log(`Plugin activated: ${plugin.name}`)
    } catch (error) {
      console.error(`Failed to activate plugin ${pluginId}:`, error)
      throw error
    }
  }

  async deactivatePlugin(pluginId: string): Promise<void> {
    try {
      const plugin = this.plugins.get(pluginId)
      if (!plugin) {
        throw new Error(`Plugin not found: ${pluginId}`)
      }

      if (!this.activePlugins.has(pluginId)) {
        console.warn(`Plugin not active: ${pluginId}`)
        return
      }

      // Check if other plugins depend on this one
      const dependents = this.getDependentPlugins(pluginId)
      if (dependents.length > 0) {
        throw new Error(`Cannot deactivate plugin ${pluginId}: required by ${dependents.join(', ')}`)
      }

      // Call deactivate method
      if (plugin.deactivate) {
        await plugin.deactivate()
      }

      // Dispose context subscriptions
      const context = this.pluginContexts.get(pluginId)
      if (context) {
        context.subscriptions.forEach(subscription => subscription.dispose())
        context.subscriptions.length = 0
      }

      this.activePlugins.delete(pluginId)
      this.emit('pluginDeactivated', plugin)
      console.log(`Plugin deactivated: ${plugin.name}`)
    } catch (error) {
      console.error(`Failed to deactivate plugin ${pluginId}:`, error)
      throw error
    }
  }

  async unregisterPlugin(pluginId: string): Promise<void> {
    try {
      // Deactivate first if active
      if (this.activePlugins.has(pluginId)) {
        await this.deactivatePlugin(pluginId)
      }

      // Remove contributions
      this.unregisterContributions(pluginId)

      // Remove from registry
      const plugin = this.plugins.get(pluginId)
      this.plugins.delete(pluginId)
      this.pluginContexts.delete(pluginId)

      if (plugin) {
        this.emit('pluginUnregistered', plugin)
        console.log(`Plugin unregistered: ${plugin.name}`)
      }
    } catch (error) {
      console.error(`Failed to unregister plugin ${pluginId}:`, error)
      throw error
    }
  }

  // Command system
  registerCommand(command: string, callback: Function): Disposable {
    if (this.commandRegistry.has(command)) {
      throw new Error(`Command already registered: ${command}`)
    }

    this.commandRegistry.set(command, callback)
    
    return {
      dispose: () => {
        this.commandRegistry.delete(command)
      }
    }
  }

  async executeCommand(command: string, ...args: any[]): Promise<any> {
    const callback = this.commandRegistry.get(command)
    if (!callback) {
      throw new Error(`Command not found: ${command}`)
    }

    try {
      return await callback(...args)
    } catch (error) {
      console.error(`Command execution failed: ${command}`, error)
      throw error
    }
  }

  getCommands(): string[] {
    return Array.from(this.commandRegistry.keys())
  }

  // Plugin queries
  getPlugin(pluginId: string): Plugin | undefined {
    return this.plugins.get(pluginId)
  }

  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  getActivePlugins(): Plugin[] {
    return Array.from(this.activePlugins)
      .map(id => this.plugins.get(id)!)
      .filter(Boolean)
  }

  isPluginActive(pluginId: string): boolean {
    return this.activePlugins.has(pluginId)
  }

  // Private methods
  private validatePlugin(plugin: Plugin): void {
    if (!plugin.id || !plugin.name || !plugin.version) {
      throw new Error('Plugin must have id, name, and version')
    }

    if (this.plugins.has(plugin.id)) {
      throw new Error(`Plugin already registered: ${plugin.id}`)
    }

    // Validate version format
    if (!/^\d+\.\d+\.\d+/.test(plugin.version)) {
      throw new Error('Plugin version must follow semantic versioning')
    }
  }

  private async checkDependencies(plugin: Plugin): Promise<void> {
    if (!plugin.dependencies) return

    for (const [depId, requiredVersion] of Object.entries(plugin.dependencies)) {
      const depPlugin = this.plugins.get(depId)
      if (!depPlugin) {
        throw new Error(`Missing dependency: ${depId}`)
      }

      // Simple version check (in real implementation, use semver)
      if (depPlugin.version !== requiredVersion) {
        console.warn(`Version mismatch for ${depId}: required ${requiredVersion}, found ${depPlugin.version}`)
      }
    }
  }

  private async requestPermissions(plugin: Plugin): Promise<void> {
    if (!plugin.permissions) return

    for (const permission of plugin.permissions) {
      if (permission.required) {
        // In real implementation, show permission dialog
        console.log(`Plugin ${plugin.name} requests ${permission.type} permission: ${permission.description}`)
      }
    }
  }

  private createPluginContext(plugin: Plugin): PluginContext {
    return {
      subscriptions: [],
      workspaceState: new MemoryMemento(),
      globalState: new MemoryMemento(),
      extensionPath: `/plugins/${plugin.id}`,
      asAbsolutePath: (relativePath: string) => `/plugins/${plugin.id}/${relativePath}`
    }
  }

  private registerContributions(pluginId: string, contributions: PluginContributions): void {
    // Register commands
    if (contributions.commands) {
      contributions.commands.forEach(command => {
        // Register command metadata
        console.log(`Registered command: ${command.command} from plugin ${pluginId}`)
      })
    }

    // Register menus
    if (contributions.menus) {
      this.menuContributions.set(pluginId, contributions.menus)
    }

    // Register keybindings
    if (contributions.keybindings) {
      contributions.keybindings.forEach(keybinding => {
        this.keybindings.set(`${pluginId}:${keybinding.command}`, keybinding)
      })
    }
  }

  private unregisterContributions(pluginId: string): void {
    this.menuContributions.delete(pluginId)
    
    // Remove keybindings
    for (const [key, keybinding] of this.keybindings.entries()) {
      if (key.startsWith(`${pluginId}:`)) {
        this.keybindings.delete(key)
      }
    }
  }

  private getDependentPlugins(pluginId: string): string[] {
    const dependents: string[] = []
    
    for (const [id, plugin] of this.plugins.entries()) {
      if (plugin.dependencies && plugin.dependencies[pluginId] && this.activePlugins.has(id)) {
        dependents.push(id)
      }
    }
    
    return dependents
  }

  private setupCoreCommands(): void {
    this.registerCommand('noteflow.showCommands', () => {
      return this.getCommands()
    })

    this.registerCommand('noteflow.listPlugins', () => {
      return this.getAllPlugins().map(p => ({
        id: p.id,
        name: p.name,
        version: p.version,
        active: this.isPluginActive(p.id)
      }))
    })
  }
}

// Simple in-memory implementation of Memento
class MemoryMemento implements Memento {
  private storage = new Map<string, any>()

  get<T>(key: string): T | undefined
  get<T>(key: string, defaultValue: T): T
  get<T>(key: string, defaultValue?: T): T | undefined {
    const value = this.storage.get(key)
    return value !== undefined ? value : defaultValue
  }

  async update(key: string, value: any): Promise<void> {
    this.storage.set(key, value)
  }
}

// Global plugin manager instance
export const pluginManager = new PluginManager()
