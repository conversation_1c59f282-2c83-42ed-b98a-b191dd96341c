{"name": "noteflow-app", "version": "1.0.0", "description": "Sistema de notas ultra moderno com AI integrada", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "firebase:emulators": "firebase emulators:start", "firebase:deploy": "npm run build && firebase deploy", "firebase:deploy:hosting": "npm run build && firebase deploy --only hosting", "firebase:deploy:functions": "firebase deploy --only functions", "firebase:deploy:firestore": "firebase deploy --only firestore"}, "dependencies": {"firebase": "^10.7.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-hot-toast": "^2.4.1", "react-i18next": "^15.5.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^22.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6"}, "keywords": ["notes", "productivity", "ai", "react", "typescript", "tailwindcss"], "author": "NoteFlow Team", "license": "MIT"}