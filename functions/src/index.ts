import * as functions from 'firebase-functions'
import * as admin from 'firebase-admin'
import { onDocumentCreated, onDocumentUpdated, onDocumentDeleted } from 'firebase-functions/v2/firestore'
import { onCall, HttpsError } from 'firebase-functions/v2/https'
import { onSchedule } from 'firebase-functions/v2/scheduler'

// Initialize Firebase Admin
admin.initializeApp()

// AI Functions
export { generateTagSuggestions, generateSummary, improveWriting } from './ai'

// Analytics Functions
export { updateUserAnalytics, generateInsights } from './analytics'

// Collaboration Functions
export { shareNote, addCollaborator, removeCollaborator } from './collaboration'

// Export Functions
export { exportToPDF, exportToWord, exportToMarkdown } from './export'

// Notification Functions
export { sendNotification, sendEmail } from './notifications'

// Triggers

// When a note is created
export const onNoteCreated = onDocumentCreated('notes/{noteId}', async (event) => {
  const noteData = event.data?.data()
  if (!noteData) return

  const { userId } = noteData

  try {
    // Update user stats
    await admin.firestore().doc(`users/${userId}`).update({
      'stats.totalNotes': admin.firestore.FieldValue.increment(1),
      'stats.totalWords': admin.firestore.FieldValue.increment(noteData.wordCount || 0),
      'stats.lastActiveAt': admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    })

    // Check for daily streak
    await updateDailyStreak(userId)

    // Generate AI suggestions if enabled
    if (noteData.content && noteData.content.length > 50) {
      const suggestions = await generateAISuggestions(noteData.content)
      
      if (suggestions.tags.length > 0) {
        await event.data?.ref.update({
          suggestedTags: suggestions.tags,
          aiSummary: suggestions.summary
        })
      }
    }

    console.log(`Note created: ${event.params.noteId} for user: ${userId}`)
  } catch (error) {
    console.error('Error in onNoteCreated:', error)
  }
})

// When a note is updated
export const onNoteUpdated = onDocumentUpdated('notes/{noteId}', async (event) => {
  const beforeData = event.data?.before.data()
  const afterData = event.data?.after.data()
  
  if (!beforeData || !afterData) return

  const { userId } = afterData
  const wordCountDiff = (afterData.wordCount || 0) - (beforeData.wordCount || 0)

  try {
    // Update user stats if word count changed
    if (wordCountDiff !== 0) {
      await admin.firestore().doc(`users/${userId}`).update({
        'stats.totalWords': admin.firestore.FieldValue.increment(wordCountDiff),
        'stats.lastActiveAt': admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      })
    }

    // Notify collaborators if note was shared
    if (afterData.collaborators && afterData.collaborators.length > 0) {
      await notifyCollaborators(event.params.noteId, afterData, 'updated')
    }

    console.log(`Note updated: ${event.params.noteId}`)
  } catch (error) {
    console.error('Error in onNoteUpdated:', error)
  }
})

// When a note is deleted
export const onNoteDeleted = onDocumentDeleted('notes/{noteId}', async (event) => {
  const noteData = event.data?.data()
  if (!noteData) return

  const { userId } = noteData

  try {
    // Update user stats
    await admin.firestore().doc(`users/${userId}`).update({
      'stats.totalNotes': admin.firestore.FieldValue.increment(-1),
      'stats.totalWords': admin.firestore.FieldValue.increment(-(noteData.wordCount || 0)),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    })

    // Delete related subcollections
    const batch = admin.firestore().batch()
    
    // Delete versions
    const versionsSnapshot = await admin.firestore()
      .collection(`notes/${event.params.noteId}/versions`)
      .get()
    
    versionsSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref)
    })

    // Delete comments
    const commentsSnapshot = await admin.firestore()
      .collection(`notes/${event.params.noteId}/comments`)
      .get()
    
    commentsSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref)
    })

    await batch.commit()

    console.log(`Note deleted: ${event.params.noteId}`)
  } catch (error) {
    console.error('Error in onNoteDeleted:', error)
  }
})

// Scheduled Functions

// Daily analytics update
export const dailyAnalyticsUpdate = onSchedule('0 2 * * *', async () => {
  try {
    const usersSnapshot = await admin.firestore().collection('users').get()
    
    for (const userDoc of usersSnapshot.docs) {
      const userId = userDoc.id
      await generateDailyAnalytics(userId)
    }

    console.log('Daily analytics update completed')
  } catch (error) {
    console.error('Error in dailyAnalyticsUpdate:', error)
  }
})

// Weekly backup
export const weeklyBackup = onSchedule('0 3 * * 0', async () => {
  try {
    // Implement backup logic
    console.log('Weekly backup started')
    
    // Export all notes to Cloud Storage
    const notesSnapshot = await admin.firestore().collection('notes').get()
    const backupData = {
      timestamp: new Date().toISOString(),
      notes: notesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    }

    const bucket = admin.storage().bucket()
    const fileName = `backups/notes-${new Date().toISOString().split('T')[0]}.json`
    const file = bucket.file(fileName)
    
    await file.save(JSON.stringify(backupData, null, 2), {
      metadata: {
        contentType: 'application/json'
      }
    })

    console.log(`Weekly backup completed: ${fileName}`)
  } catch (error) {
    console.error('Error in weeklyBackup:', error)
  }
})

// Helper Functions

async function updateDailyStreak(userId: string): Promise<void> {
  const userRef = admin.firestore().doc(`users/${userId}`)
  const userDoc = await userRef.get()
  const userData = userDoc.data()
  
  if (!userData) return

  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  const lastActive = userData.stats?.lastActiveAt?.toDate()
  if (!lastActive) {
    // First note ever
    await userRef.update({
      'stats.dailyStreak': 1,
      'stats.lastStreakDate': admin.firestore.Timestamp.fromDate(today)
    })
    return
  }

  const lastActiveDate = new Date(lastActive)
  lastActiveDate.setHours(0, 0, 0, 0)
  
  const daysDiff = Math.floor((today.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60 * 24))
  
  if (daysDiff === 1) {
    // Consecutive day
    await userRef.update({
      'stats.dailyStreak': admin.firestore.FieldValue.increment(1),
      'stats.lastStreakDate': admin.firestore.Timestamp.fromDate(today)
    })
  } else if (daysDiff > 1) {
    // Streak broken
    await userRef.update({
      'stats.dailyStreak': 1,
      'stats.lastStreakDate': admin.firestore.Timestamp.fromDate(today)
    })
  }
  // daysDiff === 0 means same day, no update needed
}

async function generateAISuggestions(content: string): Promise<{ tags: string[], summary: string }> {
  // Simple keyword-based suggestions (replace with actual AI service)
  const keywords = {
    'meeting': ['meeting', 'reunião', 'call', 'conference'],
    'idea': ['idea', 'brainstorm', 'concept', 'innovation'],
    'task': ['task', 'todo', 'action', 'deadline'],
    'project': ['project', 'projeto', 'development', 'build'],
    'research': ['research', 'study', 'analysis', 'investigation']
  }

  const tags: string[] = []
  const lowerContent = content.toLowerCase()

  Object.entries(keywords).forEach(([tag, words]) => {
    if (words.some(word => lowerContent.includes(word))) {
      tags.push(tag)
    }
  })

  // Generate simple summary (first 100 characters)
  const summary = content.replace(/<[^>]*>/g, '').substring(0, 100) + '...'

  return { tags, summary }
}

async function notifyCollaborators(noteId: string, noteData: any, action: string): Promise<void> {
  if (!noteData.collaborators || noteData.collaborators.length === 0) return

  const batch = admin.firestore().batch()

  for (const collaboratorId of noteData.collaborators) {
    const notificationRef = admin.firestore().collection('notifications').doc()
    batch.set(notificationRef, {
      userId: collaboratorId,
      type: 'collaboration',
      title: `Nota ${action}`,
      message: `A nota "${noteData.title}" foi ${action}`,
      noteId,
      read: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    })
  }

  await batch.commit()
}

async function generateDailyAnalytics(userId: string): Promise<void> {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  yesterday.setHours(0, 0, 0, 0)

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // Get notes created yesterday
  const notesSnapshot = await admin.firestore()
    .collection('notes')
    .where('userId', '==', userId)
    .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(yesterday))
    .where('createdAt', '<', admin.firestore.Timestamp.fromDate(today))
    .get()

  const analytics = {
    date: admin.firestore.Timestamp.fromDate(yesterday),
    notesCreated: notesSnapshot.size,
    wordsWritten: notesSnapshot.docs.reduce((total, doc) => total + (doc.data().wordCount || 0), 0),
    categoriesUsed: [...new Set(notesSnapshot.docs.map(doc => doc.data().category))],
    tagsUsed: [...new Set(notesSnapshot.docs.flatMap(doc => doc.data().tags || []))]
  }

  await admin.firestore()
    .collection(`analytics/${userId}/daily`)
    .doc(yesterday.toISOString().split('T')[0])
    .set(analytics)
}
