{"hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "/service-worker.js", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"source": "functions", "runtime": "nodejs18", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}