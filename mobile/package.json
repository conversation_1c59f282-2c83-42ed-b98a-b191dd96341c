{"name": "noteflow-mobile", "version": "1.0.0", "description": "NoteFlow Mobile App - React Native", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace NoteFlow.xcworkspace -scheme NoteFlow -configuration Release -destination generic/platform=iOS -archivePath NoteFlow.xcarchive archive", "clean": "react-native clean", "pod-install": "cd ios && pod install"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-vector-icons": "^10.0.2", "react-native-paper": "^5.11.1", "react-native-async-storage": "@react-native-async-storage/async-storage", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "react-native-push-notification": "^8.1.1", "react-native-background-job": "^0.2.9", "react-native-share": "^9.4.1", "react-native-document-picker": "^9.1.1", "react-native-fs": "^2.20.0", "react-native-sqlite-storage": "^6.0.1", "react-native-voice": "^3.2.4", "react-native-camera": "^4.2.1", "react-native-image-picker": "^7.0.3", "react-native-markdown-display": "^7.0.0", "react-native-webview": "^13.6.3", "react-native-netinfo": "@react-native-community/netinfo", "react-native-device-info": "^10.11.0", "react-native-haptic-feedback": "^2.2.0", "react-native-orientation-locker": "^1.5.0", "react-native-splash-screen": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile", "notes", "productivity", "ai"], "author": "NoteFlow Team", "license": "MIT"}