# 🔥 **<PERSON><PERSON><PERSON> de Deploy Firebase - NoteFlow**

Este guia te ajudará a fazer o deploy completo do NoteFlow no Firebase.

## 📋 **Pré-requisitos**

1. **Node.js 18+** instalado
2. **Conta Google** ativa
3. **Firebase CLI** instalado globalmente:
   ```bash
   npm install -g firebase-tools
   ```

## 🚀 **Passo a Passo do Deploy**

### **1. Configuração do Projeto Firebase**

#### **1.1. Criar Projeto no Firebase Console**
1. Acesse [Firebase Console](https://console.firebase.google.com/)
2. Clique em "Criar projeto"
3. Nome do projeto: `noteflow-app` (ou seu nome preferido)
4. Ative Google Analytics (recomendado)
5. Aguarde a criação do projeto

#### **1.2. Configurar Serviços**

**Authentication:**
1. No console, vá em "Authentication" > "Get started"
2. Aba "Sign-in method":
   - Ative "Email/Password"
   - Ative "Google" (configure OAuth)
   - Ative "GitHub" (configure OAuth)

**Firestore Database:**
1. Vá em "Firestore Database" > "Create database"
2. Escolha "Start in test mode" (temporário)
3. Selecione localização (us-central1 recomendado)

**Storage:**
1. Vá em "Storage" > "Get started"
2. Aceite as regras padrão
3. Selecione a mesma localização do Firestore

**Hosting:**
1. Vá em "Hosting" > "Get started"
2. Siga as instruções (será configurado via CLI)

### **2. Configuração Local**

#### **2.1. Login no Firebase**
```bash
firebase login
```

#### **2.2. Inicializar Projeto**
```bash
# Na raiz do projeto
firebase init
```

Selecione:
- ✅ Firestore
- ✅ Functions
- ✅ Hosting
- ✅ Storage

Configurações:
- **Firestore Rules**: `firestore.rules`
- **Firestore Indexes**: `firestore.indexes.json`
- **Functions Language**: TypeScript
- **Functions Source**: `functions`
- **Hosting Public**: `dist`
- **Single Page App**: Yes
- **Storage Rules**: `storage.rules`

#### **2.3. Configurar Variáveis de Ambiente**

Copie o arquivo de exemplo:
```bash
cp .env.example .env
```

No Firebase Console, vá em "Project Settings" > "General" > "Your apps" > "Web app":
1. Clique em "Add app"
2. Nome: "NoteFlow Web"
3. Copie as configurações para o `.env`:

```env
VITE_FIREBASE_API_KEY=sua_api_key
VITE_FIREBASE_AUTH_DOMAIN=seu-projeto.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=seu-projeto-id
VITE_FIREBASE_STORAGE_BUCKET=seu-projeto.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef
VITE_FIREBASE_MEASUREMENT_ID=G-ABCDEFGHIJ
```

### **3. Configuração das Functions**

#### **3.1. Instalar Dependências**
```bash
cd functions
npm install
cd ..
```

#### **3.2. Configurar Variáveis de Ambiente das Functions**
```bash
# OpenAI API Key (opcional, para AI features)
firebase functions:config:set openai.api_key="sua_openai_key"

# Email configuration (opcional, para notificações)
firebase functions:config:set email.user="<EMAIL>"
firebase functions:config:set email.password="sua_senha_app"
```

### **4. Deploy**

#### **4.1. Build da Aplicação**
```bash
npm install
npm run build
```

#### **4.2. Deploy Completo**
```bash
firebase deploy
```

Ou deploy por partes:
```bash
# Apenas Hosting
firebase deploy --only hosting

# Apenas Functions
firebase deploy --only functions

# Apenas Firestore Rules
firebase deploy --only firestore
```

### **5. Configurações Pós-Deploy**

#### **5.1. Configurar Domínio Personalizado (Opcional)**
1. No Firebase Console > Hosting
2. Clique em "Add custom domain"
3. Siga as instruções para configurar DNS

#### **5.2. Configurar Regras de Segurança**

**Firestore Rules** (já configurado em `firestore.rules`):
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Regras já definidas no arquivo
  }
}
```

**Storage Rules**:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

#### **5.3. Configurar Índices do Firestore**
Os índices serão criados automaticamente baseados no arquivo `firestore.indexes.json`.

### **6. Monitoramento e Manutenção**

#### **6.1. Logs das Functions**
```bash
firebase functions:log
```

#### **6.2. Monitoramento**
- Firebase Console > Functions > Logs
- Firebase Console > Performance
- Firebase Console > Crashlytics (se configurado)

#### **6.3. Backup Automático**
As functions já incluem backup semanal automático para Cloud Storage.

## 🔧 **Scripts Úteis**

```bash
# Desenvolvimento local com emuladores
npm run firebase:emulators

# Deploy apenas hosting
npm run firebase:deploy:hosting

# Deploy apenas functions
npm run firebase:deploy:functions

# Deploy apenas firestore
npm run firebase:deploy:firestore

# Deploy completo
npm run firebase:deploy
```

## 🚨 **Troubleshooting**

### **Erro de Permissões**
```bash
firebase login --reauth
```

### **Erro de Build**
```bash
rm -rf node_modules package-lock.json
npm install
npm run build
```

### **Erro nas Functions**
```bash
cd functions
npm run build
cd ..
firebase deploy --only functions
```

### **Erro de Quota**
- Verifique limites no Firebase Console
- Considere upgrade para plano Blaze se necessário

## 📊 **Custos Estimados**

**Plano Spark (Gratuito):**
- Hosting: 10GB/mês
- Firestore: 1GB storage, 50K reads/day
- Functions: 125K invocations/mês
- Authentication: Ilimitado

**Plano Blaze (Pay-as-you-go):**
- Hosting: $0.026/GB
- Firestore: $0.18/100K reads
- Functions: $0.40/1M invocations
- Storage: $0.026/GB

## 🎉 **Sucesso!**

Após o deploy, sua aplicação estará disponível em:
- **URL Firebase**: `https://seu-projeto.web.app`
- **URL Personalizada**: `https://seu-dominio.com` (se configurado)

### **Próximos Passos:**
1. Configure Analytics
2. Configure Push Notifications
3. Implemente CI/CD com GitHub Actions
4. Configure monitoramento avançado
5. Otimize performance

---

**🔥 NoteFlow está no ar! Parabéns! 🎉**
