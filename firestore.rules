rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Notes - users can only access their own notes
    match /notes/{noteId} {
      allow read, write: if request.auth != null &&
        (resource.data.userId == request.auth.uid ||
         request.auth.uid in resource.data.collaborators);

      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;

      // Allow reading public notes
      allow read: if resource.data.isPublic == true;

      // Note versions subcollection
      match /versions/{versionId} {
        allow read, write: if request.auth != null &&
          get(/databases/$(database)/documents/notes/$(noteId)).data.userId == request.auth.uid;
      }

      // Comments subcollection
      match /comments/{commentId} {
        allow read: if request.auth != null &&
          (get(/databases/$(database)/documents/notes/$(noteId)).data.userId == request.auth.uid ||
           request.auth.uid in get(/databases/$(database)/documents/notes/$(noteId)).data.collaborators ||
           get(/databases/$(database)/documents/notes/$(noteId)).data.isPublic == true);

        allow create: if request.auth != null &&
          request.resource.data.userId == request.auth.uid;

        allow update, delete: if request.auth != null &&
          resource.data.userId == request.auth.uid;
      }
    }

    // Shared notes collection for collaboration
    match /shared/{shareId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null &&
        request.resource.data.ownerId == request.auth.uid;
      allow update, delete: if request.auth != null &&
        resource.data.ownerId == request.auth.uid;
    }

    // Analytics data - only for the user
    match /analytics/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Templates - public read, authenticated write
    match /templates/{templateId} {
      allow read: if true;
      allow create, update: if request.auth != null;
      allow delete: if request.auth != null &&
        resource.data.authorId == request.auth.uid;
    }

    // Notifications - users can only access their own notifications
    match /notifications/{notificationId} {
      // Read and update existing notifications
      allow read, update, delete: if request.auth != null &&
        resource.data.userId == request.auth.uid;

      // Create new notifications
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid &&
        isValidNotification(request.resource.data);
    }

    // Categories - users can only access their own categories
    match /categories/{categoryId} {
      allow read, write: if request.auth != null &&
        resource.data.userId == request.auth.uid;

      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid &&
        isValidCategory(request.resource.data);

      allow update: if request.auth != null &&
        resource.data.userId == request.auth.uid &&
        isValidCategory(request.resource.data);

      allow delete: if request.auth != null &&
        resource.data.userId == request.auth.uid &&
        resource.data.isDefault != true;
    }

    // Function to validate note data
    function isValidNote(note) {
      return note.keys().hasAll(['title', 'content', 'userId', 'createdAt', 'updatedAt']) &&
             note.title is string &&
             note.content is string &&
             note.userId is string &&
             note.createdAt is timestamp &&
             note.updatedAt is timestamp;
    }

    // Function to validate category data
    function isValidCategory(category) {
      return category.keys().hasAll(['name', 'color', 'icon', 'userId', 'createdAt', 'updatedAt']) &&
             category.name is string &&
             category.color is string &&
             category.icon is string &&
             category.userId is string &&
             category.createdAt is timestamp &&
             category.updatedAt is timestamp &&
             category.name.size() > 0 &&
             category.name.size() <= 50;
    }

    // Function to validate notification data
    function isValidNotification(notification) {
      return notification.keys().hasAll(['type', 'message', 'userId', 'read', 'createdAt']) &&
             notification.type is string &&
             notification.message is string &&
             notification.userId is string &&
             notification.read is bool &&
             notification.createdAt is timestamp &&
             notification.message.size() > 0 &&
             notification.message.size() <= 500;
    }

    // Function to validate user permissions
    function hasNoteAccess(noteId) {
      return request.auth != null &&
        (get(/databases/$(database)/documents/notes/$(noteId)).data.userId == request.auth.uid ||
         request.auth.uid in get(/databases/$(database)/documents/notes/$(noteId)).data.collaborators);
    }
  }
}
