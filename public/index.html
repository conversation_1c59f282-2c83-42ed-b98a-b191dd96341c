<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

    <!-- Primary Meta Tags -->
    <title>NoteFlow - Sistema de Notas Ultra Moderno</title>
    <meta name="title" content="NoteFlow - Sistema de Notas Ultra Moderno" />
    <meta name="description" content="Sistema de notas com AI integrada, colaboração em tempo real e recursos avançados de produtividade" />
    <meta name="keywords" content="notas, produtividade, AI, colaboração, mind map, pomodoro" />
    <meta name="author" content="NoteFlow Team" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://noteflow.app/" />
    <meta property="og:title" content="NoteFlow - Sistema de Notas Ultra Moderno" />
    <meta property="og:description" content="Sistema de notas com AI integrada, colaboração em tempo real e recursos avançados de produtividade" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://noteflow.app/" />
    <meta property="twitter:title" content="NoteFlow - Sistema de Notas Ultra Moderno" />
    <meta property="twitter:description" content="Sistema de notas com AI integrada, colaboração em tempo real e recursos avançados de produtividade" />
    <meta property="twitter:image" content="/twitter-image.png" />

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="NoteFlow" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="NoteFlow" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="msapplication-TileColor" content="#3b82f6" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="theme-color" content="#3b82f6" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/favicon.svg" />

    <!-- Favicons -->
    <link rel="shortcut icon" href="/favicon.svg" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Preload critical resources -->
    <link rel="preload" href="/src/main.tsx" as="script" />
    <link rel="preload" href="/src/index.css" as="style" />

    <!-- Ensure CSS is loaded -->
    <style>
      /* Fallback styles while CSS loads */
      body { font-family: Inter, sans-serif; margin: 0; padding: 0; }
      #root { min-height: 100vh; }
    </style>
  </head>
  <body>
    <!-- App container -->
    <div id="root"></div>

    <!-- Loading fallback -->
    <noscript>
      <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Inter, sans-serif;">
        <div style="text-align: center;">
          <h1>NoteFlow</h1>
          <p>JavaScript é necessário para executar esta aplicação.</p>
        </div>
      </div>
    </noscript>

    <!-- Main app script -->
    <script type="module" src="/src/main.tsx"></script>

    <!-- Service Worker registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
