import React from 'react';

// Import components
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import NoteCard from './components/NoteCard';
import MindMap from './components/MindMap';
import { QuickCaptureModal, CommandPaletteModal, TemplatesModal } from './components/Modals';

// Import hooks and utilities
import { useNotesApp } from './src/hooks/useNotesApp';
import { formatTime, copyNoteToClipboard, exportNote } from './utils';

const NotesApp = () => {
  // Use custom hook for state management
  const {
    // Core states
    darkMode,
    showSidebar,
    showMindMap,
    showQuickCapture,
    showCommandPalette,
    showTemplates,
    viewMode,
    searchTerm,
    filterTag,
    filterCategory,
    pomodoroActive,
    pomodoroTime,
    focusMode,
    showAnalytics,
    dailyStreak,
    totalWords,
    notifications,
    showNotifications,

    // Data
    sortedNotes,
    allTags,

    // Setters
    setDarkMode,
    setShowSidebar,
    setShowMindMap,
    setShowQuickCapture,
    setShowCommandPalette,
    setShowTemplates,
    setViewMode,
    setSearchTerm,
    setFilterTag,
    setFilterCategory,
    setPomodoroActive,
    setPomodoroTime,
    setFocusMode,
    setShowAnalytics,
    setShowNotifications,

    // Handlers
    handleNewNote,
    handleUseTemplate,
    handleEditNote,
    handleDeleteNote,
    handleToggleStar,
    handleTogglePin,
    handleToggleLock,
    handleTogglePublic,
    handleQuickCapture
  } = useNotesApp();



  // Simulate real-time collaboration
  useEffect(() => {
    const interval = setInterval(() => {
      const onlineCollaborators = collaborators.filter(() => Math.random() > 0.3);
      if (selectedNote && selectedNote.collaborators && selectedNote.collaborators.length > 0) {
        setActiveUsers(onlineCollaborators.slice(0, Math.floor(Math.random() * 3) + 1));
      }
    }, 5000);
    return () => clearInterval(interval);
  }, [collaborators, selectedNote]);

  // Pomodoro Timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (pomodoroActive && pomodoroTime > 0) {
      interval = setInterval(() => {
        setPomodoroTime(prev => prev - 1);
      }, 1000);
    } else if (pomodoroTime === 0) {
      setPomodoroActive(false);
      addNotification('timer', 'Pomodoro', 'concluído! Hora de uma pausa!', 'agora');
    }
    return () => clearInterval(interval);
  }, [pomodoroActive, pomodoroTime]);

  // Recording Timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRecording]);

  // AI Suggestions
  useEffect(() => {
    if (noteContent && showAISuggestions) {
      const words = noteContent.toLowerCase().split(' ');
      const suggestions: string[] = [];

      if (words.some(w => ['meeting', 'reunião', 'call'].includes(w))) suggestions.push('meeting');
      if (words.some(w => ['idea', 'ideia', 'concept'].includes(w))) suggestions.push('brainstorm');
      if (words.some(w => ['todo', 'task', 'fazer'].includes(w))) suggestions.push('tasks');
      if (words.some(w => ['bug', 'error', 'problema'].includes(w))) suggestions.push('debug');
      if (words.some(w => ['learn', 'study', 'estudar'].includes(w))) suggestions.push('learning');

      setSuggestedTags([...new Set(suggestions)]);
    }
  }, [noteContent, showAISuggestions]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setShowCommandPalette(true);
      }
      if ((e.metaKey || e.ctrlKey) && e.key === 'n') {
        e.preventDefault();
        handleNewNote();
      }
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'l') {
        e.preventDefault();
        setShowQuickCapture(true);
      }
      if (e.key === 'Escape') {
        setShowCommandPalette(false);
        setSelectedNote(null);
        setIsEditing(false);
        setShowQuickCapture(false);
        setShowShareModal(false);
        setShowComments(false);
        setShowVersionHistory(false);
        setShowNotifications(false);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Helper functions
  const addNotification = (type: string, user: string, message: string, time: string) => {
    const newNotification: Notification = {
      id: Date.now(),
      type,
      user,
      message,
      time,
      read: false
    };
    setNotifications(prev => [newNotification, ...prev]);
  };

  const calculateStats = (content: string) => {
    const text = content.replace(/<[^>]*>/g, '');
    const words = text.trim().split(/\s+/).length;
    const readTime = Math.ceil(words / 200);
    return { words, readTime };
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handlers
  const handleNewNote = () => {
    setSelectedNote(null);
    setIsEditing(true);
    setShowNewNote(true);
    setNoteTitle('');
    setNoteContent('');
    setNoteTags([]);
    setNoteColor('#3B82F6');
    setNoteCategory('pessoal');
    setNoteMood('neutral');
    setShowAISuggestions(true);
    setNoteConnections([]);
  };

  const handleUseTemplate = (template: Template) => {
    setSelectedNote(null);
    setIsEditing(true);
    setShowNewNote(true);
    setNoteTitle(template.name);
    setNoteContent(template.content);
    setNoteTags([template.id]);
    setNoteColor('#3B82F6');
    setNoteCategory('pessoal');
    setNoteMood('neutral');
    setShowTemplates(false);
  };

  const handleSaveNote = () => {
    if (!noteTitle.trim()) return;

    const stats = calculateStats(noteContent);

    if (selectedNote) {
      setNotes(notes.map(note =>
        note.id === selectedNote.id
          ? {
              ...note,
              title: noteTitle,
              content: noteContent,
              tags: noteTags,
              color: noteColor,
              category: noteCategory,
              mood: noteMood,
              updatedAt: new Date().toISOString(),
              wordCount: stats.words,
              readTime: stats.readTime,
              version: (note.version || 1) + 1,
              connections: noteConnections
            }
          : note
      ));

      if (selectedNote.collaborators && selectedNote.collaborators.length > 0) {
        selectedNote.collaborators.forEach((collab: Collaborator) => {
          if (collab.id !== 1) {
            addNotification('edit', 'You', 'editou uma nota compartilhada', 'agora');
          }
        });
      }
    } else {
      const newNote: Note = {
        id: Date.now(),
        title: noteTitle,
        content: noteContent,
        tags: noteTags,
        color: noteColor,
        category: noteCategory,
        mood: noteMood,
        starred: false,
        pinned: false,
        locked: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        wordCount: stats.words,
        readTime: stats.readTime,
        connections: noteConnections,
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      };
      setNotes([newNote, ...notes]);
      setTotalWords(prev => prev + stats.words);
    }

    setIsEditing(false);
    setShowNewNote(false);
    setShowAISuggestions(false);
    setNoteConnections([]);
  };

  const handleDeleteNote = (id: number) => {
    const noteToDelete = notes.find(n => n.id === id);
    if (noteToDelete && noteToDelete.permissions !== 'owner') {
      alert('Você não tem permissão para deletar esta nota');
      return;
    }

    setNotes(notes.filter(note => note.id !== id));
    if (selectedNote && selectedNote.id === id) {
      setSelectedNote(null);
      setIsEditing(false);
    }
  };

  const handleEditNote = (note: Note) => {
    if (note.locked && note.permissions !== 'owner') return;

    setSelectedNote(note);
    setIsEditing(true);
    setNoteTitle(note.title);
    setNoteContent(note.content);
    setNoteTags(note.tags);
    setNoteColor(note.color);
    setNoteCategory(note.category);
    setNoteMood(note.mood || 'neutral');
    setNoteConnections(note.connections || []);
  };

  const handleQuickCapture = () => {
    if (!quickCaptureText.trim()) return;

    const newNote: Note = {
      id: Date.now(),
      title: quickCaptureText.split('\n')[0].substring(0, 50) || 'Quick Note',
      content: `<p>${quickCaptureText}</p>`,
      tags: ['quick-capture'],
      color: '#10B981',
      category: 'pessoal',
      starred: false,
      pinned: false,
      locked: false,
      mood: 'neutral',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      wordCount: quickCaptureText.split(' ').length,
      readTime: 1,
      connections: [],
      todos: [],
      audioNote: null,
      collaborators: [],
      comments: [],
      version: 1,
      isPublic: false,
      permissions: 'owner'
    };

    setNotes([newNote, ...notes]);
    setQuickCaptureText('');
    setShowQuickCapture(false);
  };

  const handleShareNote = () => {
    if (!shareEmail.trim() || !selectedNote) return;

    const newCollaborator: Collaborator = {
      id: Date.now(),
      name: shareEmail.split('@')[0],
      email: shareEmail,
      avatar: '👤',
      permission: sharePermission,
      isOnline: false
    };

    setCollaborators([...collaborators, newCollaborator]);

    setNotes(notes.map(note =>
      note.id === selectedNote.id
        ? { ...note, collaborators: [...(note.collaborators || []), newCollaborator] }
        : note
    ));

    addNotification('share', 'Você', `compartilhou com ${newCollaborator.name}`, 'agora');

    setShareEmail('');
    setShowShareModal(false);
  };

  const handleAddComment = () => {
    if (!commentText.trim() || !selectedNote) return;

    const newComment: Comment = {
      id: Date.now(),
      user: 'You',
      avatar: '👤',
      text: commentText,
      time: new Date().toISOString()
    };

    setNotes(notes.map(note =>
      note.id === selectedNote.id
        ? { ...note, comments: [...(note.comments || []), newComment] }
        : note
    ));

    if (selectedNote.collaborators) {
      selectedNote.collaborators.forEach((collab: Collaborator) => {
        if (collab.id !== 1) {
          addNotification('comment', 'You', 'comentou em uma nota compartilhada', 'agora');
        }
      });
    }

    setCommentText('');
  };

  const handleToggleStar = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, starred: !note.starred } : note
    ));
  };

  const handleTogglePin = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, pinned: !note.pinned } : note
    ));
  };

  const handleToggleLock = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, locked: !note.locked } : note
    ));
  };

  const handleTogglePublic = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, isPublic: !note.isPublic } : note
    ));

    const note = notes.find(n => n.id === id);
    if (note) {
      addNotification('share', 'You', `tornou a nota "${note.title}" ${note.isPublic ? 'privada' : 'pública'}`, 'agora');
    }
  };

  const handleCopyNote = (note: Note) => {
    navigator.clipboard.writeText(`${note.title}\n\n${note.content.replace(/<[^>]*>/g, '')}`);
    setCopiedId(note.id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  const handleExportNote = (note: Note) => {
    const element = document.createElement('a');
    const file = new Blob([`# ${note.title}\n\n${note.content.replace(/<[^>]*>/g, '')}\n\nTags: ${note.tags.join(', ')}`], {type: 'text/markdown'});
    element.href = URL.createObjectURL(file);
    element.download = `${note.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const formatText = (command: string) => {
    document.execCommand(command, false, undefined);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  const insertLink = () => {
    const url = prompt('Digite a URL:');
    if (url) {
      document.execCommand('createLink', false, url);
      if (editorRef.current) {
        editorRef.current.focus();
      }
    }
  };

  // Computed values
  const filteredNotes = useMemo(() => {
    return notes.filter(note => {
      const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          note.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          note.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesTag = !filterTag || note.tags.includes(filterTag);
      const matchesCategory = !filterCategory || note.category === filterCategory;
      return matchesSearch && matchesTag && matchesCategory;
    });
  }, [notes, searchTerm, filterTag, filterCategory]);

  const allTags = useMemo(() => {
    const tags = new Set<string>();
    notes.forEach(note => note.tags.forEach(tag => tags.add(tag)));
    return Array.from(tags);
  }, [notes]);

  const sortedNotes = useMemo(() => {
    return [...filteredNotes].sort((a, b) => {
      if (a.pinned && !b.pinned) return -1;
      if (!a.pinned && b.pinned) return 1;
      if (a.starred && !b.starred) return -1;
      if (!a.starred && b.starred) return 1;
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });
  }, [filteredNotes]);

  const unreadNotifications = notifications.filter(n => !n.read).length;

  // Mind Map Component
  const MindMapView = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
      if (!canvasRef.current) return;

      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const radius = 200;

      notes.forEach((note, index) => {
        const angle = (index / notes.length) * 2 * Math.PI;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);

        if (note.connections) {
          note.connections.forEach(connId => {
            const connNote = notes.find(n => n.id === connId);
            if (connNote) {
              const connIndex = notes.indexOf(connNote);
              const connAngle = (connIndex / notes.length) * 2 * Math.PI;
              const connX = centerX + radius * Math.cos(connAngle);
              const connY = centerY + radius * Math.sin(connAngle);

              ctx.beginPath();
              ctx.moveTo(x, y);
              ctx.lineTo(connX, connY);
              ctx.strokeStyle = darkMode ? '#4B5563' : '#D1D5DB';
              ctx.lineWidth = 2;
              ctx.stroke();
            }
          });
        }

        ctx.beginPath();
        ctx.arc(x, y, 30, 0, 2 * Math.PI);
        ctx.fillStyle = note.color;
        ctx.fill();
        ctx.strokeStyle = darkMode ? '#1F2937' : '#F3F4F6';
        ctx.lineWidth = 3;
        ctx.stroke();

        ctx.fillStyle = darkMode ? '#F3F4F6' : '#1F2937';
        ctx.font = '12px Inter';
        ctx.textAlign = 'center';
        ctx.fillText(note.title.substring(0, 15) + '...', x, y + 50);
      });
    }, [notes, darkMode]);

    return (
      <div className="relative h-full">
        <canvas
          ref={canvasRef}
          className="w-full h-full cursor-move"
          onClick={(e) => {
            if (!canvasRef.current) return;
            const rect = canvasRef.current.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const radius = 200;

            notes.forEach((note, index) => {
              const angle = (index / notes.length) * 2 * Math.PI;
              const noteX = centerX + radius * Math.cos(angle);
              const noteY = centerY + radius * Math.sin(angle);

              const distance = Math.sqrt(Math.pow(x - noteX, 2) + Math.pow(y - noteY, 2));
              if (distance < 30) {
                handleEditNote(note);
              }
            });
          }}
        />
        <div className="absolute top-4 left-4 space-y-2">
          <div className={`px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800/80' : 'bg-white/80'} backdrop-blur`}>
            <h3 className="font-semibold">Mind Map</h3>
            <p className="text-sm text-gray-500">Clique em uma nota para editar</p>
          </div>
        </div>
      </div>
    );
  };

  // Main render
  return (
    <div className={`min-h-screen transition-all duration-500 ${darkMode ? 'bg-gray-950 text-white' : 'bg-gray-50 text-gray-900'} ${focusMode ? 'focus-mode' : ''}`}>
      {/* Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 backdrop-blur-xl ${darkMode ? 'bg-gray-900/80' : 'bg-white/80'} border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} ${focusMode ? 'opacity-0 hover:opacity-100 transition-opacity duration-300' : ''}`}>
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <Menu className="w-5 h-5" />
            </button>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
              NoteFlow
              <span className="text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-500 to-red-500 text-white">
                🔥 {dailyStreak} days
              </span>
            </h1>
          </div>

          <div className="flex items-center gap-4">
            {activeUsers.length > 0 && (
              <div className="flex items-center -space-x-2">
                {activeUsers.map((user) => (
                  <div
                    key={user.id}
                    className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-semibold border-2 border-gray-900"
                    title={user.name}
                  >
                    {user.avatar}
                  </div>
                ))}
                <div className="ml-2 text-xs text-gray-500">
                  {activeUsers.length} online
                </div>
              </div>
            )}

            <div className="flex items-center gap-2">
              <button
                onClick={() => setFocusMode(!focusMode)}
                className={`p-2 rounded-lg transition-all hover:scale-110 ${focusMode ? 'bg-purple-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                title="Focus Mode"
              >
                <Target className="w-5 h-5" />
              </button>

              <button
                onClick={() => {
                  setPomodoroActive(!pomodoroActive);
                  if (!pomodoroActive) setPomodoroTime(25 * 60);
                }}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all ${pomodoroActive ? 'bg-red-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              >
                <Timer className="w-4 h-4" />
                <span className="text-sm font-mono">{formatTime(pomodoroTime)}</span>
              </button>
            </div>

            <div className={`flex items-center gap-2 px-4 py-2 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} transition-all duration-300 hover:shadow-lg`}>
              <Search className="w-4 h-4 text-gray-500" />
              <input
                type="text"
                placeholder="Buscar notas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`bg-transparent outline-none w-64 ${darkMode ? 'placeholder-gray-500' : 'placeholder-gray-400'}`}
              />
              <kbd className="text-xs px-2 py-1 rounded bg-gray-700 text-gray-300">⌘K</kbd>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className={`relative p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              >
                <Bell className="w-5 h-5" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {unreadNotifications}
                  </span>
                )}
              </button>

              <button
                onClick={() => setShowQuickCapture(true)}
                className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                title="Quick Capture (⌘⇧L)"
              >
                <Zap className="w-5 h-5" />
              </button>

              <button
                onClick={() => setShowMindMap(!showMindMap)}
                className={`p-2 rounded-lg transition-all hover:scale-110 ${showMindMap ? 'bg-purple-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                title="Mind Map"
              >
                <GitBranch className="w-5 h-5" />
              </button>

              <button
                onClick={() => setShowAnalytics(!showAnalytics)}
                className={`p-2 rounded-lg transition-all hover:scale-110 ${showAnalytics ? 'bg-blue-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                title="Analytics"
              >
                <BarChart3 className="w-5 h-5" />
              </button>

              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              >
                {viewMode === 'grid' ? <Grid className="w-5 h-5" /> : <List className="w-5 h-5" />}
              </button>

              <button
                onClick={() => setDarkMode(!darkMode)}
                className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              >
                {darkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
              </button>

              <button
                onClick={handleNewNote}
                className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all hover:scale-105"
              >
                <Plus className="w-4 h-4" />
                Nova Nota
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Notifications Dropdown */}
      {showNotifications && (
        <div className={`fixed top-20 right-6 w-96 ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-xl shadow-2xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} z-50`}>
          <div className="p-4 border-b border-gray-800">
            <h3 className="font-semibold flex items-center justify-between">
              Notificações
              <button
                onClick={() => setNotifications(notifications.map(n => ({ ...n, read: true })))}
                className="text-sm text-blue-500 hover:underline"
              >
                Marcar todas como lidas
              </button>
            </h3>
          </div>
          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <p className="p-4 text-center text-gray-500">Nenhuma notificação</p>
            ) : (
              notifications.map(notif => (
                <div
                  key={notif.id}
                  className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} ${!notif.read ? 'bg-blue-500/10' : ''} hover:bg-gray-800/50 transition-colors cursor-pointer`}
                  onClick={() => setNotifications(notifications.map(n => n.id === notif.id ? { ...n, read: true } : n))}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${notif.type === 'comment' ? 'bg-blue-500/20' : notif.type === 'share' ? 'bg-green-500/20' : 'bg-orange-500/20'}`}>
                      {notif.type === 'comment' ? <MessageSquare className="w-4 h-4" /> :
                       notif.type === 'share' ? <Share2 className="w-4 h-4" /> :
                       <Timer className="w-4 h-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-semibold">{notif.user}</span> {notif.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">{notif.time}</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex pt-20">
        {/* Sidebar */}
        <aside className={`fixed left-0 top-20 h-[calc(100vh-5rem)] w-64 ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border-r ${darkMode ? 'border-gray-800' : 'border-gray-200'} transition-all duration-300 ${showSidebar ? 'translate-x-0' : '-translate-x-full'} z-40 ${focusMode ? 'opacity-0 hover:opacity-100' : ''}`}>
          <div className="p-6 space-y-6">
            <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800/50' : 'bg-gray-100/50'} space-y-2`}>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Total de Notas</span>
                <span className="font-bold">{notes.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Colaboradores</span>
                <span className="font-bold">{collaborators.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Palavras Escritas</span>
                <span className="font-bold">{totalWords.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Streak</span>
                <span className="font-bold">🔥 {dailyStreak} dias</span>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-500 mb-3 flex items-center justify-between">
                TEMPLATES
                <button
                  onClick={() => setShowTemplates(!showTemplates)}
                  className={`text-xs px-2 py-1 rounded ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}
                >
                  Ver todos
                </button>
              </h3>
              <div className="space-y-2">
                {TEMPLATES.slice(0, 3).map((template: Template) => {
                  const Icon = template.icon;
                  return (
                    <button
                      key={template.id}
                      onClick={() => handleUseTemplate(template)}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                    >
                      <Icon className="w-4 h-4" />
                      {template.name}
                    </button>
                  );
                })}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-500 mb-3">CATEGORIAS</h3>
              <div className="space-y-2">
                <button
                  onClick={() => setFilterCategory('')}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all ${!filterCategory ? 'bg-blue-500/20 text-blue-500' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                >
                  Todas
                </button>
                {CATEGORIES.map((cat: string) => (
                  <button
                    key={cat}
                    onClick={() => setFilterCategory(cat)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-all capitalize ${filterCategory === cat ? 'bg-blue-500/20 text-blue-500' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                  >
                    {cat === 'compartilhada' && <Users className="w-4 h-4 inline mr-2" />}
                    {cat}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-500 mb-3">TAGS</h3>
              <div className="flex flex-wrap gap-2">
                {allTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => setFilterTag(filterTag === tag ? '' : tag)}
                    className={`px-3 py-1 rounded-full text-sm transition-all ${filterTag === tag ? 'bg-blue-500 text-white' : darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-200 hover:bg-gray-300'}`}
                  >
                    #{tag}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <button className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                <Star className="w-4 h-4" />
                Favoritas
              </button>
              <button className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                <Users className="w-4 h-4" />
                Compartilhadas
              </button>
              <button className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                <Globe className="w-4 h-4" />
                Públicas
              </button>
              <button className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                <Brain className="w-4 h-4" />
                Mind Maps
              </button>
              <button className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                <Headphones className="w-4 h-4" />
                Audio Notes
              </button>
              <button className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                <Archive className="w-4 h-4" />
                Arquivadas
              </button>
            </div>
          </div>
        </aside>

        {/* Main Content Area */}
        <main className={`flex-1 ${showSidebar ? 'ml-64' : 'ml-0'} transition-all duration-300 p-6`}>
          {showAnalytics ? (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold">📊 Analytics & Insights</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm text-gray-500">Daily Streak</h3>
                    <TrendingUp className="w-4 h-4 text-green-500" />
                  </div>
                  <p className="text-3xl font-bold">🔥 {dailyStreak}</p>
                  <p className="text-sm text-gray-500 mt-1">dias consecutivos</p>
                </div>

                <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm text-gray-500">Total Words</h3>
                    <Edit3 className="w-4 h-4 text-blue-500" />
                  </div>
                  <p className="text-3xl font-bold">{totalWords.toLocaleString()}</p>
                  <p className="text-sm text-gray-500 mt-1">palavras escritas</p>
                </div>

                <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm text-gray-500">Colaborações</h3>
                    <Users className="w-4 h-4 text-purple-500" />
                  </div>
                  <p className="text-3xl font-bold">{notes.filter(n => n.collaborators && n.collaborators.length > 0).length}</p>
                  <p className="text-sm text-gray-500 mt-1">notas compartilhadas</p>
                </div>

                <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm text-gray-500">Focus Time</h3>
                    <Clock className="w-4 h-4 text-purple-500" />
                  </div>
                  <p className="text-3xl font-bold">3.5h</p>
                  <p className="text-sm text-gray-500 mt-1">hoje</p>
                </div>
              </div>

              <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                <h3 className="text-lg font-semibold mb-4">Mood Tracker - Últimos 7 dias</h3>
                <div className="flex items-center justify-between">
                  {[...Array(7)].map((_, i) => (
                    <div key={i} className="text-center">
                      <div className="text-2xl mb-1">{MOODS[Math.floor(Math.random() * MOODS.length)].emoji}</div>
                      <div className="text-xs text-gray-500">{new Date(Date.now() - i * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR', { weekday: 'short' })}</div>
                    </div>
                  ))}
                </div>
              </div>

              <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                <h3 className="text-lg font-semibold mb-4">Estatísticas de Colaboração</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-3xl font-bold text-purple-500">{collaborators.length}</p>
                    <p className="text-sm text-gray-500">Colaboradores Ativos</p>
                  </div>
                  <div className="text-center">
                    <p className="text-3xl font-bold text-blue-500">{notes.reduce((acc, note) => acc + (note.comments ? note.comments.length : 0), 0)}</p>
                    <p className="text-sm text-gray-500">Comentários Total</p>
                  </div>
                  <div className="text-center">
                    <p className="text-3xl font-bold text-green-500">{notes.filter(n => n.isPublic).length}</p>
                    <p className="text-sm text-gray-500">Notas Públicas</p>
                  </div>
                </div>
              </div>

              <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                <h3 className="text-lg font-semibold mb-4">Hábitos de Escrita</h3>
                <div className="space-y-3">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Melhor horário: 09:00 - 11:00</span>
                      <span className="text-sm font-bold">82% produtividade</span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-green-500 to-blue-500" style={{ width: '82%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Categoria mais usada: Ideias</span>
                      <span className="text-sm font-bold">45%</span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-purple-500 to-pink-500" style={{ width: '45%' }} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : showMindMap ? (
            <div className="h-[calc(100vh-8rem)]">
              <MindMapView />
            </div>
          ) : (
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-4'}>
              {sortedNotes.map(note => (
                <div
                  key={note.id}
                  onClick={() => handleEditNote(note)}
                  className={`group relative ${viewMode === 'grid' ? 'h-64' : 'h-24'} ${darkMode ? 'bg-gray-900/50' : 'bg-white/50'} backdrop-blur-xl rounded-xl border ${darkMode ? 'border-gray-800' : 'border-gray-200'} p-6 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-2xl overflow-hidden`}
                  style={{
                    borderTop: `4px solid ${note.color}`
                  }}
                >
                  <div
                    className="absolute inset-0 opacity-10"
                    style={{
                      background: `radial-gradient(circle at top right, ${note.color}, transparent)`
                    }}
                  />

                  <div className="absolute top-4 right-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    {note.pinned && <Pin className="w-4 h-4 text-blue-500" />}
                    {note.starred && <Star className="w-4 h-4 text-yellow-500 fill-yellow-500" />}
                    {note.locked && <Lock className="w-4 h-4 text-red-500" />}
                    {note.audioNote && <Mic className="w-4 h-4 text-green-500" />}
                    {note.collaborators && note.collaborators.length > 0 && <Users className="w-4 h-4 text-purple-500" />}
                    {note.isPublic && <Globe className="w-4 h-4 text-blue-500" />}
                  </div>

                  {note.mood && note.mood !== 'neutral' && (
                    <div className="absolute top-4 left-4">
                      <span className="text-2xl">{MOODS.find((m: Mood) => m.name === note.mood)?.emoji}</span>
                    </div>
                  )}

                  <div className="relative">
                    <h3 className={`font-bold ${viewMode === 'grid' ? 'text-lg mb-2' : 'text-base'} line-clamp-2 ${note.mood && note.mood !== 'neutral' ? 'mt-8' : ''}`}>
                      {note.title}
                    </h3>

                    {viewMode === 'grid' && (
                      <>
                        <div
                          className="text-sm text-gray-500 line-clamp-4 mb-4"
                          dangerouslySetInnerHTML={{ __html: note.content }}
                        />

                        <div className="flex items-center gap-3 text-xs text-gray-500 mb-2">
                          <span className="flex items-center gap-1">
                            <BookOpen className="w-3 h-3" />
                            {note.wordCount || 0} palavras
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {note.readTime || 1} min
                          </span>
                          {note.comments && note.comments.length > 0 && (
                            <span className="flex items-center gap-1">
                              <MessageSquare className="w-3 h-3" />
                              {note.comments.length}
                            </span>
                          )}
                        </div>
                      </>
                    )}

                    <div className={`flex flex-wrap gap-2 ${viewMode === 'grid' ? 'absolute bottom-6 left-6 right-6' : ''}`}>
                      {note.tags.map(tag => (
                        <span key={tag} className={`text-xs px-2 py-1 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className={`absolute ${viewMode === 'grid' ? 'bottom-4 right-4' : 'top-4 right-4'} flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity`}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleStar(note.id);
                      }}
                      className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                    >
                      <Star className={`w-4 h-4 ${note.starred ? 'fill-yellow-500 text-yellow-500' : ''}`} />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedNote(note);
                        setShowShareModal(true);
                      }}
                      className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                    >
                      <Share2 className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTogglePublic(note.id);
                      }}
                      className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                    >
                      {note.isPublic ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteNote(note.id);
                      }}
                      className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </main>
      </div>

      {/* Quick Capture Modal */}
      {showQuickCapture && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-6">
          <div className={`w-full max-w-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Zap className="w-5 h-5 text-yellow-500" />
                  Quick Capture
                </h3>
                <button
                  onClick={() => setShowQuickCapture(false)}
                  className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {TEMPLATES.map((template: Template) => {
                  const Icon = template.icon;
                  return (
                    <button
                      key={template.id}
                      onClick={() => handleUseTemplate(template)}
                      className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/50 hover:bg-gray-800' : 'bg-gray-100/50 hover:bg-gray-100'} transition-all text-left space-y-3`}
                    >
                      <Icon className="w-8 h-8 text-blue-500" />
                      <h3 className="font-semibold">{template.name}</h3>
                      <p className="text-sm text-gray-500">Template pronto para {template.id}</p>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Command Palette */}
      {showCommandPalette && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-6">
          <div className={`w-full max-w-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}>
            <div className="p-6">
              <div className={`flex items-center gap-2 px-4 py-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                <Command className="w-5 h-5 text-gray-500" />
                <input
                  type="text"
                  placeholder="Digite um comando..."
                  className="flex-1 bg-transparent outline-none"
                  autoFocus
                />
              </div>

              <div className="mt-4 space-y-2">
                <button className={`w-full text-left px-4 py-3 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                  <div className="flex items-center gap-3">
                    <Plus className="w-4 h-4" />
                    <span>Nova Nota</span>
                    <kbd className="ml-auto text-xs px-2 py-1 rounded bg-gray-700 text-gray-300">⌘N</kbd>
                  </div>
                </button>
                <button className={`w-full text-left px-4 py-3 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                  <div className="flex items-center gap-3">
                    <Search className="w-4 h-4" />
                    <span>Buscar</span>
                    <kbd className="ml-auto text-xs px-2 py-1 rounded bg-gray-700 text-gray-300">⌘K</kbd>
                  </div>
                </button>
                <button className={`w-full text-left px-4 py-3 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                  <div className="flex items-center gap-3">
                    <Zap className="w-4 h-4" />
                    <span>Quick Capture</span>
                    <kbd className="ml-auto text-xs px-2 py-1 rounded bg-gray-700 text-gray-300">⌘⇧L</kbd>
                  </div>
                </button>
                <button className={`w-full text-left px-4 py-3 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                  <div className="flex items-center gap-3">
                    <Target className="w-4 h-4" />
                    <span>Focus Mode</span>
                  </div>
                </button>
                <button className={`w-full text-left px-4 py-3 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                  <div className="flex items-center gap-3">
                    <Timer className="w-4 h-4" />
                    <span>Iniciar Pomodoro</span>
                  </div>
                </button>
                <button className={`w-full text-left px-4 py-3 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                  <div className="flex items-center gap-3">
                    <UserPlus className="w-4 h-4" />
                    <span>Compartilhar Nota</span>
                  </div>
                </button>
                <button className={`w-full text-left px-4 py-3 rounded-lg transition-all ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}>
                  <div className="flex items-center gap-3">
                    <BarChart3 className="w-4 h-4" />
                    <span>Analytics</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default NotesApp;