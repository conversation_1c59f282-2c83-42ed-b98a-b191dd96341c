import React, { useState } from 'react';
import { Toaster } from 'react-hot-toast';

// Import components
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import NoteCard from './components/NoteCard';
import MindMap from './components/MindMap';
import { QuickCaptureModal, CommandPaletteModal, TemplatesModal } from './components/Modals';
import { AuthModal } from './components/AuthModal';

// Import hooks and utilities
import { useAuth } from './src/hooks/useAuth';
import { useNotesFirebase } from './src/hooks/useNotesFirebase';
import { formatTime, copyNoteToClipboard, exportNote } from './utils';

// Import Firebase config
import './config/firebase';

const NotesApp = () => {
  const { user, loading: authLoading } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Use custom hook for state management
  const {
    // Core states
    darkMode,
    showSidebar,
    showMindMap,
    showQuickCapture,
    showCommandPalette,
    showTemplates,
    viewMode,
    searchTerm,
    filterTag,
    filterCategory,
    pomodoroActive,
    pomodoroTime,
    focusMode,
    showAnalytics,
    dailyStreak,
    totalWords,
    notifications,
    showNotifications,
    copiedId,
    quickCaptureText,

    // Data
    sortedNotes,
    allTags,

    // Setters
    setDarkMode,
    setShowSidebar,
    setShowMindMap,
    setShowQuickCapture,
    setShowCommandPalette,
    setShowTemplates,
    setViewMode,
    setSearchTerm,
    setFilterTag,
    setFilterCategory,
    setPomodoroActive,
    setPomodoroTime,
    setFocusMode,
    setShowAnalytics,
    setShowNotifications,
    setQuickCaptureText,
    setCopiedId,

    // Handlers
    handleNewNote,
    handleUseTemplate,
    handleEditNote,
    handleDeleteNote,
    handleToggleStar,
    handleTogglePin,
    handleToggleLock,
    handleTogglePublic,
    handleQuickCapture
  } = useNotesFirebase();

  // Show auth modal if user is not logged in
  React.useEffect(() => {
    if (!authLoading && !user) {
      setShowAuthModal(true);
    }
  }, [user, authLoading]);

  // Show loading screen while checking auth
  if (authLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl font-bold text-white">N</span>
          </div>
          <h1 className="text-2xl font-bold mb-2">NoteFlow</h1>
          <p className="text-gray-500">Carregando...</p>
        </div>
      </div>
    );
  }

  const handleCopyNote = (note: any) => {
    copyNoteToClipboard(note);
    setCopiedId(note.id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  const handleExportNote = (note: any) => {
    exportNote(note);
  };

  const handleShareNote = (note: any) => {
    // Implementar compartilhamento
    console.log('Compartilhar nota:', note);
  };

  return (
    <div className={`min-h-screen transition-all duration-300 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <Header
        darkMode={darkMode}
        setDarkMode={setDarkMode}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        viewMode={viewMode}
        setViewMode={setViewMode}
        showSidebar={showSidebar}
        setShowSidebar={setShowSidebar}
        setShowCommandPalette={setShowCommandPalette}
        handleNewNote={handleNewNote}
        pomodoroActive={pomodoroActive}
        pomodoroTime={pomodoroTime}
        formatTime={formatTime}
        setPomodoroActive={setPomodoroActive}
        setPomodoroTime={setPomodoroTime}
        focusMode={focusMode}
        setFocusMode={setFocusMode}
        setShowAnalytics={setShowAnalytics}
        setShowMindMap={setShowMindMap}
        setShowQuickCapture={setShowQuickCapture}
        notifications={notifications}
        setShowNotifications={setShowNotifications}
        showNotifications={showNotifications}
        dailyStreak={dailyStreak}
        totalWords={totalWords}
      />

      <div className="flex">
        {/* Sidebar */}
        <Sidebar
          darkMode={darkMode}
          showSidebar={showSidebar}
          setShowSidebar={setShowSidebar}
          filterCategory={filterCategory}
          setFilterCategory={setFilterCategory}
          filterTag={filterTag}
          setFilterTag={setFilterTag}
          notes={sortedNotes}
          allTags={allTags}
        />

        {/* Main Content */}
        <main className={`flex-1 transition-all duration-300 ${showSidebar ? 'ml-80' : 'ml-0'}`}>
          {showMindMap ? (
            <MindMap
              notes={sortedNotes}
              darkMode={darkMode}
              onNoteClick={handleEditNote}
            />
          ) : (
            <div className="p-6">
              {sortedNotes.length === 0 ? (
                <div className="text-center py-20">
                  <h2 className="text-2xl font-bold mb-4">Nenhuma nota encontrada</h2>
                  <p className="text-gray-500 mb-8">Comece criando sua primeira nota!</p>
                  <button
                    onClick={handleNewNote}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all hover:scale-105"
                  >
                    Criar Nova Nota
                  </button>
                </div>
              ) : (
                <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-4'}>
                  {sortedNotes.map((note: any) => (
                    <NoteCard
                      key={note.id}
                      note={note}
                      darkMode={darkMode}
                      viewMode={viewMode}
                      copiedId={copiedId}
                      onEdit={handleEditNote}
                      onDelete={handleDeleteNote}
                      onToggleStar={handleToggleStar}
                      onTogglePin={handleTogglePin}
                      onToggleLock={handleToggleLock}
                      onTogglePublic={handleTogglePublic}
                      onCopy={handleCopyNote}
                      onExport={handleExportNote}
                      onShare={handleShareNote}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </main>
      </div>

      {/* Modals */}
      <QuickCaptureModal
        show={showQuickCapture}
        darkMode={darkMode}
        quickCaptureText={quickCaptureText}
        setQuickCaptureText={setQuickCaptureText}
        onClose={() => setShowQuickCapture(false)}
        onSave={handleQuickCapture}
      />

      <CommandPaletteModal
        show={showCommandPalette}
        darkMode={darkMode}
        onClose={() => setShowCommandPalette(false)}
        onNewNote={handleNewNote}
        onQuickCapture={() => setShowQuickCapture(true)}
        onFocusMode={() => setFocusMode(!focusMode)}
        onPomodoro={() => setPomodoroActive(!pomodoroActive)}
        onAnalytics={() => setShowAnalytics(true)}
        onMindMap={() => setShowMindMap(!showMindMap)}
      />

      <TemplatesModal
        show={showTemplates}
        darkMode={darkMode}
        onClose={() => setShowTemplates(false)}
        onUseTemplate={handleUseTemplate}
      />

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        darkMode={darkMode}
      />

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: darkMode ? '#374151' : '#ffffff',
            color: darkMode ? '#ffffff' : '#000000',
            border: darkMode ? '1px solid #4B5563' : '1px solid #E5E7EB',
          },
        }}
      />
    </div>
  );
};

export default NotesApp;
