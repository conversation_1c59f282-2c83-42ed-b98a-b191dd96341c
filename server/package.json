{"name": "noteflow-server", "version": "1.0.0", "description": "Backend API for NoteFlow application", "type": "module", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step needed for Node.js'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "format": "prettier --write src/"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "prettier": "^3.1.0"}, "keywords": ["notes", "api", "backend", "express", "nodejs"], "author": "NoteFlow Team", "license": "MIT"}