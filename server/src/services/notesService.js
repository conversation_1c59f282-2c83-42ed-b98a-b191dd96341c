import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

export class NotesService {
  constructor() {
    this.dataDir = path.join(__dirname, '../../data')
    this.notesFile = path.join(this.dataDir, 'notes.json')
    this.versionsFile = path.join(this.dataDir, 'versions.json')
    this.init()
  }

  async init() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true })
      
      // Initialize files if they don't exist
      try {
        await fs.access(this.notesFile)
      } catch {
        await fs.writeFile(this.notesFile, JSON.stringify([]))
      }

      try {
        await fs.access(this.versionsFile)
      } catch {
        await fs.writeFile(this.versionsFile, JSON.stringify([]))
      }
    } catch (error) {
      console.error('Failed to initialize NotesService:', error)
    }
  }

  async loadNotes() {
    try {
      const data = await fs.readFile(this.notesFile, 'utf8')
      return JSON.parse(data)
    } catch (error) {
      console.error('Failed to load notes:', error)
      return []
    }
  }

  async saveNotes(notes) {
    try {
      await fs.writeFile(this.notesFile, JSON.stringify(notes, null, 2))
    } catch (error) {
      console.error('Failed to save notes:', error)
      throw error
    }
  }

  async loadVersions() {
    try {
      const data = await fs.readFile(this.versionsFile, 'utf8')
      return JSON.parse(data)
    } catch (error) {
      console.error('Failed to load versions:', error)
      return []
    }
  }

  async saveVersions(versions) {
    try {
      await fs.writeFile(this.versionsFile, JSON.stringify(versions, null, 2))
    } catch (error) {
      console.error('Failed to save versions:', error)
      throw error
    }
  }

  async getUserNotes(userId, filters = {}) {
    const notes = await this.loadNotes()
    let userNotes = notes.filter(note => note.userId === userId)

    // Apply filters
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      userNotes = userNotes.filter(note => 
        note.title.toLowerCase().includes(searchTerm) ||
        note.content.toLowerCase().includes(searchTerm) ||
        note.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    if (filters.category) {
      userNotes = userNotes.filter(note => note.category === filters.category)
    }

    if (filters.tag) {
      userNotes = userNotes.filter(note => note.tags?.includes(filters.tag))
    }

    // Sort by pinned, starred, then by updatedAt
    userNotes.sort((a, b) => {
      if (a.pinned && !b.pinned) return -1
      if (!a.pinned && b.pinned) return 1
      if (a.starred && !b.starred) return -1
      if (!a.starred && b.starred) return 1
      return new Date(b.updatedAt) - new Date(a.updatedAt)
    })

    // Apply pagination
    const start = filters.offset || 0
    const end = start + (filters.limit || userNotes.length)
    
    return userNotes.slice(start, end)
  }

  async getNoteById(noteId, userId) {
    const notes = await this.loadNotes()
    return notes.find(note => note.id === noteId && note.userId === userId)
  }

  async createNote(noteData) {
    const notes = await this.loadNotes()
    
    // Calculate stats
    const wordCount = this.calculateWordCount(noteData.content)
    const readTime = Math.ceil(wordCount / 200)

    const note = {
      ...noteData,
      wordCount,
      readTime,
      starred: false,
      pinned: false,
      locked: false,
      isPublic: false,
      collaborators: [],
      comments: [],
      connections: []
    }

    notes.unshift(note) // Add to beginning
    await this.saveNotes(notes)

    // Save initial version
    await this.saveVersion(note)

    return note
  }

  async updateNote(noteId, updateData, userId) {
    const notes = await this.loadNotes()
    const noteIndex = notes.findIndex(note => note.id === noteId && note.userId === userId)
    
    if (noteIndex === -1) {
      return null
    }

    const existingNote = notes[noteIndex]
    
    // Calculate stats if content changed
    if (updateData.content !== undefined) {
      updateData.wordCount = this.calculateWordCount(updateData.content)
      updateData.readTime = Math.ceil(updateData.wordCount / 200)
    }

    const updatedNote = {
      ...existingNote,
      ...updateData
    }

    notes[noteIndex] = updatedNote
    await this.saveNotes(notes)

    // Save version if content changed
    if (updateData.content !== undefined || updateData.title !== undefined) {
      await this.saveVersion(updatedNote)
    }

    return updatedNote
  }

  async deleteNote(noteId, userId) {
    const notes = await this.loadNotes()
    const noteIndex = notes.findIndex(note => note.id === noteId && note.userId === userId)
    
    if (noteIndex === -1) {
      return false
    }

    notes.splice(noteIndex, 1)
    await this.saveNotes(notes)
    
    return true
  }

  async toggleStar(noteId, userId) {
    const notes = await this.loadNotes()
    const note = notes.find(note => note.id === noteId && note.userId === userId)
    
    if (!note) {
      return null
    }

    note.starred = !note.starred
    note.updatedAt = new Date().toISOString()
    
    await this.saveNotes(notes)
    return note
  }

  async togglePin(noteId, userId) {
    const notes = await this.loadNotes()
    const note = notes.find(note => note.id === noteId && note.userId === userId)
    
    if (!note) {
      return null
    }

    note.pinned = !note.pinned
    note.updatedAt = new Date().toISOString()
    
    await this.saveNotes(notes)
    return note
  }

  async saveVersion(note) {
    const versions = await this.loadVersions()
    
    const version = {
      id: `${note.id}_v${note.version}`,
      noteId: note.id,
      version: note.version,
      title: note.title,
      content: note.content,
      createdAt: new Date().toISOString()
    }

    versions.unshift(version)
    
    // Keep only last 10 versions per note
    const noteVersions = versions.filter(v => v.noteId === note.id)
    if (noteVersions.length > 10) {
      const versionsToRemove = noteVersions.slice(10)
      versionsToRemove.forEach(v => {
        const index = versions.findIndex(version => version.id === v.id)
        if (index > -1) versions.splice(index, 1)
      })
    }

    await this.saveVersions(versions)
  }

  async getNoteVersions(noteId, userId) {
    // First check if user has access to the note
    const note = await this.getNoteById(noteId, userId)
    if (!note) {
      return []
    }

    const versions = await this.loadVersions()
    return versions
      .filter(version => version.noteId === noteId)
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  }

  calculateWordCount(content) {
    // Strip HTML tags and count words
    const text = content.replace(/<[^>]*>/g, '').trim()
    return text ? text.split(/\s+/).length : 0
  }

  async getAnalytics(userId) {
    const notes = await this.loadNotes()
    const userNotes = notes.filter(note => note.userId === userId)

    const totalNotes = userNotes.length
    const totalWords = userNotes.reduce((sum, note) => sum + (note.wordCount || 0), 0)
    const starredNotes = userNotes.filter(note => note.starred).length
    const publicNotes = userNotes.filter(note => note.isPublic).length
    
    const categoryCounts = userNotes.reduce((acc, note) => {
      acc[note.category] = (acc[note.category] || 0) + 1
      return acc
    }, {})

    const tagCounts = userNotes.reduce((acc, note) => {
      note.tags?.forEach(tag => {
        acc[tag] = (acc[tag] || 0) + 1
      })
      return acc
    }, {})

    return {
      totalNotes,
      totalWords,
      starredNotes,
      publicNotes,
      categoryCounts,
      tagCounts,
      averageWordsPerNote: totalNotes > 0 ? Math.round(totalWords / totalNotes) : 0
    }
  }
}
