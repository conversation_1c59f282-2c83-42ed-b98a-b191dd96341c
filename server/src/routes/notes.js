import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { v4 as uuidv4 } from 'uuid'
import { NotesService } from '../services/notesService.js'
import { AIService } from '../services/aiService.js'

const router = express.Router()
const notesService = new NotesService()
const aiService = new AIService()

// Validation middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    })
  }
  next()
}

// GET /api/notes - Get all notes for user
router.get('/', [
  query('search').optional().isString().trim(),
  query('category').optional().isString().trim(),
  query('tag').optional().isString().trim(),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 }),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const { search, category, tag, limit = 50, offset = 0 } = req.query

    const notes = await notesService.getUserNotes(userId, {
      search,
      category,
      tag,
      limit: parseInt(limit),
      offset: parseInt(offset)
    })

    res.json({
      notes,
      total: notes.length,
      limit: parseInt(limit),
      offset: parseInt(offset)
    })
  } catch (error) {
    next(error)
  }
})

// GET /api/notes/:id - Get specific note
router.get('/:id', [
  param('id').isUUID(),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const noteId = req.params.id

    const note = await notesService.getNoteById(noteId, userId)
    
    if (!note) {
      return res.status(404).json({
        error: 'Note not found'
      })
    }

    res.json(note)
  } catch (error) {
    next(error)
  }
})

// POST /api/notes - Create new note
router.post('/', [
  body('title').isString().trim().isLength({ min: 1, max: 200 }),
  body('content').isString().trim(),
  body('category').optional().isString().trim(),
  body('tags').optional().isArray(),
  body('color').optional().isString().matches(/^#[0-9A-F]{6}$/i),
  body('mood').optional().isIn(['happy', 'sad', 'excited', 'calm', 'stressed', 'neutral']),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const noteData = {
      id: uuidv4(),
      ...req.body,
      userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: 1
    }

    // Generate AI suggestions for tags
    if (noteData.content && !noteData.tags?.length) {
      const suggestedTags = await aiService.generateTagSuggestions(noteData.content)
      noteData.suggestedTags = suggestedTags
    }

    const note = await notesService.createNote(noteData)

    res.status(201).json(note)
  } catch (error) {
    next(error)
  }
})

// PUT /api/notes/:id - Update note
router.put('/:id', [
  param('id').isUUID(),
  body('title').optional().isString().trim().isLength({ min: 1, max: 200 }),
  body('content').optional().isString().trim(),
  body('category').optional().isString().trim(),
  body('tags').optional().isArray(),
  body('color').optional().isString().matches(/^#[0-9A-F]{6}$/i),
  body('mood').optional().isIn(['happy', 'sad', 'excited', 'calm', 'stressed', 'neutral']),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const noteId = req.params.id

    const existingNote = await notesService.getNoteById(noteId, userId)
    if (!existingNote) {
      return res.status(404).json({
        error: 'Note not found'
      })
    }

    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString(),
      version: existingNote.version + 1
    }

    const updatedNote = await notesService.updateNote(noteId, updateData, userId)

    res.json(updatedNote)
  } catch (error) {
    next(error)
  }
})

// DELETE /api/notes/:id - Delete note
router.delete('/:id', [
  param('id').isUUID(),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const noteId = req.params.id

    const deleted = await notesService.deleteNote(noteId, userId)
    
    if (!deleted) {
      return res.status(404).json({
        error: 'Note not found'
      })
    }

    res.status(204).send()
  } catch (error) {
    next(error)
  }
})

// POST /api/notes/:id/star - Toggle star status
router.post('/:id/star', [
  param('id').isUUID(),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const noteId = req.params.id

    const note = await notesService.toggleStar(noteId, userId)
    
    if (!note) {
      return res.status(404).json({
        error: 'Note not found'
      })
    }

    res.json(note)
  } catch (error) {
    next(error)
  }
})

// POST /api/notes/:id/pin - Toggle pin status
router.post('/:id/pin', [
  param('id').isUUID(),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const noteId = req.params.id

    const note = await notesService.togglePin(noteId, userId)
    
    if (!note) {
      return res.status(404).json({
        error: 'Note not found'
      })
    }

    res.json(note)
  } catch (error) {
    next(error)
  }
})

// GET /api/notes/:id/versions - Get note version history
router.get('/:id/versions', [
  param('id').isUUID(),
  validateRequest
], async (req, res, next) => {
  try {
    const userId = req.user.id
    const noteId = req.params.id

    const versions = await notesService.getNoteVersions(noteId, userId)
    
    res.json(versions)
  } catch (error) {
    next(error)
  }
})

// POST /api/notes/ai/suggestions - Get AI suggestions
router.post('/ai/suggestions', [
  body('content').isString().trim().isLength({ min: 1 }),
  validateRequest
], async (req, res, next) => {
  try {
    const { content } = req.body

    const suggestions = await aiService.generateTagSuggestions(content)
    const summary = await aiService.generateSummary(content)
    const connections = await aiService.findConnections(content, req.user.id)

    res.json({
      tagSuggestions: suggestions,
      summary,
      connections
    })
  } catch (error) {
    next(error)
  }
})

export default router
