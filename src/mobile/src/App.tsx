import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Linking } from 'react-native'
import { NavigationContainer } from '@react-navigation/native'
import { Provider as PaperProvider } from 'react-native-paper'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import SplashScreen from 'react-native-splash-screen'
import NetInfo from '@react-native-community/netinfo'
import DeviceInfo from 'react-native-device-info'

// Navigation
import { AppNavigator } from './navigation/AppNavigator'

// Contexts
import { AuthProvider } from './contexts/AuthContext'
import { NotesProvider } from './contexts/NotesContext'
import { ThemeProvider } from './contexts/ThemeContext'
import { OfflineProvider } from './contexts/OfflineContext'

// Services
import { NotificationService } from './services/NotificationService'
import { BiometricService } from './services/BiometricService'
import { SyncService } from './services/SyncService'

// Themes
import { lightTheme, darkTheme } from './themes'

// Types
import { AppState } from './types'

const App: React.FC = () => {
  const [isReady, setIsReady] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(true)
  const [isConnected, setIsConnected] = useState(true)
  const [appState, setAppState] = useState<AppState>('active')

  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    try {
      // Initialize services
      await NotificationService.initialize()
      await BiometricService.initialize()
      await SyncService.initialize()

      // Check device capabilities
      const hasNotch = await DeviceInfo.hasNotch()
      const isTablet = await DeviceInfo.isTablet()
      
      console.log('Device info:', { hasNotch, isTablet })

      // Setup network monitoring
      const unsubscribe = NetInfo.addEventListener(state => {
        setIsConnected(state.isConnected ?? false)
        if (state.isConnected) {
          SyncService.syncPendingChanges()
        }
      })

      // Setup deep linking
      const handleDeepLink = (url: string) => {
        console.log('Deep link received:', url)
        // Handle deep link navigation
      }

      const linkingListener = Linking.addEventListener('url', ({ url }) => {
        handleDeepLink(url)
      })

      // Check for initial URL
      const initialUrl = await Linking.getInitialURL()
      if (initialUrl) {
        handleDeepLink(initialUrl)
      }

      setIsReady(true)
      SplashScreen.hide()

      return () => {
        unsubscribe()
        linkingListener.remove()
      }
    } catch (error) {
      console.error('Failed to initialize app:', error)
      Alert.alert(
        'Erro de Inicialização',
        'Ocorreu um erro ao inicializar o aplicativo. Tente novamente.',
        [{ text: 'OK', onPress: () => setIsReady(true) }]
      )
    }
  }

  if (!isReady) {
    return null // Splash screen is showing
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider>
        <PaperProvider theme={isDarkMode ? darkTheme : lightTheme}>
          <AuthProvider>
            <NotesProvider>
              <OfflineProvider isConnected={isConnected}>
                <NavigationContainer
                  theme={isDarkMode ? darkTheme : lightTheme}
                  onStateChange={(state) => {
                    // Track navigation for analytics
                    console.log('Navigation state changed:', state)
                  }}
                >
                  <StatusBar
                    barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                    backgroundColor={isDarkMode ? '#0f172a' : '#ffffff'}
                    translucent={false}
                  />
                  <AppNavigator />
                </NavigationContainer>
              </OfflineProvider>
            </NotesProvider>
          </AuthProvider>
        </PaperProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  )
}

export default App
