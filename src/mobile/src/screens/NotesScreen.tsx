import React, { useState, useEffect, useCallback } from 'react'
import {
  View,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
  Platform
} from 'react-native'
import {
  Searchbar,
  FAB,
  Portal,
  Modal,
  Text,
  Card,
  Chip,
  IconButton,
  Menu,
  Divider
} from 'react-native-paper'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming
} from 'react-native-reanimated'
import HapticFeedback from 'react-native-haptic-feedback'

// Hooks
import { useNotes } from '../hooks/useNotes'
import { useTheme } from '../hooks/useTheme'
import { useOffline } from '../hooks/useOffline'

// Components
import { NoteCard } from '../components/NoteCard'
import { QuickCaptureModal } from '../components/QuickCaptureModal'
import { FilterModal } from '../components/FilterModal'
import { EmptyState } from '../components/EmptyState'
import { OfflineBanner } from '../components/OfflineBanner'

// Services
import { HapticService } from '../services/HapticService'
import { AnalyticsService } from '../services/AnalyticsService'

// Types
import { Note, ViewMode, SortOption } from '../types'

const { width } = Dimensions.get('window')

interface NotesScreenProps {
  navigation: any
}

export const NotesScreen: React.FC<NotesScreenProps> = ({ navigation }) => {
  const insets = useSafeAreaInsets()
  const { theme } = useTheme()
  const { isOffline } = useOffline()
  
  const {
    notes,
    loading,
    refreshing,
    searchTerm,
    setSearchTerm,
    filterCategory,
    filterTag,
    sortBy,
    viewMode,
    setViewMode,
    setSortBy,
    refreshNotes,
    deleteNote,
    toggleStar,
    togglePin
  } = useNotes()

  // Local state
  const [showQuickCapture, setShowQuickCapture] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [selectedNote, setSelectedNote] = useState<Note | null>(null)
  const [fabVisible, setFabVisible] = useState(true)

  // Animations
  const fabScale = useSharedValue(1)
  const searchBarOpacity = useSharedValue(1)

  useEffect(() => {
    // Track screen view
    AnalyticsService.trackScreenView('Notes')
  }, [])

  const handleNotePress = useCallback((note: Note) => {
    HapticService.impact('light')
    navigation.navigate('NoteEditor', { noteId: note.id })
  }, [navigation])

  const handleNoteLongPress = useCallback((note: Note) => {
    HapticService.impact('medium')
    setSelectedNote(note)
    // Show context menu or actions
  }, [])

  const handleDeleteNote = useCallback(async (noteId: string) => {
    HapticService.impact('heavy')
    
    Alert.alert(
      'Deletar Nota',
      'Tem certeza que deseja deletar esta nota?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Deletar',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteNote(noteId)
              AnalyticsService.trackEvent('note_deleted')
            } catch (error) {
              Alert.alert('Erro', 'Não foi possível deletar a nota')
            }
          }
        }
      ]
    )
  }, [deleteNote])

  const handleToggleStar = useCallback(async (noteId: string) => {
    HapticService.impact('light')
    try {
      await toggleStar(noteId)
      AnalyticsService.trackEvent('note_starred')
    } catch (error) {
      Alert.alert('Erro', 'Não foi possível favoritar a nota')
    }
  }, [toggleStar])

  const handleTogglePin = useCallback(async (noteId: string) => {
    HapticService.impact('light')
    try {
      await togglePin(noteId)
      AnalyticsService.trackEvent('note_pinned')
    } catch (error) {
      Alert.alert('Erro', 'Não foi possível fixar a nota')
    }
  }, [togglePin])

  const handleNewNote = useCallback(() => {
    HapticService.impact('medium')
    navigation.navigate('NoteEditor', { noteId: null })
    AnalyticsService.trackEvent('new_note_started')
  }, [navigation])

  const handleQuickCapture = useCallback(() => {
    HapticService.impact('light')
    setShowQuickCapture(true)
    AnalyticsService.trackEvent('quick_capture_opened')
  }, [])

  const handleScroll = useCallback((event: any) => {
    const { contentOffset, velocity } = event.nativeEvent
    
    if (velocity.y > 0 && fabVisible) {
      // Scrolling down - hide FAB
      setFabVisible(false)
      fabScale.value = withSpring(0.8)
    } else if (velocity.y < 0 && !fabVisible) {
      // Scrolling up - show FAB
      setFabVisible(true)
      fabScale.value = withSpring(1)
    }
  }, [fabVisible, fabScale])

  const fabAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: fabScale.value }],
    opacity: withTiming(fabVisible ? 1 : 0.7)
  }))

  const renderNote = useCallback(({ item, index }: { item: Note; index: number }) => (
    <NoteCard
      note={item}
      onPress={() => handleNotePress(item)}
      onLongPress={() => handleNoteLongPress(item)}
      onDelete={() => handleDeleteNote(item.id)}
      onToggleStar={() => handleToggleStar(item.id)}
      onTogglePin={() => handleTogglePin(item.id)}
      viewMode={viewMode}
      index={index}
    />
  ), [viewMode, handleNotePress, handleNoteLongPress, handleDeleteNote, handleToggleStar, handleTogglePin])

  const renderHeader = () => (
    <View style={{ paddingHorizontal: 16, paddingBottom: 8 }}>
      <Searchbar
        placeholder="Buscar notas..."
        value={searchTerm}
        onChangeText={setSearchTerm}
        style={{ marginBottom: 12 }}
        inputStyle={{ fontSize: 16 }}
        iconColor={theme.colors.primary}
      />
      
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Chip
            icon="filter"
            onPress={() => setShowFilters(true)}
            style={{ marginRight: 8 }}
          >
            Filtros
          </Chip>
          
          {filterCategory && (
            <Chip
              icon="close"
              onPress={() => {/* clear category filter */}}
              style={{ marginRight: 8 }}
            >
              {filterCategory}
            </Chip>
          )}
          
          {filterTag && (
            <Chip
              icon="close"
              onPress={() => {/* clear tag filter */}}
              style={{ marginRight: 8 }}
            >
              #{filterTag}
            </Chip>
          )}
        </View>
        
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Menu
            visible={showSortMenu}
            onDismiss={() => setShowSortMenu(false)}
            anchor={
              <IconButton
                icon="sort"
                onPress={() => setShowSortMenu(true)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                setSortBy('updatedAt')
                setShowSortMenu(false)
              }}
              title="Última modificação"
              leadingIcon="clock"
            />
            <Menu.Item
              onPress={() => {
                setSortBy('createdAt')
                setShowSortMenu(false)
              }}
              title="Data de criação"
              leadingIcon="calendar"
            />
            <Menu.Item
              onPress={() => {
                setSortBy('title')
                setShowSortMenu(false)
              }}
              title="Título"
              leadingIcon="alphabetical"
            />
            <Divider />
            <Menu.Item
              onPress={() => {
                setViewMode(viewMode === 'grid' ? 'list' : 'grid')
                setShowSortMenu(false)
              }}
              title={viewMode === 'grid' ? 'Lista' : 'Grade'}
              leadingIcon={viewMode === 'grid' ? 'view-list' : 'view-grid'}
            />
          </Menu>
        </View>
      </View>
    </View>
  )

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {isOffline && <OfflineBanner />}
      
      <FlatList
        data={notes}
        renderItem={renderNote}
        keyExtractor={(item) => item.id}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={{
          paddingTop: 16,
          paddingBottom: insets.bottom + 80,
          paddingHorizontal: viewMode === 'grid' ? 8 : 0
        }}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="note-plus"
            title="Nenhuma nota encontrada"
            subtitle="Comece criando sua primeira nota!"
            actionText="Criar Nota"
            onAction={handleNewNote}
          />
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={refreshNotes}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        onScroll={handleScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      />

      <Portal>
        <Animated.View
          style={[
            {
              position: 'absolute',
              bottom: insets.bottom + 16,
              right: 16,
            },
            fabAnimatedStyle
          ]}
        >
          <FAB
            icon="plus"
            onPress={handleNewNote}
            onLongPress={handleQuickCapture}
            style={{
              backgroundColor: theme.colors.primary,
            }}
          />
        </Animated.View>
      </Portal>

      <QuickCaptureModal
        visible={showQuickCapture}
        onDismiss={() => setShowQuickCapture(false)}
        onSave={(content) => {
          // Handle quick capture save
          setShowQuickCapture(false)
        }}
      />

      <FilterModal
        visible={showFilters}
        onDismiss={() => setShowFilters(false)}
        currentCategory={filterCategory}
        currentTag={filterTag}
        onApplyFilters={(category, tag) => {
          // Apply filters
          setShowFilters(false)
        }}
      />
    </View>
  )
}
