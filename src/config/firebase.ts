import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import { getStorage } from 'firebase/storage'
import { getFunctions } from 'firebase/functions'
import { getAnalytics } from 'firebase/analytics'

// Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firebase services
export const auth = getAuth(app)
export const db = getFirestore(app)
export const storage = getStorage(app)
export const functions = getFunctions(app)

// Initialize Analytics (only in production)
export const analytics = typeof window !== 'undefined' && import.meta.env.PROD
  ? getAnalytics(app)
  : null

console.log('🔥 Firebase initialized successfully with project:', firebaseConfig.projectId)

// Connect to emulators in development (disabled for now - using Firebase real)
// Uncomment the code below if you want to use Firebase emulators
/*
if (import.meta.env.DEV && import.meta.env.VITE_USE_EMULATORS === 'true') {
  const isEmulatorConnected = {
    auth: false,
    firestore: false,
    storage: false,
    functions: false
  }

  // Auth emulator
  if (!isEmulatorConnected.auth) {
    try {
      connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true })
      isEmulatorConnected.auth = true
      console.log('🔥 Connected to Auth Emulator')
    } catch (error) {
      console.warn('Failed to connect to Auth Emulator:', error)
    }
  }

  // Firestore emulator
  if (!isEmulatorConnected.firestore) {
    try {
      connectFirestoreEmulator(db, 'localhost', 8080)
      isEmulatorConnected.firestore = true
      console.log('🔥 Connected to Firestore Emulator')
    } catch (error) {
      console.warn('Failed to connect to Firestore Emulator:', error)
    }
  }

  // Storage emulator
  if (!isEmulatorConnected.storage) {
    try {
      connectStorageEmulator(storage, 'localhost', 9199)
      isEmulatorConnected.storage = true
      console.log('🔥 Connected to Storage Emulator')
    } catch (error) {
      console.warn('Failed to connect to Storage Emulator:', error)
    }
  }

  // Functions emulator
  if (!isEmulatorConnected.functions) {
    try {
      connectFunctionsEmulator(functions, 'localhost', 5001)
      isEmulatorConnected.functions = true
      console.log('🔥 Connected to Functions Emulator')
    } catch (error) {
      console.warn('Failed to connect to Functions Emulator:', error)
    }
  }
}
*/

console.log('🔥 Using Firebase production services')

export default app
