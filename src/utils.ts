import { Note } from './types'

// Text and content utilities
export const calculateStats = (content: string) => {
  const text = content.replace(/<[^>]*>/g, '').trim()
  const words = text ? text.split(/\s+/).length : 0
  const readTime = Math.ceil(words / 200) // 200 words per minute

  return {
    words,
    readTime: readTime || 1
  }
}

export const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

export const stripHtml = (html: string): string => {
  return html.replace(/<[^>]*>/g, '')
}

export const highlightText = (text: string, searchTerm: string): string => {
  if (!searchTerm) return text

  const regex = new RegExp(`(${searchTerm})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// AI and suggestions
export const generateAISuggestions = (content: string): string[] => {
  const suggestions: string[] = []
  const lowerContent = content.toLowerCase()

  // Simple keyword-based suggestions
  const keywords = {
    'meeting': ['meeting', 'reunião', 'call', 'conference', 'agenda'],
    'brainstorm': ['idea', 'brainstorm', 'concept', 'innovation', 'creative'],
    'tasks': ['task', 'todo', 'action', 'deadline', 'complete'],
    'project': ['project', 'projeto', 'development', 'build', 'plan'],
    'research': ['research', 'study', 'analysis', 'investigation', 'learn'],
    'personal': ['personal', 'diary', 'journal', 'thoughts', 'feelings'],
    'work': ['work', 'job', 'office', 'business', 'professional'],
    'urgent': ['urgent', 'asap', 'priority', 'important', 'critical'],
    'review': ['review', 'feedback', 'evaluation', 'assessment', 'check']
  }

  Object.entries(keywords).forEach(([tag, words]) => {
    if (words.some(word => lowerContent.includes(word))) {
      suggestions.push(tag)
    }
  })

  return [...new Set(suggestions)] // Remove duplicates
}

// Note filtering and sorting
export const filterNotes = (
  notes: Note[],
  searchTerm: string,
  filterTag: string,
  filterCategory: string
): Note[] => {
  return notes.filter(note => {
    const matchesSearch = !searchTerm ||
      note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      note.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      note.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

    // Handle special filter tags for starred, pinned, locked, and public
    let matchesTag = true;
    if (filterTag) {
      if (filterTag === 'starred') {
        matchesTag = note.starred;
      } else if (filterTag === 'pinned') {
        matchesTag = note.pinned;
      } else if (filterTag === 'locked') {
        matchesTag = note.locked;
      } else if (filterTag === 'public') {
        matchesTag = note.isPublic;
      } else {
        matchesTag = note.tags.includes(filterTag);
      }
    }

    // Handle special category filter for uncategorized notes
    let matchesCategory = true;
    if (filterCategory) {
      if (filterCategory === 'uncategorized') {
        matchesCategory = !note.category || note.category === '';
      } else {
        matchesCategory = note.category === filterCategory;
      }
    }

    return matchesSearch && matchesTag && matchesCategory
  })
}

export const sortNotes = (notes: Note[]): Note[] => {
  return [...notes].sort((a, b) => {
    // Pinned notes first
    if (a.pinned && !b.pinned) return -1
    if (!a.pinned && b.pinned) return 1

    // Then starred notes
    if (a.starred && !b.starred) return -1
    if (!a.starred && b.starred) return 1

    // Finally by update date
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  })
}

export const getAllTags = (notes: Note[]): string[] => {
  const allTags = notes.flatMap(note => note.tags)
  return [...new Set(allTags)].sort()
}

// Export and sharing utilities
export const exportNote = (note: Note): void => {
  const content = `# ${note.title}\n\n${stripHtml(note.content)}\n\n---\nTags: ${note.tags.join(', ')}\nCategoria: ${note.category}\nCriado em: ${note.createdAt.toLocaleDateString()}`

  const blob = new Blob([content], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)

  const link = document.createElement('a')
  link.href = url
  link.download = `${note.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  URL.revokeObjectURL(url)
}

export const copyNoteToClipboard = async (note: Note): Promise<void> => {
  const content = `${note.title}\n\n${stripHtml(note.content)}`

  try {
    await navigator.clipboard.writeText(content)
  } catch (err) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = content
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
  }
}

export const shareNote = (note: Note): void => {
  if (navigator.share) {
    navigator.share({
      title: note.title,
      text: stripHtml(note.content).substring(0, 200) + '...',
      url: window.location.href
    })
  } else {
    // Fallback: copy link to clipboard
    const shareUrl = `${window.location.origin}/note/${note.id}`
    copyNoteToClipboard({ ...note, content: shareUrl })
  }
}

// Date and time utilities
export const formatDate = (date: Date): string => {
  const now = new Date()
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffInHours < 1) {
    const minutes = Math.floor(diffInHours * 60)
    return `${minutes} min atrás`
  } else if (diffInHours < 24) {
    const hours = Math.floor(diffInHours)
    return `${hours}h atrás`
  } else if (diffInHours < 48) {
    return 'Ontem'
  } else {
    return date.toLocaleDateString('pt-BR')
  }
}

export const formatDateTime = (date: Date): string => {
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Color utilities
export const getContrastColor = (hexColor: string): string => {
  // Convert hex to RGB
  const r = parseInt(hexColor.slice(1, 3), 16)
  const g = parseInt(hexColor.slice(3, 5), 16)
  const b = parseInt(hexColor.slice(5, 7), 16)

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  return luminance > 0.5 ? '#000000' : '#ffffff'
}

export const lightenColor = (hexColor: string, percent: number): string => {
  const num = parseInt(hexColor.replace('#', ''), 16)
  const amt = Math.round(2.55 * percent)
  const R = (num >> 16) + amt
  const G = (num >> 8 & 0x00FF) + amt
  const B = (num & 0x0000FF) + amt

  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255))
    .toString(16)
    .slice(1)
}

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (password.length < 6) {
    errors.push('Senha deve ter pelo menos 6 caracteres')
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Senha deve ter pelo menos uma letra maiúscula')
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Senha deve ter pelo menos uma letra minúscula')
  }

  if (!/\d/.test(password)) {
    errors.push('Senha deve ter pelo menos um número')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Local storage utilities
export const saveToLocalStorage = (key: string, value: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

export const loadFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error('Error loading from localStorage:', error)
    return defaultValue
  }
}

export const removeFromLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error removing from localStorage:', error)
  }
}

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout

  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Throttle utility
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Random utilities
export const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export const generateColor = (): string => {
  const colors = [
    '#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444',
    '#06B6D4', '#84CC16', '#EC4899', '#6B7280', '#F97316'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

// Analytics utilities
export const calculateProductivityScore = (notes: Note[]): number => {
  if (notes.length === 0) return 0

  const now = new Date()
  const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

  const recentNotes = notes.filter(note => new Date(note.updatedAt) > lastWeek)
  const totalWords = recentNotes.reduce((sum, note) => sum + note.wordCount, 0)
  const avgWordsPerNote = totalWords / recentNotes.length || 0

  // Score based on activity and quality
  const activityScore = Math.min(recentNotes.length * 10, 50)
  const qualityScore = Math.min(avgWordsPerNote / 10, 50)

  return Math.round(activityScore + qualityScore)
}

export const getWeeklyActivity = (notes: Note[]): number[] => {
  const activity = new Array(7).fill(0)
  const now = new Date()

  notes.forEach(note => {
    const noteDate = new Date(note.createdAt)
    const daysDiff = Math.floor((now.getTime() - noteDate.getTime()) / (1000 * 60 * 60 * 24))

    if (daysDiff < 7) {
      activity[6 - daysDiff]++
    }
  })

  return activity
}
