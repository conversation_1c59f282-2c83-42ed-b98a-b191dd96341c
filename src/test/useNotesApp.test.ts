import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useNotesApp } from '../hooks/useNotesApp'

describe('useNotesApp Hook', () => {
  let result: any

  beforeEach(() => {
    const { result: hookResult } = renderHook(() => useNotesApp())
    result = hookResult
  })

  describe('Initial State', () => {
    it('should initialize with default values', () => {
      expect(result.current.notes).toHaveLength(1) // INITIAL_NOTE
      expect(result.current.searchTerm).toBe('')
      expect(result.current.selectedNote).toBeNull()
      expect(result.current.isEditing).toBe(false)
      expect(result.current.darkMode).toBe(true)
      expect(result.current.viewMode).toBe('grid')
      expect(result.current.showSidebar).toBe(true)
    })

    it('should have initial collaborators and notifications', () => {
      expect(result.current.collaborators).toHaveLength(1)
      expect(result.current.notifications).toHaveLength(2)
    })
  })

  describe('Note Management', () => {
    it('should handle new note creation', () => {
      act(() => {
        result.current.handleNewNote()
      })

      expect(result.current.isEditing).toBe(true)
      expect(result.current.showNewNote).toBe(true)
      expect(result.current.noteTitle).toBe('')
      expect(result.current.noteContent).toBe('')
      expect(result.current.selectedNote).toBeNull()
    })

    it('should save a new note', () => {
      act(() => {
        result.current.handleNewNote()
        result.current.setNoteTitle('Test Note')
        result.current.setNoteContent('Test content')
        result.current.setNoteTags(['test'])
        result.current.handleSaveNote()
      })

      expect(result.current.notes).toHaveLength(2)
      expect(result.current.notes[0].title).toBe('Test Note')
      expect(result.current.notes[0].content).toBe('Test content')
      expect(result.current.notes[0].tags).toContain('test')
      expect(result.current.isEditing).toBe(false)
    })

    it('should edit existing note', () => {
      const existingNote = result.current.notes[0]

      act(() => {
        result.current.handleEditNote(existingNote)
      })

      expect(result.current.selectedNote).toBe(existingNote)
      expect(result.current.isEditing).toBe(true)
      expect(result.current.noteTitle).toBe(existingNote.title)
      expect(result.current.noteContent).toBe(existingNote.content)
    })

    it('should delete a note', () => {
      const initialLength = result.current.notes.length
      const noteToDelete = result.current.notes[0]

      act(() => {
        result.current.handleDeleteNote(noteToDelete.id)
      })

      expect(result.current.notes).toHaveLength(initialLength - 1)
      expect(result.current.notes.find((n: any) => n.id === noteToDelete.id)).toBeUndefined()
    })

    it('should toggle note star status', () => {
      const note = result.current.notes[0]
      const initialStarred = note.starred

      act(() => {
        result.current.handleToggleStar(note.id)
      })

      const updatedNote = result.current.notes.find((n: any) => n.id === note.id)
      expect(updatedNote.starred).toBe(!initialStarred)
    })

    it('should toggle note pin status', () => {
      const note = result.current.notes[0]
      const initialPinned = note.pinned

      act(() => {
        result.current.handleTogglePin(note.id)
      })

      const updatedNote = result.current.notes.find((n: any) => n.id === note.id)
      expect(updatedNote.pinned).toBe(!initialPinned)
    })

    it('should toggle note lock status', () => {
      const note = result.current.notes[0]
      const initialLocked = note.locked

      act(() => {
        result.current.handleToggleLock(note.id)
      })

      const updatedNote = result.current.notes.find((n: any) => n.id === note.id)
      expect(updatedNote.locked).toBe(!initialLocked)
    })

    it('should toggle note public status', () => {
      const note = result.current.notes[0]
      const initialPublic = note.isPublic

      act(() => {
        result.current.handleTogglePublic(note.id)
      })

      const updatedNote = result.current.notes.find((n: any) => n.id === note.id)
      expect(updatedNote.isPublic).toBe(!initialPublic)
    })
  })

  describe('Quick Capture', () => {
    it('should handle quick capture', () => {
      const initialLength = result.current.notes.length

      act(() => {
        result.current.setQuickCaptureText('Quick note content')
        result.current.handleQuickCapture()
      })

      expect(result.current.notes).toHaveLength(initialLength + 1)
      expect(result.current.notes[0].content).toContain('Quick note content')
      expect(result.current.notes[0].tags).toContain('quick-capture')
      expect(result.current.quickCaptureText).toBe('')
      expect(result.current.showQuickCapture).toBe(false)
    })

    it('should not create note with empty quick capture text', () => {
      const initialLength = result.current.notes.length

      act(() => {
        result.current.setQuickCaptureText('')
        result.current.handleQuickCapture()
      })

      expect(result.current.notes).toHaveLength(initialLength)
    })
  })

  describe('Template Usage', () => {
    it('should use template for new note', () => {
      const template = {
        id: 'test-template',
        name: 'Test Template',
        icon: null,
        content: '<h1>Template Content</h1>'
      }

      act(() => {
        result.current.handleUseTemplate(template)
      })

      expect(result.current.isEditing).toBe(true)
      expect(result.current.noteTitle).toBe('Test Template')
      expect(result.current.noteContent).toBe('<h1>Template Content</h1>')
      expect(result.current.noteTags).toContain('test-template')
    })
  })

  describe('Computed Values', () => {
    it('should filter notes correctly', () => {
      act(() => {
        result.current.setSearchTerm('bem-vindo')
      })

      expect(result.current.filteredNotes).toHaveLength(1)
      expect(result.current.filteredNotes[0].title).toContain('Bem-vindo')
    })

    it('should get all tags from notes', () => {
      expect(result.current.allTags).toContain('tutorial')
      expect(result.current.allTags).toContain('início')
    })

    it('should sort notes with pinned and starred first', () => {
      // Add a new note and pin it
      act(() => {
        result.current.handleNewNote()
        result.current.setNoteTitle('Pinned Note')
        result.current.handleSaveNote()
        result.current.handleTogglePin(result.current.notes[0].id)
      })

      const sortedNotes = result.current.sortedNotes
      expect(sortedNotes[0].title).toBe('Pinned Note')
    })
  })

  describe('State Management', () => {
    it('should update search term', () => {
      act(() => {
        result.current.setSearchTerm('test search')
      })

      expect(result.current.searchTerm).toBe('test search')
    })

    it('should toggle dark mode', () => {
      const initialDarkMode = result.current.darkMode

      act(() => {
        result.current.setDarkMode(!initialDarkMode)
      })

      expect(result.current.darkMode).toBe(!initialDarkMode)
    })

    it('should toggle view mode', () => {
      expect(result.current.viewMode).toBe('grid')

      act(() => {
        result.current.setViewMode('list')
      })

      expect(result.current.viewMode).toBe('list')
    })

    it('should toggle sidebar visibility', () => {
      const initialShowSidebar = result.current.showSidebar

      act(() => {
        result.current.setShowSidebar(!initialShowSidebar)
      })

      expect(result.current.showSidebar).toBe(!initialShowSidebar)
    })
  })

  describe('Notifications', () => {
    it('should add notification', () => {
      const initialLength = result.current.notifications.length

      act(() => {
        result.current.addNotification('test', 'Test User', 'Test message', 'now')
      })

      expect(result.current.notifications).toHaveLength(initialLength + 1)
      expect(result.current.notifications[0].type).toBe('test')
      expect(result.current.notifications[0].user).toBe('Test User')
      expect(result.current.notifications[0].message).toBe('Test message')
      expect(result.current.notifications[0].read).toBe(false)
    })

    it('should calculate unread notifications correctly', () => {
      const unreadCount = result.current.unreadNotifications
      expect(typeof unreadCount).toBe('number')
      expect(unreadCount).toBeGreaterThanOrEqual(0)
    })
  })
})
