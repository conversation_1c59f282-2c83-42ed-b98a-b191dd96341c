import '@testing-library/jest-dom'

// Mock do localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock do document.execCommand
Object.defineProperty(document, 'execCommand', {
  value: vi.fn(() => true)
})

// Mock do navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: vi.fn(() => Promise.resolve())
  }
})

// Mock do prompt
Object.defineProperty(window, 'prompt', {
  value: vi.fn(() => 'http://example.com')
})

// Mock do alert
Object.defineProperty(window, 'alert', {
  value: vi.fn()
})

// Mock do URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  value: vi.fn(() => 'blob:mock-url')
})

// Mock do URL.revokeObjectURL
Object.defineProperty(URL, 'revokeObjectURL', {
  value: vi.fn()
})
