import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  calculateStats,
  formatTime,
  generateAISuggestions,
  filterNotes,
  sortNotes,
  getAllTags,
  exportNote,
  copyNoteToClipboard
} from '../../utils'
import { Note } from '../../types'

describe('Utils', () => {
  describe('calculateStats', () => {
    it('should calculate word count and read time correctly', () => {
      const content = '<p>This is a test content with multiple words</p>'
      const result = calculateStats(content)

      expect(result.words).toBe(9)
      expect(result.readTime).toBe(1) // Math.ceil(9 / 200)
    })

    it('should handle empty content', () => {
      const result = calculateStats('')

      expect(result.words).toBe(1) // split returns [''] which has length 1
      expect(result.readTime).toBe(1)
    })

    it('should strip HTML tags', () => {
      const content = '<h1>Title</h1><p>Paragraph with <strong>bold</strong> text</p>'
      const result = calculateStats(content)

      expect(result.words).toBe(6) // "Title Paragraph with bold text"
    })
  })

  describe('formatTime', () => {
    it('should format seconds correctly', () => {
      expect(formatTime(0)).toBe('00:00')
      expect(formatTime(30)).toBe('00:30')
      expect(formatTime(60)).toBe('01:00')
      expect(formatTime(125)).toBe('02:05')
      expect(formatTime(3661)).toBe('61:01')
    })
  })

  describe('generateAISuggestions', () => {
    it('should suggest meeting tag for meeting content', () => {
      const content = 'We had a meeting today to discuss the project'
      const suggestions = generateAISuggestions(content)

      expect(suggestions).toContain('meeting')
    })

    it('should suggest brainstorm tag for idea content', () => {
      const content = 'I have a great idea for the new feature'
      const suggestions = generateAISuggestions(content)

      expect(suggestions).toContain('brainstorm')
    })

    it('should suggest multiple tags', () => {
      const content = 'Meeting to discuss new ideas and tasks to do'
      const suggestions = generateAISuggestions(content)

      expect(suggestions).toContain('meeting')
      expect(suggestions).toContain('brainstorm')
      expect(suggestions).toContain('tasks')
    })

    it('should return unique suggestions', () => {
      const content = 'meeting meeting idea idea'
      const suggestions = generateAISuggestions(content)

      expect(suggestions.filter(s => s === 'meeting')).toHaveLength(1)
      expect(suggestions.filter(s => s === 'brainstorm')).toHaveLength(1)
    })
  })

  describe('filterNotes', () => {
    const mockNotes: Note[] = [
      {
        id: 1,
        title: 'React Tutorial',
        content: 'Learning React hooks',
        tags: ['react', 'tutorial'],
        category: 'estudos',
        color: '#3B82F6',
        starred: false,
        pinned: false,
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      },
      {
        id: 2,
        title: 'Meeting Notes',
        content: 'Project discussion',
        tags: ['meeting', 'work'],
        category: 'trabalho',
        color: '#10B981',
        starred: true,
        pinned: false,
        createdAt: '2023-01-02',
        updatedAt: '2023-01-02',
        locked: false,
        mood: 'neutral',
        wordCount: 5,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      }
    ]

    it('should filter by search term in title', () => {
      const result = filterNotes(mockNotes, 'react', '', '')
      expect(result).toHaveLength(1)
      expect(result[0].title).toBe('React Tutorial')
    })

    it('should filter by search term in content', () => {
      const result = filterNotes(mockNotes, 'discussion', '', '')
      expect(result).toHaveLength(1)
      expect(result[0].title).toBe('Meeting Notes')
    })

    it('should filter by tag', () => {
      const result = filterNotes(mockNotes, '', 'tutorial', '')
      expect(result).toHaveLength(1)
      expect(result[0].title).toBe('React Tutorial')
    })

    it('should filter by category', () => {
      const result = filterNotes(mockNotes, '', '', 'trabalho')
      expect(result).toHaveLength(1)
      expect(result[0].title).toBe('Meeting Notes')
    })

    it('should return all notes when no filters applied', () => {
      const result = filterNotes(mockNotes, '', '', '')
      expect(result).toHaveLength(2)
    })
  })

  describe('sortNotes', () => {
    const mockNotes: Note[] = [
      {
        id: 1,
        title: 'Normal Note',
        starred: false,
        pinned: false,
        updatedAt: '2023-01-01',
        content: '',
        tags: [],
        category: 'pessoal',
        color: '#3B82F6',
        createdAt: '2023-01-01',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      },
      {
        id: 2,
        title: 'Starred Note',
        starred: true,
        pinned: false,
        updatedAt: '2023-01-02',
        content: '',
        tags: [],
        category: 'pessoal',
        color: '#3B82F6',
        createdAt: '2023-01-02',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      },
      {
        id: 3,
        title: 'Pinned Note',
        starred: false,
        pinned: true,
        updatedAt: '2023-01-03',
        content: '',
        tags: [],
        category: 'pessoal',
        color: '#3B82F6',
        createdAt: '2023-01-03',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      }
    ]

    it('should sort pinned notes first', () => {
      const result = sortNotes(mockNotes)
      expect(result[0].title).toBe('Pinned Note')
    })

    it('should sort starred notes after pinned', () => {
      const result = sortNotes(mockNotes)
      expect(result[1].title).toBe('Starred Note')
    })

    it('should not mutate original array', () => {
      const originalLength = mockNotes.length
      sortNotes(mockNotes)
      expect(mockNotes).toHaveLength(originalLength)
    })
  })

  describe('getAllTags', () => {
    const mockNotes: Note[] = [
      {
        id: 1,
        tags: ['react', 'tutorial'],
        title: '',
        content: '',
        category: 'pessoal',
        color: '#3B82F6',
        starred: false,
        pinned: false,
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      },
      {
        id: 2,
        tags: ['meeting', 'react'],
        title: '',
        content: '',
        category: 'pessoal',
        color: '#3B82F6',
        starred: false,
        pinned: false,
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      }
    ]

    it('should return unique tags', () => {
      const result = getAllTags(mockNotes)
      expect(result).toContain('react')
      expect(result).toContain('tutorial')
      expect(result).toContain('meeting')
      expect(result.filter(tag => tag === 'react')).toHaveLength(1)
    })
  })

  describe('exportNote', () => {
    beforeEach(() => {
      // Mock DOM methods
      document.createElement = vi.fn(() => ({
        href: '',
        download: '',
        click: vi.fn(),
        style: {}
      })) as any

      document.body.appendChild = vi.fn()
      document.body.removeChild = vi.fn()
    })

    it('should create download link for note export', () => {
      const mockNote: Note = {
        id: 1,
        title: 'Test Note',
        content: '<p>Test content</p>',
        tags: ['test', 'export'],
        category: 'pessoal',
        color: '#3B82F6',
        starred: false,
        pinned: false,
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      }

      exportNote(mockNote)

      expect(document.createElement).toHaveBeenCalledWith('a')
      expect(document.body.appendChild).toHaveBeenCalled()
      expect(document.body.removeChild).toHaveBeenCalled()
    })
  })

  describe('copyNoteToClipboard', () => {
    it('should copy note content to clipboard', async () => {
      const mockNote: Note = {
        id: 1,
        title: 'Test Note',
        content: '<p>Test content</p>',
        tags: [],
        category: 'pessoal',
        color: '#3B82F6',
        starred: false,
        pinned: false,
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
        locked: false,
        mood: 'neutral',
        wordCount: 10,
        readTime: 1,
        connections: [],
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      }

      await copyNoteToClipboard(mockNote)

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Test Note\n\nTest content')
    })
  })
})
