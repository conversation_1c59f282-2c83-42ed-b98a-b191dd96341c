import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import NoteCard from '../../components/NoteCard'
import { Note } from '../../types'

const mockNote: Note = {
  id: 1,
  title: 'Test Note',
  content: '<p>This is a test note content</p>',
  tags: ['test', 'react'],
  color: '#3B82F6',
  starred: false,
  pinned: false,
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z',
  category: 'pessoal',
  locked: false,
  mood: 'happy',
  wordCount: 6,
  readTime: 1,
  connections: [],
  todos: [],
  audioNote: null,
  collaborators: [],
  comments: [],
  version: 1,
  isPublic: false,
  permissions: 'owner'
}

const defaultProps = {
  note: mockNote,
  darkMode: false,
  viewMode: 'grid' as const,
  copiedId: null,
  onEdit: vi.fn(),
  onDelete: vi.fn(),
  onToggleStar: vi.fn(),
  onTogglePin: vi.fn(),
  onToggleLock: vi.fn(),
  onTogglePublic: vi.fn(),
  onCopy: vi.fn(),
  onExport: vi.fn(),
  onShare: vi.fn()
}

describe('NoteCard Component', () => {
  it('should render note title and content', () => {
    render(<NoteCard {...defaultProps} />)
    
    expect(screen.getByText('Test Note')).toBeInTheDocument()
    expect(screen.getByText(/This is a test note content/)).toBeInTheDocument()
  })

  it('should display note metadata', () => {
    render(<NoteCard {...defaultProps} />)
    
    expect(screen.getByText('pessoal')).toBeInTheDocument()
    expect(screen.getByText('6 palavras')).toBeInTheDocument()
    expect(screen.getByText('1 min')).toBeInTheDocument()
  })

  it('should display tags', () => {
    render(<NoteCard {...defaultProps} />)
    
    expect(screen.getByText('#test')).toBeInTheDocument()
    expect(screen.getByText('#react')).toBeInTheDocument()
  })

  it('should call onEdit when card is clicked', () => {
    render(<NoteCard {...defaultProps} />)
    
    fireEvent.click(screen.getByText('Test Note'))
    expect(defaultProps.onEdit).toHaveBeenCalledWith(mockNote)
  })

  it('should call onToggleStar when star button is clicked', () => {
    render(<NoteCard {...defaultProps} />)
    
    const starButton = screen.getByRole('button', { name: /star/i })
    fireEvent.click(starButton)
    expect(defaultProps.onToggleStar).toHaveBeenCalledWith(mockNote.id)
  })

  it('should show pinned indicator when note is pinned', () => {
    const pinnedNote = { ...mockNote, pinned: true }
    render(<NoteCard {...defaultProps} note={pinnedNote} />)
    
    expect(screen.getByTitle(/fixar/i)).toBeInTheDocument()
  })

  it('should show starred state correctly', () => {
    const starredNote = { ...mockNote, starred: true }
    render(<NoteCard {...defaultProps} note={starredNote} />)
    
    const starButton = screen.getByRole('button', { name: /star/i })
    expect(starButton).toHaveClass('text-yellow-500')
  })

  it('should show locked indicator when note is locked', () => {
    const lockedNote = { ...mockNote, locked: true }
    render(<NoteCard {...defaultProps} note={lockedNote} />)
    
    expect(screen.getByTitle(/desbloquear/i)).toBeInTheDocument()
  })

  it('should show public indicator when note is public', () => {
    const publicNote = { ...mockNote, isPublic: true }
    render(<NoteCard {...defaultProps} note={publicNote} />)
    
    expect(screen.getByTitle(/tornar privada/i)).toBeInTheDocument()
  })

  it('should show collaborators count when there are collaborators', () => {
    const noteWithCollaborators = {
      ...mockNote,
      collaborators: [
        { id: 1, name: 'User 1', email: '<EMAIL>', avatar: '👤', permission: 'edit', isOnline: true },
        { id: 2, name: 'User 2', email: '<EMAIL>', avatar: '👤', permission: 'view', isOnline: false }
      ]
    }
    render(<NoteCard {...defaultProps} note={noteWithCollaborators} />)
    
    expect(screen.getByText('2')).toBeInTheDocument()
  })

  it('should show comments count when there are comments', () => {
    const noteWithComments = {
      ...mockNote,
      comments: [
        { id: 1, user: 'User 1', avatar: '👤', text: 'Great note!', time: '1h ago' },
        { id: 2, user: 'User 2', avatar: '👤', text: 'Thanks for sharing', time: '2h ago' }
      ]
    }
    render(<NoteCard {...defaultProps} note={noteWithComments} />)
    
    expect(screen.getByText('2')).toBeInTheDocument()
  })

  it('should render in list view mode correctly', () => {
    render(<NoteCard {...defaultProps} viewMode="list" />)
    
    // In list mode, the card should have different styling
    const cardElement = screen.getByText('Test Note').closest('div')
    expect(cardElement).toHaveClass('flex', 'items-center')
  })

  it('should show mood emoji when mood is set', () => {
    render(<NoteCard {...defaultProps} />)
    
    // The happy mood should show the 😊 emoji
    expect(screen.getByText('😊')).toBeInTheDocument()
  })

  it('should show copied state when note is copied', () => {
    render(<NoteCard {...defaultProps} copiedId={mockNote.id} />)
    
    // Should show check icon instead of copy icon
    expect(screen.getByTitle(/copiar/i)).toBeInTheDocument()
  })

  it('should call action handlers correctly', () => {
    render(<NoteCard {...defaultProps} />)
    
    // Test delete button
    const deleteButton = screen.getByTitle(/deletar/i)
    fireEvent.click(deleteButton)
    expect(defaultProps.onDelete).toHaveBeenCalledWith(mockNote.id)

    // Test share button
    const shareButton = screen.getByTitle(/compartilhar/i)
    fireEvent.click(shareButton)
    expect(defaultProps.onShare).toHaveBeenCalledWith(mockNote)

    // Test export button
    const exportButton = screen.getByTitle(/exportar/i)
    fireEvent.click(exportButton)
    expect(defaultProps.onExport).toHaveBeenCalledWith(mockNote)
  })

  it('should prevent event propagation on action buttons', () => {
    render(<NoteCard {...defaultProps} />)
    
    const starButton = screen.getByRole('button', { name: /star/i })
    const mockEvent = { stopPropagation: vi.fn() }
    
    fireEvent.click(starButton, mockEvent)
    
    // The onEdit should not be called when clicking action buttons
    expect(defaultProps.onEdit).not.toHaveBeenCalled()
  })

  it('should apply dark mode styles correctly', () => {
    render(<NoteCard {...defaultProps} darkMode={true} />)
    
    const cardElement = screen.getByText('Test Note').closest('div')
    expect(cardElement).toHaveClass('bg-gray-800/50')
  })

  it('should truncate long content in preview', () => {
    const longContentNote = {
      ...mockNote,
      content: '<p>' + 'Very long content '.repeat(20) + '</p>'
    }
    render(<NoteCard {...defaultProps} note={longContentNote} />)
    
    const contentElement = screen.getByText(/Very long content/)
    expect(contentElement.textContent).toMatch(/\.\.\./)
  })

  it('should limit number of displayed tags', () => {
    const manyTagsNote = {
      ...mockNote,
      tags: ['tag1', 'tag2', 'tag3', 'tag4', 'tag5', 'tag6']
    }
    render(<NoteCard {...defaultProps} note={manyTagsNote} />)
    
    // Should show +2 indicator for extra tags in grid view
    expect(screen.getByText('+2')).toBeInTheDocument()
  })
})
