@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Configurações de Aparência */
:root {
  --spacing-scale: 1;
  --font-size-base: 16px;
}

/* <PERSON><PERSON><PERSON> */
html.font-small {
  --font-size-base: 14px;
}

html.font-medium {
  --font-size-base: 16px;
}

html.font-large {
  --font-size-base: 18px;
}

html.font-extra-large {
  --font-size-base: 20px;
}

/* Modo Compacto */
html.compact-mode {
  --spacing-scale: 0.75;
}

html.compact-mode .p-1 { padding: calc(0.25rem * var(--spacing-scale)); }
html.compact-mode .p-2 { padding: calc(0.5rem * var(--spacing-scale)); }
html.compact-mode .p-3 { padding: calc(0.75rem * var(--spacing-scale)); }
html.compact-mode .p-4 { padding: calc(1rem * var(--spacing-scale)); }
html.compact-mode .p-6 { padding: calc(1.5rem * var(--spacing-scale)); }
html.compact-mode .p-8 { padding: calc(2rem * var(--spacing-scale)); }

html.compact-mode .m-1 { margin: calc(0.25rem * var(--spacing-scale)); }
html.compact-mode .m-2 { margin: calc(0.5rem * var(--spacing-scale)); }
html.compact-mode .m-3 { margin: calc(0.75rem * var(--spacing-scale)); }
html.compact-mode .m-4 { margin: calc(1rem * var(--spacing-scale)); }
html.compact-mode .m-6 { margin: calc(1.5rem * var(--spacing-scale)); }
html.compact-mode .m-8 { margin: calc(2rem * var(--spacing-scale)); }

html.compact-mode .gap-1 { gap: calc(0.25rem * var(--spacing-scale)); }
html.compact-mode .gap-2 { gap: calc(0.5rem * var(--spacing-scale)); }
html.compact-mode .gap-3 { gap: calc(0.75rem * var(--spacing-scale)); }
html.compact-mode .gap-4 { gap: calc(1rem * var(--spacing-scale)); }
html.compact-mode .gap-6 { gap: calc(1.5rem * var(--spacing-scale)); }

html.compact-mode .space-y-1 > * + * { margin-top: calc(0.25rem * var(--spacing-scale)); }
html.compact-mode .space-y-2 > * + * { margin-top: calc(0.5rem * var(--spacing-scale)); }
html.compact-mode .space-y-3 > * + * { margin-top: calc(0.75rem * var(--spacing-scale)); }
html.compact-mode .space-y-4 > * + * { margin-top: calc(1rem * var(--spacing-scale)); }
html.compact-mode .space-y-6 > * + * { margin-top: calc(1.5rem * var(--spacing-scale)); }

/* Aplicar tamanho de fonte base */
html {
  font-size: var(--font-size-base);
}

/* Responsividade para tamanhos de fonte */
@media (max-width: 768px) {
  html.font-small { --font-size-base: 13px; }
  html.font-medium { --font-size-base: 15px; }
  html.font-large { --font-size-base: 17px; }
  html.font-extra-large { --font-size-base: 19px; }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4B5563;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background: #4B5563;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Focus styles */
.focus-visible:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Custom button styles */
.btn-primary {
  @apply px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all hover:scale-105;
}

.btn-secondary {
  @apply px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all;
}

/* Card styles */
.card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-all hover:shadow-lg;
}

/* Input styles */
.input {
  @apply w-full px-3 py-2 bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all;
}

/* Backdrop blur support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur {
  backdrop-filter: blur(8px);
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
