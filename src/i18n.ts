import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translations
import ptBR from './locales/pt-BR/translation.json';
import ptPT from './locales/pt-PT/translation.json';
import en from './locales/en/translation.json';

const resources = {
  'pt-BR': {
    translation: ptBR
  },
  'pt-PT': {
    translation: ptPT
  },
  'en': {
    translation: en
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'pt-BR',
    debug: true, // Enable debug for troubleshooting

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
      lookupLocalStorage: 'noteflow-language',
    },

    react: {
      useSuspense: false,
    },
  })
  .then(() => {
    console.log('🌐 i18n initialized successfully');
    console.log('🌐 Current language:', i18n.language);
    console.log('🌐 Available resources:', Object.keys(i18n.options.resources || {}));
    console.log('🌐 Test translation:', i18n.t('common.save'));
  })
  .catch((error) => {
    console.error('❌ i18n initialization failed:', error);
  });

export default i18n;

// Language options for the settings
export const languageOptions = [
  { code: 'pt-BR', name: 'Português (Brasil)', flag: '🇧🇷' },
  { code: 'pt-PT', name: 'Português (Portugal)', flag: '🇵🇹' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
];

// Helper function to get language name
export const getLanguageName = (code: string): string => {
  const option = languageOptions.find(lang => lang.code === code);
  return option ? option.name : code;
};

// Helper function to get language flag
export const getLanguageFlag = (code: string): string => {
  const option = languageOptions.find(lang => lang.code === code);
  return option ? option.flag : '🌐';
};
