import { Template } from './types'

// Application constants
export const APP_NAME = 'NoteFlow'
export const APP_VERSION = '1.0.0'
export const APP_DESCRIPTION = 'Sistema de notas ultra moderno com AI integrada'

// Categories
export const CATEGORIES = [
  { id: 'pessoal', name: 'Pessoal', icon: '👤', color: '#3B82F6' },
  { id: 'trabalho', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '💼', color: '#10B981' },
  { id: 'estudos', name: 'Estudo<PERSON>', icon: '📚', color: '#8B5CF6' },
  { id: 'projetos', name: 'Projetos', icon: '🚀', color: '#F59E0B' },
  { id: 'ideias', name: 'I<PERSON><PERSON>', icon: '💡', color: '#EF4444' },
  { id: 'receitas', name: 'Receitas', icon: '🍳', color: '#06B6D4' },
  { id: 'viagem', name: 'Viagem', icon: '✈️', color: '#84CC16' },
  { id: 'saude', name: '<PERSON>ú<PERSON>', icon: '🏥', color: '#EC4899' }
]

// Note colors
export const NOTE_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#8B5CF6', // Purple
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
  '#F97316'  // Orange
]

// Mood options
export const MOODS = [
  { id: 'happy', name: 'Feliz', emoji: '😊', color: '#10B981' },
  { id: 'sad', name: 'Triste', emoji: '😢', color: '#6B7280' },
  { id: 'excited', name: 'Animado', emoji: '🤩', color: '#F59E0B' },
  { id: 'calm', name: 'Calmo', emoji: '😌', color: '#06B6D4' },
  { id: 'stressed', name: 'Estressado', emoji: '😰', color: '#EF4444' },
  { id: 'neutral', name: 'Neutro', emoji: '😐', color: '#8B5CF6' }
]

// Templates
export const TEMPLATES: Template[] = [
  {
    id: 'meeting-notes',
    name: 'Notas de Reunião',
    icon: '📝',
    content: `# Reunião - [Data]

## Participantes
- 
- 
- 

## Agenda
1. 
2. 
3. 

## Discussões
### Tópico 1


### Tópico 2


## Ações
- [ ] Ação 1 - Responsável: [Nome] - Prazo: [Data]
- [ ] Ação 2 - Responsável: [Nome] - Prazo: [Data]

## Próximos Passos
- 
- 

## Observações
`
  },
  {
    id: 'daily-journal',
    name: 'Diário Pessoal',
    icon: '📔',
    content: `# Diário - ${new Date().toLocaleDateString('pt-BR')}

## Como me sinto hoje
😊 

## O que aconteceu hoje
- 
- 
- 

## Conquistas
✅ 
✅ 
✅ 

## Desafios
⚠️ 
⚠️ 

## Aprendizados
💡 
💡 

## Gratidão
🙏 
🙏 
🙏 

## Planos para amanhã
📅 
📅 
📅 
`
  },
  {
    id: 'project-planning',
    name: 'Planejamento de Projeto',
    icon: '🚀',
    content: `# Projeto: [Nome do Projeto]

## Visão Geral
**Objetivo:** 
**Prazo:** 
**Orçamento:** 

## Escopo
### Incluído
- 
- 
- 

### Não Incluído
- 
- 

## Fases do Projeto
### Fase 1: [Nome]
**Duração:** 
**Entregáveis:**
- 
- 

### Fase 2: [Nome]
**Duração:** 
**Entregáveis:**
- 
- 

## Recursos Necessários
- **Pessoas:** 
- **Tecnologia:** 
- **Orçamento:** 

## Riscos
| Risco | Probabilidade | Impacto | Mitigação |
|-------|---------------|---------|-----------|
|       |               |         |           |

## Cronograma
- [ ] Marco 1 - [Data]
- [ ] Marco 2 - [Data]
- [ ] Marco 3 - [Data]
`
  },
  {
    id: 'study-notes',
    name: 'Notas de Estudo',
    icon: '📚',
    content: `# [Matéria/Tópico] - ${new Date().toLocaleDateString('pt-BR')}

## Conceitos Principais
### Conceito 1
**Definição:** 
**Exemplo:** 
**Aplicação:** 

### Conceito 2
**Definição:** 
**Exemplo:** 
**Aplicação:** 

## Fórmulas/Regras Importantes
- 
- 
- 

## Exemplos Práticos
1. **Problema:** 
   **Solução:** 
   
2. **Problema:** 
   **Solução:** 

## Dúvidas
❓ 
❓ 
❓ 

## Para Revisar
- [ ] 
- [ ] 
- [ ] 

## Referências
- 
- 
- 
`
  },
  {
    id: 'recipe',
    name: 'Receita',
    icon: '🍳',
    content: `# [Nome da Receita]

## Informações
**Tempo de Preparo:** 
**Tempo de Cozimento:** 
**Porções:** 
**Dificuldade:** ⭐⭐⭐

## Ingredientes
- 
- 
- 
- 
- 

## Modo de Preparo
1. 
2. 
3. 
4. 
5. 

## Dicas
💡 
💡 
💡 

## Variações
- 
- 

## Notas
📝 
`
  },
  {
    id: 'travel-planning',
    name: 'Planejamento de Viagem',
    icon: '✈️',
    content: `# Viagem para [Destino]

## Informações Gerais
**Datas:** 
**Duração:** 
**Orçamento:** 
**Viajantes:** 

## Transporte
### Ida
**Meio:** 
**Horário:** 
**Valor:** 

### Volta
**Meio:** 
**Horário:** 
**Valor:** 

## Hospedagem
**Local:** 
**Check-in:** 
**Check-out:** 
**Valor:** 

## Roteiro
### Dia 1
- 
- 
- 

### Dia 2
- 
- 
- 

## O que levar
- [ ] Documentos
- [ ] Roupas
- [ ] Medicamentos
- [ ] Eletrônicos
- [ ] 

## Contatos Importantes
- **Hotel:** 
- **Emergência:** 
- **Seguro:** 

## Orçamento
| Item | Valor |
|------|-------|
| Transporte | |
| Hospedagem | |
| Alimentação | |
| Passeios | |
| **Total** | |
`
  },
  {
    id: 'book-review',
    name: 'Resenha de Livro',
    icon: '📖',
    content: `# [Título do Livro]

## Informações
**Autor:** 
**Gênero:** 
**Páginas:** 
**Ano:** 
**Avaliação:** ⭐⭐⭐⭐⭐

## Resumo
[Breve resumo da história/conteúdo]

## Personagens Principais
- **[Nome]:** 
- **[Nome]:** 
- **[Nome]:** 

## Temas Principais
- 
- 
- 

## Citações Marcantes
> "..."

> "..."

## O que gostei
✅ 
✅ 
✅ 

## O que não gostei
❌ 
❌ 

## Aprendizados
💡 
💡 
💡 

## Recomendaria?
[Sim/Não] - [Justificativa]

## Livros Similares
- 
- 
- 
`
  }
]

// Default note content
export const DEFAULT_NOTE_CONTENT = `# Bem-vindo ao NoteFlow! 🚀

Esta é sua primeira nota. Aqui você pode:

## ✨ Funcionalidades
- ✅ **Escrever** em Markdown
- ✅ **Organizar** com tags e categorias
- ✅ **Colaborar** em tempo real
- ✅ **Buscar** rapidamente
- ✅ **Sincronizar** na nuvem

## 🎯 Dicas Rápidas
1. Use **#tags** para organizar
2. Pressione **Ctrl+K** para comandos
3. **Ctrl+Shift+L** para captura rápida
4. Arraste e solte para reorganizar

## 🚀 Comece Agora
Clique em "Nova Nota" ou use um template!

---
*Feito com ❤️ pela equipe NoteFlow*`

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = [
  { key: 'Ctrl+N', action: 'Nova Nota', mac: '⌘N' },
  { key: 'Ctrl+K', action: 'Command Palette', mac: '⌘K' },
  { key: 'Ctrl+Shift+L', action: 'Quick Capture', mac: '⌘⇧L' },
  { key: 'Ctrl+S', action: 'Salvar', mac: '⌘S' },
  { key: 'Ctrl+F', action: 'Buscar', mac: '⌘F' },
  { key: 'Ctrl+B', action: 'Sidebar', mac: '⌘B' },
  { key: 'Ctrl+/', action: 'Ajuda', mac: '⌘/' },
  { key: 'Escape', action: 'Fechar Modal', mac: 'Escape' }
]

// API endpoints (for future use)
export const API_ENDPOINTS = {
  NOTES: '/api/notes',
  AUTH: '/api/auth',
  USERS: '/api/users',
  ANALYTICS: '/api/analytics',
  EXPORT: '/api/export',
  IMPORT: '/api/import'
}

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'noteflow_theme',
  SIDEBAR: 'noteflow_sidebar',
  VIEW_MODE: 'noteflow_view_mode',
  LAST_CATEGORY: 'noteflow_last_category',
  DRAFT: 'noteflow_draft',
  PREFERENCES: 'noteflow_preferences'
}

// Firebase collections
export const FIREBASE_COLLECTIONS = {
  USERS: 'users',
  NOTES: 'notes',
  TEMPLATES: 'templates',
  ANALYTICS: 'analytics',
  NOTIFICATIONS: 'notifications',
  SHARED: 'shared'
}

// Limits and constraints
export const LIMITS = {
  MAX_TITLE_LENGTH: 200,
  MAX_CONTENT_LENGTH: 1000000, // 1MB
  MAX_TAGS: 20,
  MAX_TAG_LENGTH: 50,
  MAX_COLLABORATORS: 10,
  MAX_COMMENTS: 100,
  MAX_VERSIONS: 50
}

// Animation durations (in ms)
export const ANIMATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000
}

// Breakpoints for responsive design
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
}
