{"common": {"save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "loading": "Carregando...", "search": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "yes": "<PERSON>m", "no": "Não", "ok": "OK", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "settings": "Configurações", "help": "<PERSON><PERSON><PERSON>", "about": "Sobre"}, "auth": {"login": "Entrar", "logout": "<PERSON><PERSON>", "register": "Registrar", "email": "E-mail", "password": "<PERSON><PERSON>", "confirmPassword": "Confirmar <PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON> a senha", "loginWithGoogle": "Entrar com Google", "loginWithGitHub": "Entrar com GitHub", "createAccount": "C<PERSON><PERSON> conta", "alreadyHaveAccount": "Já tem uma conta?", "dontHaveAccount": "Não tem uma conta?", "signInToAccount": "Entre na sua conta", "createNewAccount": "Crie uma nova conta"}, "notes": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON>", "category": "Categoria", "mood": "<PERSON><PERSON>", "tags": "Tags", "createNote": "<PERSON><PERSON><PERSON>", "editNote": "<PERSON><PERSON>", "deleteNote": "Excluir Nota", "saveNote": "<PERSON><PERSON>", "searchNotes": "Pesquisar notas...", "noNotes": "Nenhuma nota encontrada", "allNotes": "<PERSON><PERSON> as <PERSON><PERSON>", "recentNotes": "<PERSON><PERSON>", "favoriteNotes": "Notas Favori<PERSON>", "publicNotes": "Notas Públicas", "privateNotes": "<PERSON><PERSON>", "lockedNotes": "Notas Bloqueadas", "noteCreated": "Nota criada com sucesso!", "noteUpdated": "Nota atualizada com sucesso!", "noteDeleted": "Nota excluída com sucesso!", "confirmDelete": "Tem certeza que deseja excluir esta nota?", "lockNote": "Bloquear Nota", "unlockNote": "Desbloquear Nota", "enterLockCode": "Digite o código de bloqueio", "setLockCode": "Definir código de bloqueio", "lockCode": "Código de Bloqueio", "noteIsLocked": "Esta nota está bloqueada", "invalidLockCode": "Código de bloqueio inválido"}, "categories": {"category": "Categoria", "categories": "Categorias", "createCategory": "Criar Categoria", "editCategory": "Editar Categoria", "deleteCategory": "Excluir Categoria", "categoryName": "Nome da Categoria", "categoryColor": "<PERSON><PERSON> da Categoria", "categoryCreated": "Categoria criada com sucesso!", "categoryUpdated": "Categoria atualizada com sucesso!", "categoryDeleted": "Categoria excluída com sucesso!", "confirmDeleteCategory": "Tem certeza que deseja excluir esta categoria?", "noCategorySelected": "Nenhuma categoria selecionada", "manageCategories": "Gerenciar Categorias"}, "settings": {"settings": "Configurações", "personalizeExperience": "Personalize sua experiência no NoteFlow", "account": "Conta", "appearance": "Aparência", "notifications": "Notificações", "privacy": "Privacidade", "language": "Idioma", "theme": "<PERSON><PERSON>", "fontSize": "<PERSON><PERSON><PERSON>", "compactMode": "Modo Compacto", "darkMode": "<PERSON><PERSON>", "lightMode": "<PERSON><PERSON>", "systemMode": "Sistema", "small": "Pequeno", "medium": "Médio", "large": "Grande", "extraLarge": "Extra Grande", "enableNotifications": "Ativar Notificações", "emailNotifications": "Notificações por E-mail", "pushNotifications": "Notificaçõ<PERSON>", "soundEnabled": "<PERSON><PERSON>", "profileVisibility": "Visibilidade do Perfil", "dataCollection": "Coleta de Dados", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "public": "Público", "private": "Privado", "settingsSaved": "Configurações salvas com sucesso!", "displayName": "Nome de Exibição", "email": "E-mail", "updateProfile": "<PERSON><PERSON><PERSON><PERSON>", "profileUpdated": "Perfil atualizado com sucesso!"}, "header": {"noteflow": "NoteFlow", "subtitle": "Organize suas ideias", "dashboard": "<PERSON><PERSON>", "notes": "Notas", "categories": "Categorias", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Perfil", "logout": "<PERSON><PERSON>", "notifications": "Notificações", "noNotifications": "Nenhuma notificação", "markAllAsRead": "Marcar todas como lidas"}, "sidebar": {"allNotes": "<PERSON><PERSON> as <PERSON><PERSON>", "recentNotes": "<PERSON><PERSON>", "favoriteNotes": "Favoritas", "categories": "Categorias", "tags": "Tags", "archive": "Arquivo", "trash": "Lixeira", "hide": "Ocultar sidebar", "show": "Mostrar sidebar"}, "editor": {"bold": "Negrito", "italic": "Itálico", "underline": "<PERSON><PERSON><PERSON><PERSON>", "strikethrough": "Riscado", "heading": "<PERSON><PERSON><PERSON><PERSON>", "bulletList": "Lista com Marcadores", "numberedList": "Lista Numerada", "link": "Link", "image": "Imagem", "code": "Código", "quote": "Citação", "table": "<PERSON><PERSON><PERSON>", "formula": "<PERSON><PERSON><PERSON><PERSON>", "insertFormula": "<PERSON><PERSON><PERSON>", "preview": "Visualizar", "edit": "<PERSON><PERSON>", "fullscreen": "Tela Cheia", "exitFullscreen": "<PERSON><PERSON> <PERSON>"}, "homepage": {"welcome": "Bem-vindo ao Note<PERSON>low", "subtitle": "Sua plataforma de anotações inteligente", "description": "Organize seus pensamentos, ideias e conhecimento de forma eficiente com nossa ferramenta de anotações avançada.", "getStarted": "<PERSON><PERSON><PERSON>", "learnMore": "<PERSON><PERSON> Mai<PERSON>", "features": "Recursos", "feature1Title": "Editor <PERSON>", "feature1Description": "Editor de texto avançado com suporte a markdown, fórmulas matemáticas e formatação rica.", "feature2Title": "Organização Inteligente", "feature2Description": "Categorize e organize suas notas com tags, categorias e sistema de busca avançado.", "feature3Title": "Sincronização em Nuvem", "feature3Description": "Acesse suas notas de qualquer lugar com sincronização automática na nuvem."}, "errors": {"generic": "Ocorreu um erro inesperado", "networkError": "Erro de conexão com a internet", "authError": "Erro de autenticação", "notFound": "Não encontrado", "permissionDenied": "Permissão negada", "invalidInput": "Entrada inválida", "saveFailed": "<PERSON>alha ao salvar", "loadFailed": "Falha ao carregar", "deleteFailed": "Falha ao excluir"}}