{"common": {"save": "<PERSON><PERSON>", "saving": "Salvando...", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "loading": "Carregando...", "search": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "yes": "<PERSON>m", "no": "Não", "ok": "OK", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "settings": "Configurações", "help": "<PERSON><PERSON><PERSON>", "about": "Sobre"}, "auth": {"login": "Entrar", "logout": "<PERSON><PERSON>", "register": "Registrar", "email": "E-mail", "password": "<PERSON><PERSON>", "confirmPassword": "Confirmar <PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON> a senha", "loginWithGoogle": "Entrar com Google", "loginWithGitHub": "Entrar com GitHub", "createAccount": "C<PERSON><PERSON> conta", "alreadyHaveAccount": "Já tem uma conta?", "dontHaveAccount": "Não tem uma conta?", "signInToAccount": "Entre na sua conta", "createNewAccount": "Crie uma nova conta", "enterNoteFlow": "Entrar no Note<PERSON>low", "createAccountTitle": "<PERSON><PERSON><PERSON>", "recoverPassword": "<PERSON><PERSON><PERSON><PERSON>", "enterToAccess": "Entre para acessar suas notas e continuar organizando suas ideias", "createFreeAccount": "Crie sua conta gratuita e comece a transformar sua produtividade", "enterEmailRecover": "Digite seu email e enviaremos instruções para recuperar sua senha", "name": "Nome", "yourName": "Seu nome", "yourEmail": "<EMAIL>", "yourPassword": "<PERSON><PERSON> se<PERSON>a", "enterButton": "Entrar", "createFreeAccountButton": "<PERSON><PERSON><PERSON>", "sendEmailButton": "<PERSON><PERSON><PERSON>", "orContinueWith": "ou continue com", "continueWithGoogle": "Continuar com Google", "forgotPasswordQuestion": "Esque<PERSON>u a senha?", "newToNoteFlow": "Novo no NoteFlow?", "createFreeAccountLink": "<PERSON><PERSON><PERSON> conta gr<PERSON>", "alreadyHaveAccountQuestion": "Já tem uma conta?", "loginLink": "Fazer login", "backToLogin": "Voltar ao login", "authError": "Erro ao fazer autenticação", "googleAuthError": "Erro ao fazer login com Google", "recoveryEmailSent": "Email de recuperação enviado!"}, "notes": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON>", "category": "Categoria", "mood": "<PERSON><PERSON>", "tags": "Tags", "createNote": "<PERSON><PERSON><PERSON>", "editNote": "<PERSON><PERSON>", "deleteNote": "Excluir Nota", "saveNote": "<PERSON><PERSON>", "searchNotes": "Pesquisar notas...", "noNotes": "Nenhuma nota encontrada", "allNotes": "<PERSON><PERSON> as <PERSON><PERSON>", "recentNotes": "<PERSON><PERSON>", "favoriteNotes": "Notas Favori<PERSON>", "publicNotes": "Notas Públicas", "privateNotes": "<PERSON><PERSON>", "lockedNotes": "Notas Bloqueadas", "noteCreated": "Nota criada com sucesso!", "noteUpdated": "Nota atualizada com sucesso!", "noteDeleted": "Nota excluída com sucesso!", "confirmDelete": "Tem certeza que deseja excluir esta nota?", "lockNote": "Bloquear Nota", "unlockNote": "Desbloquear Nota", "enterLockCode": "Digite o código de bloqueio", "setLockCode": "Definir código de bloqueio", "lockCode": "Código de Bloqueio", "noteIsLocked": "Esta nota está bloqueada", "invalidLockCode": "Código de bloqueio inválido", "protected": "Protegida", "favorite": "Favorita", "pinned": "Fixada", "protectedNote": "Nota Protegida", "clickToEnterCode": "Clique para inserir código", "untitledNote": "Nota sem título", "words": "palavras", "share": "Compartilhar", "copy": "Copiar", "actionCannotBeUndone": "Esta ação não pode ser desfeita", "confirmDeleteMessage": "Tem certeza que deseja excluir a nota \"{{title}}\"?", "deleting": "Excluindo..."}, "categories": {"default": "Padrão", "cannotDeleteDefault": "Categorias padrão não podem ser deletadas", "deleteCategory": "Deletar categoria"}, "settings": {"settings": "Configurações", "personalizeExperience": "Personalize sua experiência no NoteFlow", "account": "Conta", "appearance": "Aparência", "notifications": "Notificações", "privacy": "Privacidade", "language": "Idioma", "theme": "<PERSON><PERSON>", "fontSize": "<PERSON><PERSON><PERSON>", "compactMode": "Modo Compacto", "darkMode": "<PERSON><PERSON>", "lightMode": "<PERSON><PERSON>", "systemMode": "Sistema", "small": "Pequeno", "medium": "Médio", "large": "Grande", "extraLarge": "Extra Grande", "enableNotifications": "Ativar Notificações", "emailNotifications": "Notificações por E-mail", "pushNotifications": "Notificaçõ<PERSON>", "soundEnabled": "<PERSON><PERSON>", "profileVisibility": "Visibilidade do Perfil", "dataCollection": "Coleta de Dados", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "public": "Público", "private": "Privado", "settingsSaved": "Configurações salvas com sucesso!", "displayName": "Nome de Exibição", "email": "E-mail", "updateProfile": "<PERSON><PERSON><PERSON><PERSON>", "profileUpdated": "Perfil atualizado com sucesso!", "accountInformation": "Informações da Conta", "emailCannotBeChanged": "O email não pode ser alterado", "accountStats": "Estatísticas da Conta", "notesCreated": "<PERSON><PERSON>", "wordsWritten": "Palavras Escritas", "accountActions": "Ações da Conta", "logoutAccount": "<PERSON><PERSON>", "deleteAccount": "Excluir Conta", "loggingOut": "Saindo...", "themeLight": "<PERSON><PERSON><PERSON>", "themeDark": "Escuro", "themeSystem": "Sistema", "fontSizeSmall": "Pequeno", "fontSizeMedium": "Médio", "fontSizeLarge": "Grande", "fontSizeExtraLarge": "Extra Grande", "compactModeDescription": "Reduz o espaçamento da interface", "generalNotifications": "Notificações Gerais", "generalNotificationsDescription": "Receber notificações sobre atividades", "emailNotificationsTitle": "Notificações por Email", "emailNotificationsDescription": "Receber resumos e atualizações por email", "pushNotificationsTitle": "Notificaçõ<PERSON>", "pushNotificationsDescription": "Notificações instantâneas no navegador", "soundNotifications": "Sons de Notificação", "soundNotificationsDescription": "Reproduzir sons para notificações", "privacySecurity": "Privacidade e Segurança", "profileVisibilityTitle": "Visibilidade do Perfil", "profileVisibilityPrivate": "Privado", "profileVisibilityFriends": "Apenas Amigos", "profileVisibilityPublic": "Público", "dataCollectionTitle": "Coleta de Dados", "dataCollectionDescription": "Permitir colet<PERSON> de dados para melhorar o serviço", "analyticsTitle": "Analytics", "analyticsDescription": "Compartilhar dados de uso anônimos"}, "header": {"noteflow": "NoteFlow", "subtitle": "Organize suas ideias", "focusMode": "<PERSON>do <PERSON>", "searchPlaceholder": "Buscar notas... (Ctrl+K para comando)", "enterFocusMode": "Entrar no modo foco (Ctrl+Shift+F)", "exitFocusMode": "Sair do modo foco (Ctrl+Shift+F)", "quickCapture": "Captura Rápida (Ctrl+Shift+L)", "commandPalette": "<PERSON><PERSON><PERSON> de Comandos (Ctrl+K)", "switchToList": "Mudar para lista", "switchToGrid": "<PERSON><PERSON> para grade", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "Notificações", "userMenu": "Menu do usuário", "user": "<PERSON><PERSON><PERSON><PERSON>", "mobileMenu": "Menu mobile", "newNote": "Nova Nota (Ctrl+N)", "capture": "Captura", "focus": "Foco", "dashboard": "<PERSON><PERSON>", "notes": "Notas", "categories": "Categorias", "profile": "Perfil", "logout": "<PERSON><PERSON>", "noNotifications": "Nenhuma notificação", "markAllAsRead": "Marcar todas como lidas"}, "sidebar": {"navigation": "Navegação", "allNotes": "<PERSON><PERSON> as <PERSON><PERSON>", "recentNotes": "<PERSON><PERSON>", "favoriteNotes": "Favoritas", "pinnedNotes": "<PERSON><PERSON><PERSON>", "protectedNotes": "Protegi<PERSON>", "publicNotes": "Públicas", "categories": "Categorias", "manageCategories": "Gerenciar Categorias", "uncategorized": "Sem categoria", "tags": "Tags", "moreTags": "mais tags...", "recentActivity": "Atividade Recente", "archive": "Arquivo", "trash": "Lixeira", "hide": "Ocultar sidebar", "show": "Mostrar sidebar"}, "editor": {"bold": "Negrito", "italic": "Itálico", "underline": "<PERSON><PERSON><PERSON><PERSON>", "strikethrough": "Riscado", "heading": "<PERSON><PERSON><PERSON><PERSON>", "bulletList": "Lista com Marcadores", "numberedList": "Lista Numerada", "link": "Link", "image": "Imagem", "code": "Código", "quote": "Citação", "table": "<PERSON><PERSON><PERSON>", "formula": "<PERSON><PERSON><PERSON><PERSON>", "insertFormula": "<PERSON><PERSON><PERSON>", "preview": "Visualizar", "edit": "<PERSON><PERSON>", "fullscreen": "Tela Cheia", "exitFullscreen": "<PERSON><PERSON> <PERSON>", "voiceRecognitionStarted": "Reconhecimento de voz iniciado", "voiceRecognitionStopped": "Reconhecimento de voz finalizado", "voiceCommandDetected": "Comando de voz detectado: parar gravação", "speechNotSupported": "Web Speech API não suportada neste navegador", "noSpeechDetected": "Nenhuma fala detectada. Tente falar mais alto.", "microphoneNotFound": "Microfone não encontrado ou sem permissão.", "microphonePermissionDenied": "Permissão para microfone negada.", "networkError": "Erro de rede. Verifique sua conexão.", "voiceRecognitionError": "Erro no reconhecimento de voz", "dateDetectedInTitle": "Data detectada no título!", "dateDetectedMessage": "Detectamos \"{dateText}\" no título. Deseja criar um lembrete para {date}?", "reminderText": "Lembre<PERSON>: {title}", "acceptReminder": "Aceitar", "dismissReminder": "Dispensar", "reminderAccepted": "Lembrete aceito:", "titleCleaned": "<PERSON><PERSON><PERSON><PERSON>:", "today": "hoje", "tomorrow": "aman<PERSON><PERSON>", "dayAfterTomorrow": "depois de aman<PERSON>", "nextWeek": "próxima semana", "nextMonth": "pró<PERSON><PERSON> mês", "monday": "segunda", "tuesday": "<PERSON><PERSON><PERSON>", "wednesday": "quarta", "thursday": "quinta", "friday": "sexta", "saturday": "s<PERSON>bad<PERSON>", "sunday": "domingo", "january": "j<PERSON><PERSON>", "february": "<PERSON><PERSON>", "march": "mar<PERSON><PERSON>", "april": "abril", "may": "maio", "june": "junho", "july": "julho", "august": "agosto", "september": "setembro", "october": "outubro", "november": "novembro", "december": "dezemb<PERSON>"}, "homepage": {"nav": {"features": "Funcionalidades", "testimonials": "<PERSON><PERSON><PERSON><PERSON>", "pricing": "Preços", "login": "Entrar", "startFree": "<PERSON><PERSON><PERSON>"}, "stats": {"activeUsers": "Usuários Ativos", "notesCreated": "<PERSON><PERSON>", "uptime": "Uptime", "rating": "Avaliação"}, "testimonials": {"ana": {"name": "<PERSON>", "role": "Estudante de Medicina", "text": "O NoteFlow revolucionou minha forma de estudar. O editor com fórmulas e tabelas é perfeito para minhas anotações médicas."}, "carlos": {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON>", "text": "A sincronização em tempo real e o mapa mental me ajudam a organizar projetos complexos de forma visual e eficiente."}, "maria": {"name": "<PERSON>", "role": "<PERSON><PERSON>", "text": "Uso o NoteFlow para preparar aulas e organizar conteúdo. As categorias personalizadas são um diferencial incrível."}}, "features": {"advancedEditor": {"title": "Editor <PERSON><PERSON><PERSON><PERSON>", "description": "Editor WYSIWYG com formatação rica, tabelas, fórmulas matemáticas e suporte a markdown.", "details": {"realTimeFormatting": "Formatação em tempo real", "dynamicTables": "Tabelas dinâ<PERSON>as", "mathFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "syntaxHighlighting": "Syntax highlighting", "keyboardShortcuts": "Atalhos de teclado"}}, "mindMap": {"title": "Mapa Mental", "description": "Visualize suas notas em um mapa mental interativo para melhor organização.", "details": {"interactiveVisualization": "Visualização interativa", "automaticConnections": "Conexões automáticas", "categoryFilters": "Filtros por categoria", "zoomNavigation": "Zoom e navegação", "imageExport": "Exportação de imagem"}}, "quickCapture": {"title": "Captura Rápida", "description": "Capture ideias instantaneamente com atalhos de teclado e comandos rápidos.", "details": {"customShortcuts": "<PERSON><PERSON><PERSON>", "commandPalette": "Comando palette", "readyTemplates": "Templates prontos", "voiceCapture": "Captura por voz", "quickNotes": "Quick notes"}}, "customCategories": {"title": "Categorias Personalizadas", "description": "Organize suas notas com categorias coloridas e ícones personalizados.", "details": {"customColors": "<PERSON>s personalizadas", "variedIcons": "Ícones variados", "defaultCategories": "Categorias pad<PERSON>", "advancedFilters": "Filtros a<PERSON>", "automaticOrganization": "Organização automática"}}, "smartSearch": {"title": "Busca Inteligente", "description": "Encontre qualquer nota rapidamente com busca por conteúdo, tags e categorias.", "details": {"realTimeSearch": "Busca em tempo real", "multipleFilters": "<PERSON><PERSON><PERSON>", "tagSearch": "Busca por tags", "searchHistory": "Histórico de busca", "relevantResults": "Resultados relevantes"}}, "synchronization": {"title": "Sincronização", "description": "Suas notas sempre sincronizadas em todos os dispositivos com Firebase.", "details": {"realTimeSync": "Sync em tempo real", "automaticBackup": "Backup automático", "offlineAccess": "Acesso offline", "multipleDevices": "<PERSON><PERSON><PERSON><PERSON> dispositivos", "guaranteedSecurity": "Segurança garantida"}}}}, "errors": {"generic": "Ocorreu um erro inesperado", "networkError": "Erro de conexão com a internet", "authError": "Erro de autenticação", "notFound": "Não encontrado", "permissionDenied": "Permissão negada", "invalidInput": "Entrada inválida", "saveFailed": "<PERSON>alha ao salvar", "loadFailed": "Falha ao carregar", "deleteFailed": "Falha ao excluir"}, "quickCapture": {"title": "Captura Rápida", "placeholder": "Capture sua ideia rapidamente...", "save": "<PERSON><PERSON>"}, "commandPalette": {"placeholder": "Digite um comando ou busque...", "noCommandsFound": "Nenhum comando encontrado", "navigationHelp": "Use ↑↓ para navegar, Enter para executar", "closeHelp": "Esc para fechar", "commands": {"newNote": "Nova Nota", "quickCapture": "Captura Rápida", "templates": "Templates", "focusMode": "<PERSON>do <PERSON>", "toggleSidebar": "Alternar <PERSON>bar", "toggleTheme": "<PERSON><PERSON><PERSON>", "analytics": "Analytics", "manageCategories": "Gerenciar Categorias", "searchNotes": "Buscar Notas"}, "categories": {"notes": "Notas", "interface": "Interface", "data": "<PERSON><PERSON>", "organization": "Organização", "navigation": "Navegação"}}, "templates": {"title": "Templates"}, "notifications": {"title": "Notificações", "filters": {"all": "<PERSON><PERSON>", "unread": "Não lidas", "read": "<PERSON><PERSON>"}, "typeFilter": "Todos os tipos", "actions": {"markAll": "<PERSON><PERSON>", "clearAll": "<PERSON><PERSON>", "markAsRead": "Marcar como lida", "deleteNotification": "Excluir notificação"}, "empty": {"title": "Nenhuma notificação", "unread": "Você não tem notificações não lidas", "read": "Você não tem notificações lidas", "all": "Você não tem notificações"}}}