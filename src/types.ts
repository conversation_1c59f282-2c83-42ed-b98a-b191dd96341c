// Core types for the NoteFlow application

export interface Note {
  id: string
  title: string
  content: string
  tags: string[]
  color: string
  starred: boolean
  pinned: boolean
  createdAt: Date
  updatedAt: Date
  category: string
  locked: boolean
  lockCode?: string // Código para desbloquear a nota
  mood: string
  wordCount: number
  readTime: number
  connections: string[]
  todos: Array<{ text: string; completed: boolean }>
  audioNote: string | null
  collaborators: Collaborator[]
  comments: Comment[]
  version: number
  isPublic: boolean
  permissions: 'owner' | 'edit' | 'view'
  userId: string
}

export interface Collaborator {
  id: string
  name: string
  email: string
  avatar: string
  permission: 'edit' | 'view'
  isOnline: boolean
}

export interface Comment {
  id: string
  user: string
  avatar: string
  text: string
  time: string
}

export interface Notification {
  id: string
  type: 'note_created' | 'note_updated' | 'note_deleted' | 'note_shared' | 'collaboration' | 'reminder' | 'system' | 'backup' | 'achievement' | 'comment'
  user: string
  message: string
  time: string
  read: boolean
  noteId?: string
  title?: string
  actionUrl?: string
  metadata?: Record<string, any>
}

export interface Template {
  id: string
  name: string
  icon: string | null
  content: string
}

export interface User {
  id: string
  name: string
  email: string
  avatar: string
  preferences: UserPreferences
}

export interface UserPreferences {
  theme: 'light' | 'dark'
  language: string
  notifications: boolean
  autoSave: boolean
  defaultCategory: string
}

export type ViewMode = 'grid' | 'list'
export type SortOption = 'updatedAt' | 'createdAt' | 'title' | 'wordCount'
export type FilterCategory = 'all' | 'pessoal' | 'trabalho' | 'estudos' | 'projetos'
export type Mood = 'happy' | 'neutral' | 'sad' | 'excited' | 'focused' | 'creative' | 'tired'

export interface AppState {
  notes: Note[]
  selectedNote: Note | null
  isEditing: boolean
  searchTerm: string
  filterCategory: string
  filterTag: string
  viewMode: ViewMode
  sortBy: SortOption
  darkMode: boolean
  showSidebar: boolean
  loading: boolean
}

// Firebase specific types
export interface FirebaseNote extends Omit<Note, 'createdAt' | 'updatedAt'> {
  createdAt: any // Firestore Timestamp
  updatedAt: any // Firestore Timestamp
}

export interface CreateNoteData {
  title: string
  content: string
  category: string
  tags: string[]
  color: string
  mood?: string
}

export interface UpdateNoteData extends Partial<CreateNoteData> {
  starred?: boolean
  pinned?: boolean
  locked?: boolean
  isPublic?: boolean
}

// Component prop types
export interface NoteCardProps {
  note: Note
  darkMode: boolean
  viewMode: ViewMode
  copiedId: string | null
  onEdit: (note: Note) => void
  onDelete: (noteId: string) => void
  onToggleStar: (noteId: string) => void
  onTogglePin: (noteId: string) => void
  onToggleLock: (noteId: string) => void
  onTogglePublic: (noteId: string) => void
  onCopy: (note: Note) => void
  onExport: (note: Note) => void
  onShare: (note: Note) => void
}

export interface HeaderProps {
  darkMode: boolean
  searchTerm: string
  setSearchTerm: (value: string) => void
  viewMode: ViewMode
  setViewMode: (value: ViewMode) => void
  showSidebar: boolean
  setShowSidebar: (value: boolean) => void
  setShowCommandPalette: (value: boolean) => void
  handleNewNote: () => void
  focusMode: boolean
  setFocusMode: (value: boolean) => void
  setShowAnalytics: (value: boolean) => void
  setShowQuickCapture: (value: boolean) => void
  notifications: Notification[]
  setShowNotifications: (value: boolean) => void
  showNotifications: boolean
  onLogout?: () => void
}

export interface SidebarProps {
  darkMode: boolean
  showSidebar: boolean
  setShowSidebar: (value: boolean) => void
  filterCategory: string
  setFilterCategory: (value: string) => void
  filterTag: string
  setFilterTag: (value: string) => void
  notes: Note[]
  allTags: string[]
}



export interface ModalProps {
  show: boolean
  darkMode: boolean
  onClose: () => void
}

export interface QuickCaptureModalProps extends ModalProps {
  quickCaptureText: string
  setQuickCaptureText: (value: string) => void
  onSave: () => void
}

export interface CommandPaletteModalProps extends ModalProps {
  onNewNote: () => void
  onQuickCapture: () => void
  onFocusMode: () => void
  onPomodoro: () => void
  onAnalytics: () => void
}

export interface TemplatesModalProps extends ModalProps {
  onUseTemplate: (template: Template) => void
}

export interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  darkMode: boolean
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// API Response types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// Analytics types
export interface AnalyticsData {
  totalNotes: number
  totalWords: number
  dailyStreak: number
  weeklyActivity: number[]
  categoryDistribution: Record<string, number>
  tagUsage: Record<string, number>
  productivityScore: number
  averageWordsPerNote: number
  mostActiveDay: string
  longestStreak: number
}

// Export/Import types
export interface ExportOptions {
  format: 'json' | 'markdown' | 'html' | 'pdf'
  includeMetadata: boolean
  includeComments: boolean
  dateRange?: {
    start: Date
    end: Date
  }
}

export interface ImportData {
  notes: Note[]
  metadata: {
    exportDate: Date
    version: string
    source: string
  }
}
