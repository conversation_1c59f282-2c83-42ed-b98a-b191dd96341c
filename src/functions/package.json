{"name": "noteflow-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for NoteFlow", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "lint": "eslint src/ --ext .js,.ts", "lint:fix": "eslint src/ --ext .js,.ts --fix"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^11.11.1", "firebase-functions": "^4.5.0", "openai": "^4.20.1", "nodemailer": "^6.9.7", "sharp": "^0.32.6", "pdf-lib": "^1.17.1", "mammoth": "^1.6.0", "jsdom": "^22.1.0", "turndown": "^7.1.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0"}, "devDependencies": {"@types/node": "^20.8.10", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/nodemailer": "^6.4.14", "@types/jsdom": "^21.1.6", "@types/turndown": "^5.0.4", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "jest": "^29.7.0", "typescript": "^5.2.2"}, "private": true}