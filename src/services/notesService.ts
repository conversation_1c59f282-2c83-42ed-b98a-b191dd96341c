import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove,
  writeBatch,
  Timestamp
} from 'firebase/firestore'
import { db } from '../config/firebase'
import type { Note } from '../types'
import toast from 'react-hot-toast'

// Simple encryption/decryption utilities for sensitive data
class CryptoUtils {
  // Simple hash function for password verification
  static async hashPassword(password: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hash = await crypto.subtle.digest('SHA-256', data);
    return btoa(String.fromCharCode(...new Uint8Array(hash)));
  }

  // Simple encryption using base64 encoding (for demo purposes)
  // In production, use proper encryption libraries
  static async encrypt(text: string, password: string): Promise<string> {
    try {
      // For now, just encode with base64 + password hash
      const encoded = btoa(text);
      const passwordHash = await this.hashPassword(password);
      return `${passwordHash.substring(0, 8)}:${encoded}`;
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  // Simple decryption
  static async decrypt(encryptedText: string, password: string): Promise<string> {
    try {
      const [hashPrefix, encoded] = encryptedText.split(':');
      const passwordHash = await this.hashPassword(password);

      // Verify password by checking hash prefix
      if (passwordHash.substring(0, 8) !== hashPrefix) {
        throw new Error('Invalid password');
      }

      return atob(encoded);
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data - incorrect password or corrupted data');
    }
  }
}

export interface CreateNoteData {
  title: string
  content: string
  category?: string // Categoria opcional - permite notas sem categoria
  tags: string[]
  color: string
  mood: string // Obrigatório, mas pode ser string vazia
  isPublic?: boolean // Status de privacidade opcional
  lockCode?: string // Código de bloqueio opcional
}

export interface UpdateNoteData extends Partial<CreateNoteData> {
  starred?: boolean
  pinned?: boolean
  locked?: boolean
  lockCode?: string
  isPublic?: boolean
}

// Interface para nota no Firestore (com dados criptografados)
export interface FirestoreNote {
  id?: string
  title: string
  content: string // Pode estar criptografado se locked=true
  category: string
  tags: string[]
  color: string
  mood: string
  starred: boolean
  pinned: boolean
  locked: boolean
  lockCodeHash?: string // Hash do código de bloqueio
  isPublic: boolean
  userId: string
  wordCount: number
  readTime: number
  createdAt: any // Firestore Timestamp
  updatedAt: any // Firestore Timestamp
  version: number
  collaborators: string[]
  connections: string[]
  todos: Array<{ text: string; completed: boolean }>
  audioNote: string | null
  comments: Array<{ id: string; text: string; userId: string; createdAt: any }>
  permissions: 'owner' | 'edit' | 'view'
}

export interface NotesQuery {
  userId: string
  search?: string
  category?: string
  tag?: string
  starred?: boolean
  pinned?: boolean
  isPublic?: boolean
  limit?: number
  lastDoc?: any
}

class NotesService {
  private notesCollection = collection(db, 'notes')

  // Create a new note
  async createNote(userId: string, data: CreateNoteData): Promise<Note> {
    try {
      const noteData = {
        ...data,
        category: data.category || '', // Permitir categoria vazia
        mood: data.mood || '', // Garantir que mood seja sempre string
        userId,
        starred: false,
        pinned: false,
        locked: false,
        isPublic: data.isPublic || false, // Usar valor passado ou false como padrão
        wordCount: this.calculateWordCount(data.content),
        readTime: this.calculateReadTime(data.content),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        version: 1,
        collaborators: [],
        connections: [],
        todos: this.extractTodos(data.content),
        audioNote: null,
        comments: [],
        permissions: 'owner'
      }

      const docRef = await addDoc(this.notesCollection, noteData)

      // Update user stats
      await this.updateUserStats(userId, {
        totalNotes: increment(1),
        totalWords: increment(noteData.wordCount)
      })

      // Create initial version
      await this.createNoteVersion(docRef.id, {
        title: data.title,
        content: data.content,
        version: 1
      })

      const createdNote = {
        id: docRef.id,
        ...noteData,
        createdAt: new Date(),
        updatedAt: new Date()
      } as Note

      toast.success('Nota criada com sucesso!')
      return createdNote
    } catch (error) {
      console.error('Error creating note:', error)
      toast.error('Erro ao criar nota')
      throw error
    }
  }

  // Update a note
  async updateNote(noteId: string, data: UpdateNoteData): Promise<void> {
    try {
      const noteRef = doc(this.notesCollection, noteId)
      const noteDoc = await getDoc(noteRef)

      if (!noteDoc.exists()) {
        throw new Error('Note not found')
      }

      const currentNote = noteDoc.data()

      const updateData: any = {
        ...data,
        updatedAt: serverTimestamp()
      }

      // Garantir que mood seja sempre string
      if (data.mood !== undefined) {
        updateData.mood = data.mood || '';
      }

      // Handle lock code encryption
      if (data.lockCode !== undefined) {
        if (data.lockCode) {
          // Encrypt content if locking with code
          if (data.content) {
            updateData.content = await CryptoUtils.encrypt(data.content, data.lockCode)
          } else if (currentNote.content && !currentNote.locked) {
            updateData.content = await CryptoUtils.encrypt(currentNote.content, data.lockCode)
          }
          // Store hash of lock code
          updateData.lockCodeHash = await CryptoUtils.hashPassword(data.lockCode)
          delete updateData.lockCode // Don't store the actual code
        } else {
          // Removing lock code - content should already be decrypted
          updateData.lockCodeHash = null
        }
      }

      // Update word count and read time if content changed
      if (data.content !== undefined) {
        updateData.wordCount = this.calculateWordCount(data.content)
        updateData.readTime = this.calculateReadTime(data.content)
        updateData.todos = this.extractTodos(data.content)
        updateData.version = increment(1)
      }

      await updateDoc(noteRef, updateData)

      // Create new version if content or title changed
      if (data.content !== undefined || data.title !== undefined) {
        await this.createNoteVersion(noteId, {
          title: data.title || currentNote.title,
          content: data.content || currentNote.content,
          version: (currentNote.version || 0) + 1
        })
      }

      toast.success('Nota atualizada com sucesso!')
    } catch (error) {
      console.error('Error updating note:', error)
      toast.error('Erro ao atualizar nota')
      throw error
    }
  }

  // Verify lock code
  async verifyLockCode(noteId: string, inputCode: string): Promise<boolean> {
    try {
      const noteRef = doc(this.notesCollection, noteId)
      const noteDoc = await getDoc(noteRef)

      if (!noteDoc.exists()) {
        throw new Error('Note not found')
      }

      const noteData = noteDoc.data()
      if (!noteData.locked || !noteData.lockCodeHash) {
        return false
      }

      // Hash the input code and compare with stored hash
      const inputHash = await CryptoUtils.hashPassword(inputCode)
      return inputHash === noteData.lockCodeHash
    } catch (error) {
      console.error('Error verifying lock code:', error)
      return false
    }
  }

  // Decrypt note content
  async decryptNoteContent(noteId: string, lockCode: string): Promise<string | null> {
    try {
      const noteRef = doc(this.notesCollection, noteId)
      const noteDoc = await getDoc(noteRef)

      if (!noteDoc.exists()) {
        throw new Error('Note not found')
      }

      const noteData = noteDoc.data()
      if (!noteData.locked || !noteData.content) {
        return noteData.content || null
      }

      // Verify code first
      const isValidCode = await this.verifyLockCode(noteId, lockCode)
      if (!isValidCode) {
        throw new Error('Invalid lock code')
      }

      // Decrypt content
      return await CryptoUtils.decrypt(noteData.content, lockCode)
    } catch (error) {
      console.error('Error decrypting note content:', error)
      return null
    }
  }

  // Delete a note
  async deleteNote(noteId: string): Promise<void> {
    try {
      const noteRef = doc(this.notesCollection, noteId)
      const noteDoc = await getDoc(noteRef)

      if (!noteDoc.exists()) {
        throw new Error('Note not found')
      }

      const noteData = noteDoc.data()

      // Use batch to delete note and its subcollections
      const batch = writeBatch(db)

      // Delete note
      batch.delete(noteRef)

      // Delete versions
      const versionsQuery = query(
        collection(db, 'notes', noteId, 'versions')
      )
      const versionsSnapshot = await getDocs(versionsQuery)
      versionsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref)
      })

      // Delete comments
      const commentsQuery = query(
        collection(db, 'notes', noteId, 'comments')
      )
      const commentsSnapshot = await getDocs(commentsQuery)
      commentsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref)
      })

      await batch.commit()

      // Update user stats
      if (noteData.userId) {
        await this.updateUserStats(noteData.userId, {
          totalNotes: increment(-1),
          totalWords: increment(-noteData.wordCount)
        })
      }

      toast.success('Nota deletada com sucesso!')
    } catch (error) {
      console.error('Error deleting note:', error)
      toast.error('Erro ao deletar nota')
      throw error
    }
  }

  // Get notes with real-time updates
  subscribeToNotes(
    queryParams: NotesQuery,
    callback: (notes: Note[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    try {
      let q = query(
        this.notesCollection,
        where('userId', '==', queryParams.userId),
        orderBy('pinned', 'desc'),
        orderBy('updatedAt', 'desc')
      )

      // Add filters
      if (queryParams.category) {
        q = query(q, where('category', '==', queryParams.category))
      }

      if (queryParams.starred !== undefined) {
        q = query(q, where('starred', '==', queryParams.starred))
      }

      if (queryParams.pinned !== undefined) {
        q = query(q, where('pinned', '==', queryParams.pinned))
      }

      if (queryParams.tag) {
        q = query(q, where('tags', 'array-contains', queryParams.tag))
      }

      if (queryParams.limit) {
        q = query(q, limit(queryParams.limit))
      }

      if (queryParams.lastDoc) {
        q = query(q, startAfter(queryParams.lastDoc))
      }

      return onSnapshot(
        q,
        (snapshot) => {
          const notes: any[] = []
          snapshot.forEach((doc) => {
            const data = doc.data()

            // Helper function to convert timestamp to Date
            const convertTimestamp = (timestamp: any): Date => {
              if (!timestamp) return new Date()
              if (timestamp.toDate && typeof timestamp.toDate === 'function') {
                return timestamp.toDate()
              }
              if (timestamp.seconds) {
                return new Date(timestamp.seconds * 1000)
              }
              if (timestamp instanceof Date) {
                return timestamp
              }
              return new Date(timestamp)
            }

            // Garantir que mood seja sempre string ao carregar
            const noteData = {
              id: doc.id,
              ...data,
              mood: data.mood || '', // Garantir string vazia se undefined/null
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }

            notes.push(noteData)
          })

          // Apply search filter (client-side for full-text search)
          let filteredNotes = notes
          if (queryParams.search) {
            const searchTerm = queryParams.search.toLowerCase()
            filteredNotes = notes.filter(note =>
              note.title.toLowerCase().includes(searchTerm) ||
              note.content.toLowerCase().includes(searchTerm) ||
              note.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm))
            )
          }

          callback(filteredNotes)
        },
        (error) => {
          console.error('Error in notes subscription:', error)
          if (onError) {
            onError(error as Error)
          } else {
            toast.error('Erro ao carregar notas')
          }
        }
      )
    } catch (error) {
      console.error('Error setting up notes subscription:', error)
      if (onError) {
        onError(error as Error)
      }
      return () => {}
    }
  }

  // Get public notes
  async getPublicNotes(limitCount = 20): Promise<Note[]> {
    try {
      const q = query(
        this.notesCollection,
        where('isPublic', '==', true),
        orderBy('updatedAt', 'desc'),
        limit(limitCount)
      )

      const snapshot = await getDocs(q)
      const notes: Note[] = []

      snapshot.forEach((doc) => {
        const data = doc.data()

        // Helper function to convert timestamp to Date
        const convertTimestamp = (timestamp: any): Date => {
          if (!timestamp) return new Date()
          if (timestamp.toDate && typeof timestamp.toDate === 'function') {
            return timestamp.toDate()
          }
          if (timestamp.seconds) {
            return new Date(timestamp.seconds * 1000)
          }
          if (timestamp instanceof Date) {
            return timestamp
          }
          return new Date(timestamp)
        }

        notes.push({
          id: doc.id,
          ...data,
          createdAt: convertTimestamp(data.createdAt),
          updatedAt: convertTimestamp(data.updatedAt)
        } as Note)
      })

      return notes
    } catch (error) {
      console.error('Error getting public notes:', error)
      toast.error('Erro ao carregar notas públicas')
      throw error
    }
  }

  // Toggle note star
  async toggleStar(noteId: string, userId: string): Promise<void> {
    try {
      const noteRef = doc(this.notesCollection, noteId)
      const noteDoc = await getDoc(noteRef)

      if (!noteDoc.exists()) {
        throw new Error('Note not found')
      }

      const currentNote = noteDoc.data()

      // Check permissions
      if (currentNote.userId !== userId && !currentNote.collaborators?.includes(userId)) {
        throw new Error('Permission denied')
      }

      await updateDoc(noteRef, {
        starred: !currentNote.starred,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error toggling star:', error)
      toast.error('Erro ao favoritar nota')
      throw error
    }
  }

  // Toggle note pin
  async togglePin(noteId: string, userId: string): Promise<void> {
    try {
      const noteRef = doc(this.notesCollection, noteId)
      const noteDoc = await getDoc(noteRef)

      if (!noteDoc.exists()) {
        throw new Error('Note not found')
      }

      const currentNote = noteDoc.data()

      // Check permissions
      if (currentNote.userId !== userId && !currentNote.collaborators?.includes(userId)) {
        throw new Error('Permission denied')
      }

      await updateDoc(noteRef, {
        pinned: !currentNote.pinned,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error toggling pin:', error)
      toast.error('Erro ao fixar nota')
      throw error
    }
  }

  // Add collaborator to note
  async addCollaborator(noteId: string, userId: string, collaboratorEmail: string): Promise<void> {
    try {
      const noteRef = doc(this.notesCollection, noteId)
      const noteDoc = await getDoc(noteRef)

      if (!noteDoc.exists()) {
        throw new Error('Note not found')
      }

      const currentNote = noteDoc.data()

      // Check permissions (only owner can add collaborators)
      if (currentNote.userId !== userId) {
        throw new Error('Permission denied')
      }

      // Find user by email
      const usersQuery = query(
        collection(db, 'users'),
        where('email', '==', collaboratorEmail),
        limit(1)
      )
      const usersSnapshot = await getDocs(usersQuery)

      if (usersSnapshot.empty) {
        throw new Error('User not found')
      }

      const collaboratorId = usersSnapshot.docs[0].id

      // Add collaborator
      await updateDoc(noteRef, {
        collaborators: arrayUnion(collaboratorId),
        updatedAt: serverTimestamp()
      })

      // Create notification for collaborator
      await this.createNotification(collaboratorId, {
        type: 'collaboration',
        title: 'Nova colaboração',
        message: `Você foi adicionado como colaborador na nota "${currentNote.title}"`,
        noteId: noteId,
        fromUserId: userId
      })

      toast.success('Colaborador adicionado com sucesso!')
    } catch (error) {
      console.error('Error adding collaborator:', error)
      toast.error('Erro ao adicionar colaborador')
      throw error
    }
  }

  // Create note version
  private async createNoteVersion(noteId: string, versionData: any): Promise<void> {
    try {
      const versionsCollection = collection(db, 'notes', noteId, 'versions')
      await addDoc(versionsCollection, {
        ...versionData,
        createdAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error creating note version:', error)
    }
  }

  // Update user stats
  private async updateUserStats(userId: string, stats: any): Promise<void> {
    try {
      const userRef = doc(db, 'users', userId)
      const updates: any = {}

      Object.entries(stats).forEach(([key, value]) => {
        updates[`stats.${key}`] = value
      })

      await updateDoc(userRef, updates)
    } catch (error) {
      console.error('Error updating user stats:', error)
    }
  }

  // Create notification
  private async createNotification(userId: string, notificationData: any): Promise<void> {
    try {
      const notificationsCollection = collection(db, 'notifications')
      await addDoc(notificationsCollection, {
        ...notificationData,
        userId,
        read: false,
        createdAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error creating notification:', error)
    }
  }

  // Utility functions
  private calculateWordCount(content: string): number {
    const text = content.replace(/<[^>]*>/g, '').trim()
    return text ? text.split(/\s+/).length : 0
  }

  private calculateReadTime(content: string): number {
    const wordCount = this.calculateWordCount(content)
    return Math.ceil(wordCount / 200) // 200 words per minute
  }

  private extractTodos(content: string): Array<{ text: string; completed: boolean }> {
    const todoRegex = /- \[([ x])\] (.+)/g
    const todos: Array<{ text: string; completed: boolean }> = []
    let match

    while ((match = todoRegex.exec(content)) !== null) {
      todos.push({
        text: match[2],
        completed: match[1] === 'x'
      })
    }

    return todos
  }
}

export { NotesService }
export const notesService = new NotesService()
