import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';

export interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  description?: string;
  isDefault?: boolean;
}

export interface CreateCategoryData {
  name: string;
  color: string;
  icon: string;
  description?: string;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {}

// Default categories that are created for new users
export const DEFAULT_CATEGORIES: Omit<Category, 'id' | 'userId' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Pessoal',
    color: '#3B82F6',
    icon: '👤',
    description: 'Notas pessoais e privadas',
    isDefault: true
  },
  {
    name: 'Trabal<PERSON>',
    color: '#10B981',
    icon: '💼',
    description: 'Notas relacionadas ao trabalho',
    isDefault: true
  },
  {
    name: 'Est<PERSON><PERSON>',
    color: '#8B5CF6',
    icon: '📚',
    description: 'Materiais de estudo e aprendizado',
    isDefault: true
  },
  {
    name: 'Projetos',
    color: '#F59E0B',
    icon: '🚀',
    description: 'Projetos pessoais e profissionais',
    isDefault: true
  },
  {
    name: 'Ideias',
    color: '#EF4444',
    icon: '💡',
    description: 'Ideias e inspirações',
    isDefault: true
  }
];

/**
 * Get all categories for a user
 */
export const getUserCategories = async (userId: string): Promise<Category[]> => {
  try {
    const categoriesRef = collection(db, 'categories');
    const q = query(
      categoriesRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'asc')
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Category[];
  } catch (error) {
    console.error('Error getting user categories:', error);
    throw error;
  }
};

/**
 * Subscribe to real-time updates of user categories
 */
export const subscribeToUserCategories = (
  userId: string,
  callback: (categories: Category[]) => void
) => {
  const categoriesRef = collection(db, 'categories');
  const q = query(
    categoriesRef,
    where('userId', '==', userId),
    orderBy('createdAt', 'asc')
  );

  return onSnapshot(q, (snapshot) => {
    const categories = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Category[];
    
    callback(categories);
  }, (error) => {
    console.error('Error in categories subscription:', error);
  });
};

/**
 * Create a new category
 */
export const createCategory = async (
  userId: string,
  categoryData: CreateCategoryData
): Promise<string> => {
  try {
    const categoriesRef = collection(db, 'categories');
    const now = Timestamp.now();
    
    const docRef = await addDoc(categoriesRef, {
      ...categoryData,
      userId,
      createdAt: now,
      updatedAt: now,
      isDefault: false
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
};

/**
 * Update an existing category
 */
export const updateCategory = async (
  categoryId: string,
  updates: UpdateCategoryData
): Promise<void> => {
  try {
    const categoryRef = doc(db, 'categories', categoryId);
    await updateDoc(categoryRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating category:', error);
    throw error;
  }
};

/**
 * Delete a category
 */
export const deleteCategory = async (categoryId: string): Promise<void> => {
  try {
    const categoryRef = doc(db, 'categories', categoryId);
    await deleteDoc(categoryRef);
  } catch (error) {
    console.error('Error deleting category:', error);
    throw error;
  }
};

/**
 * Create default categories for a new user
 */
export const createDefaultCategories = async (userId: string): Promise<void> => {
  try {
    const categoriesRef = collection(db, 'categories');
    const now = Timestamp.now();
    
    const promises = DEFAULT_CATEGORIES.map(category =>
      addDoc(categoriesRef, {
        ...category,
        userId,
        createdAt: now,
        updatedAt: now
      })
    );
    
    await Promise.all(promises);
  } catch (error) {
    console.error('Error creating default categories:', error);
    throw error;
  }
};

/**
 * Check if user has any categories
 */
export const userHasCategories = async (userId: string): Promise<boolean> => {
  try {
    const categoriesRef = collection(db, 'categories');
    const q = query(categoriesRef, where('userId', '==', userId));
    const snapshot = await getDocs(q);
    
    return !snapshot.empty;
  } catch (error) {
    console.error('Error checking user categories:', error);
    return false;
  }
};

/**
 * Get category by ID
 */
export const getCategoryById = async (categoryId: string): Promise<Category | null> => {
  try {
    const categoryRef = doc(db, 'categories', categoryId);
    const snapshot = await getDocs(query(collection(db, 'categories'), where('__name__', '==', categoryId)));
    
    if (snapshot.empty) {
      return null;
    }
    
    const categoryDoc = snapshot.docs[0];
    return {
      id: categoryDoc.id,
      ...categoryDoc.data(),
      createdAt: categoryDoc.data().createdAt?.toDate() || new Date(),
      updatedAt: categoryDoc.data().updatedAt?.toDate() || new Date()
    } as Category;
  } catch (error) {
    console.error('Error getting category by ID:', error);
    return null;
  }
};

/**
 * Bulk update categories (useful for reordering)
 */
export const bulkUpdateCategories = async (
  updates: Array<{ id: string; data: UpdateCategoryData }>
): Promise<void> => {
  try {
    const promises = updates.map(({ id, data }) => updateCategory(id, data));
    await Promise.all(promises);
  } catch (error) {
    console.error('Error bulk updating categories:', error);
    throw error;
  }
};

/**
 * Get categories count for a user
 */
export const getCategoriesCount = async (userId: string): Promise<number> => {
  try {
    const categoriesRef = collection(db, 'categories');
    const q = query(categoriesRef, where('userId', '==', userId));
    const snapshot = await getDocs(q);
    
    return snapshot.size;
  } catch (error) {
    console.error('Error getting categories count:', error);
    return 0;
  }
};
