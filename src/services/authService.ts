import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  GithubAuthProvider,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  User,
  UserCredential
} from 'firebase/auth'
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore'
import { auth, db } from '../config/firebase'
import toast from 'react-hot-toast'

export interface UserProfile {
  uid: string
  email: string
  displayName: string
  photoURL?: string
  createdAt: any
  updatedAt: any
  preferences: {
    theme: 'light' | 'dark'
    language: string
    notifications: boolean
    autoSave: boolean
    defaultCategory: string
  }
  subscription: {
    plan: 'free' | 'pro' | 'enterprise'
    status: 'active' | 'inactive' | 'cancelled'
    expiresAt?: any
  }
  stats: {
    totalNotes: number
    totalWords: number
    dailyStreak: number
    lastActiveAt: any
  }
}

class AuthService {
  // Google provider
  private googleProvider = new GoogleAuthProvider()
  
  // GitHub provider
  private githubProvider = new GithubAuthProvider()

  constructor() {
    // Configure providers
    this.googleProvider.addScope('profile')
    this.googleProvider.addScope('email')
    
    this.githubProvider.addScope('user:email')
  }

  // Sign up with email and password
  async signUp(email: string, password: string, displayName: string): Promise<UserCredential> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)
      
      // Update profile
      await updateProfile(userCredential.user, { displayName })
      
      // Create user document in Firestore
      await this.createUserProfile(userCredential.user, { displayName })
      
      toast.success('Conta criada com sucesso!')
      return userCredential
    } catch (error: any) {
      console.error('Sign up error:', error)
      this.handleAuthError(error)
      throw error
    }
  }

  // Sign in with email and password
  async signIn(email: string, password: string): Promise<UserCredential> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      
      // Update last active timestamp
      await this.updateLastActive(userCredential.user.uid)
      
      toast.success('Login realizado com sucesso!')
      return userCredential
    } catch (error: any) {
      console.error('Sign in error:', error)
      this.handleAuthError(error)
      throw error
    }
  }

  // Sign in with Google
  async signInWithGoogle(): Promise<UserCredential> {
    try {
      const userCredential = await signInWithPopup(auth, this.googleProvider)
      
      // Check if user profile exists, create if not
      const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid))
      if (!userDoc.exists()) {
        await this.createUserProfile(userCredential.user)
      } else {
        await this.updateLastActive(userCredential.user.uid)
      }
      
      toast.success('Login com Google realizado com sucesso!')
      return userCredential
    } catch (error: any) {
      console.error('Google sign in error:', error)
      this.handleAuthError(error)
      throw error
    }
  }

  // Sign in with GitHub
  async signInWithGitHub(): Promise<UserCredential> {
    try {
      const userCredential = await signInWithPopup(auth, this.githubProvider)
      
      // Check if user profile exists, create if not
      const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid))
      if (!userDoc.exists()) {
        await this.createUserProfile(userCredential.user)
      } else {
        await this.updateLastActive(userCredential.user.uid)
      }
      
      toast.success('Login com GitHub realizado com sucesso!')
      return userCredential
    } catch (error: any) {
      console.error('GitHub sign in error:', error)
      this.handleAuthError(error)
      throw error
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      await signOut(auth)
      toast.success('Logout realizado com sucesso!')
    } catch (error: any) {
      console.error('Sign out error:', error)
      toast.error('Erro ao fazer logout')
      throw error
    }
  }

  // Reset password
  async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email)
      toast.success('Email de recuperação enviado!')
    } catch (error: any) {
      console.error('Reset password error:', error)
      this.handleAuthError(error)
      throw error
    }
  }

  // Create user profile in Firestore
  private async createUserProfile(user: User, additionalData?: any): Promise<void> {
    const userProfile: UserProfile = {
      uid: user.uid,
      email: user.email!,
      displayName: additionalData?.displayName || user.displayName || 'Usuário',
      photoURL: user.photoURL,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      preferences: {
        theme: 'dark',
        language: 'pt-BR',
        notifications: true,
        autoSave: true,
        defaultCategory: 'pessoal'
      },
      subscription: {
        plan: 'free',
        status: 'active'
      },
      stats: {
        totalNotes: 0,
        totalWords: 0,
        dailyStreak: 0,
        lastActiveAt: serverTimestamp()
      }
    }

    await setDoc(doc(db, 'users', user.uid), userProfile)
  }

  // Update last active timestamp
  private async updateLastActive(uid: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', uid), {
        'stats.lastActiveAt': serverTimestamp(),
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error updating last active:', error)
    }
  }

  // Get user profile
  async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid))
      if (userDoc.exists()) {
        return userDoc.data() as UserProfile
      }
      return null
    } catch (error) {
      console.error('Error getting user profile:', error)
      return null
    }
  }

  // Update user profile
  async updateUserProfile(uid: string, data: Partial<UserProfile>): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', uid), {
        ...data,
        updatedAt: serverTimestamp()
      })
      toast.success('Perfil atualizado com sucesso!')
    } catch (error) {
      console.error('Error updating user profile:', error)
      toast.error('Erro ao atualizar perfil')
      throw error
    }
  }

  // Update user preferences
  async updateUserPreferences(uid: string, preferences: Partial<UserProfile['preferences']>): Promise<void> {
    try {
      const updates: any = {
        updatedAt: serverTimestamp()
      }
      
      Object.entries(preferences).forEach(([key, value]) => {
        updates[`preferences.${key}`] = value
      })
      
      await updateDoc(doc(db, 'users', uid), updates)
    } catch (error) {
      console.error('Error updating user preferences:', error)
      throw error
    }
  }

  // Update user stats
  async updateUserStats(uid: string, stats: Partial<UserProfile['stats']>): Promise<void> {
    try {
      const updates: any = {
        updatedAt: serverTimestamp()
      }
      
      Object.entries(stats).forEach(([key, value]) => {
        updates[`stats.${key}`] = value
      })
      
      await updateDoc(doc(db, 'users', uid), updates)
    } catch (error) {
      console.error('Error updating user stats:', error)
      throw error
    }
  }

  // Handle authentication errors
  private handleAuthError(error: any): void {
    switch (error.code) {
      case 'auth/user-disabled':
        toast.error('Conta desabilitada')
        break
      case 'auth/user-not-found':
        toast.error('Usuário não encontrado')
        break
      case 'auth/wrong-password':
        toast.error('Senha incorreta')
        break
      case 'auth/email-already-in-use':
        toast.error('Email já está em uso')
        break
      case 'auth/weak-password':
        toast.error('Senha muito fraca')
        break
      case 'auth/invalid-email':
        toast.error('Email inválido')
        break
      case 'auth/operation-not-allowed':
        toast.error('Operação não permitida')
        break
      case 'auth/popup-closed-by-user':
        toast.error('Login cancelado pelo usuário')
        break
      case 'auth/popup-blocked':
        toast.error('Popup bloqueado pelo navegador')
        break
      case 'auth/cancelled-popup-request':
        toast.error('Solicitação de popup cancelada')
        break
      case 'auth/network-request-failed':
        toast.error('Erro de conexão')
        break
      case 'auth/too-many-requests':
        toast.error('Muitas tentativas. Tente novamente mais tarde')
        break
      default:
        toast.error('Erro de autenticação')
        break
    }
  }
}

export const authService = new AuthService()
