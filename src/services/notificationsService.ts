import { 
  collection, 
  addDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot, 
  updateDoc, 
  doc, 
  serverTimestamp,
  deleteDoc,
  writeBatch
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Notification } from '../types';

export type NotificationType = 
  | 'note_created' 
  | 'note_updated' 
  | 'note_deleted'
  | 'note_shared'
  | 'collaboration'
  | 'reminder'
  | 'system'
  | 'backup'
  | 'achievement';

export interface CreateNotificationData {
  type: NotificationType;
  title: string;
  message: string;
  noteId?: string;
  fromUserId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export class NotificationsService {
  // Create a new notification
  async createNotification(userId: string, data: CreateNotificationData): Promise<string> {
    try {
      const notificationsCollection = collection(db, 'notifications');
      const docRef = await addDoc(notificationsCollection, {
        ...data,
        userId,
        read: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      console.log('✅ Notification created:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Error creating notification:', error);
      throw error;
    }
  }

  // Subscribe to user notifications
  subscribeToNotifications(
    userId: string, 
    callback: (notifications: Notification[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    try {
      const notificationsCollection = collection(db, 'notifications');
      const q = query(
        notificationsCollection,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          const notifications: Notification[] = [];
          
          snapshot.forEach((doc) => {
            const data = doc.data();
            
            // Convert Firestore timestamp to readable format
            const convertTimestamp = (timestamp: any): string => {
              if (!timestamp) return 'agora';
              
              let date: Date;
              if (timestamp.toDate && typeof timestamp.toDate === 'function') {
                date = timestamp.toDate();
              } else if (timestamp.seconds) {
                date = new Date(timestamp.seconds * 1000);
              } else if (timestamp instanceof Date) {
                date = timestamp;
              } else {
                date = new Date(timestamp);
              }

              const now = new Date();
              const diffMs = now.getTime() - date.getTime();
              const diffMins = Math.floor(diffMs / (1000 * 60));
              const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
              const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

              if (diffMins < 1) return 'agora';
              if (diffMins < 60) return `${diffMins} min atrás`;
              if (diffHours < 24) return `${diffHours}h atrás`;
              if (diffDays < 7) return `${diffDays}d atrás`;
              
              return date.toLocaleDateString('pt-BR');
            };

            const notification: Notification = {
              id: doc.id,
              type: data.type || 'system',
              user: data.fromUserId || 'Sistema',
              message: data.message || '',
              time: convertTimestamp(data.createdAt),
              read: data.read || false,
              noteId: data.noteId,
              title: data.title,
              actionUrl: data.actionUrl,
              metadata: data.metadata
            };

            notifications.push(notification);
          });

          callback(notifications);
        },
        (error) => {
          console.error('❌ Error in notifications subscription:', error);
          if (onError) {
            onError(error as Error);
          }
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error subscribing to notifications:', error);
      if (onError) {
        onError(error as Error);
      }
      return () => {};
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        read: true,
        updatedAt: serverTimestamp()
      });
      
      console.log('✅ Notification marked as read:', notificationId);
    } catch (error) {
      console.error('❌ Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read for a user
  async markAllAsRead(userId: string): Promise<void> {
    try {
      const notificationsCollection = collection(db, 'notifications');
      const q = query(
        notificationsCollection,
        where('userId', '==', userId),
        where('read', '==', false)
      );

      const batch = writeBatch(db);
      const snapshot = await q.get();
      
      snapshot.forEach((doc) => {
        batch.update(doc.ref, {
          read: true,
          updatedAt: serverTimestamp()
        });
      });

      await batch.commit();
      console.log('✅ All notifications marked as read for user:', userId);
    } catch (error) {
      console.error('❌ Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await deleteDoc(notificationRef);
      
      console.log('✅ Notification deleted:', notificationId);
    } catch (error) {
      console.error('❌ Error deleting notification:', error);
      throw error;
    }
  }

  // Delete all notifications for a user
  async deleteAllNotifications(userId: string): Promise<void> {
    try {
      const notificationsCollection = collection(db, 'notifications');
      const q = query(notificationsCollection, where('userId', '==', userId));

      const batch = writeBatch(db);
      const snapshot = await q.get();
      
      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log('✅ All notifications deleted for user:', userId);
    } catch (error) {
      console.error('❌ Error deleting all notifications:', error);
      throw error;
    }
  }

  // Helper methods for common notification types
  async notifyNoteCreated(userId: string, noteTitle: string, noteId: string): Promise<void> {
    await this.createNotification(userId, {
      type: 'note_created',
      title: 'Nova nota criada',
      message: `Nota "${noteTitle}" foi criada com sucesso`,
      noteId,
      actionUrl: `/note/${noteId}`
    });
  }

  async notifyNoteUpdated(userId: string, noteTitle: string, noteId: string): Promise<void> {
    await this.createNotification(userId, {
      type: 'note_updated',
      title: 'Nota atualizada',
      message: `Nota "${noteTitle}" foi atualizada`,
      noteId,
      actionUrl: `/note/${noteId}`
    });
  }

  async notifyNoteShared(userId: string, noteTitle: string, noteId: string, sharedWith: string): Promise<void> {
    await this.createNotification(userId, {
      type: 'note_shared',
      title: 'Nota compartilhada',
      message: `Nota "${noteTitle}" foi compartilhada com ${sharedWith}`,
      noteId,
      actionUrl: `/note/${noteId}`
    });
  }

  async notifyBackupCompleted(userId: string): Promise<void> {
    await this.createNotification(userId, {
      type: 'backup',
      title: 'Backup realizado',
      message: 'Backup automático das suas notas foi realizado com sucesso',
      metadata: { timestamp: new Date().toISOString() }
    });
  }

  async notifyAchievement(userId: string, achievement: string, description: string): Promise<void> {
    await this.createNotification(userId, {
      type: 'achievement',
      title: 'Conquista desbloqueada!',
      message: `${achievement}: ${description}`,
      metadata: { achievement }
    });
  }
}

export const notificationsService = new NotificationsService();
