import { Plugin, PluginContext, Disposable } from '../../core/PluginManager'

export const markdownPreviewPlugin: Plugin = {
  id: 'noteflow.markdown-preview',
  name: 'Markdown Preview',
  version: '1.0.0',
  description: 'Live preview for Markdown notes with syntax highlighting and custom themes',
  author: 'NoteFlow Team',
  homepage: 'https://github.com/noteflow/plugins/markdown-preview',
  keywords: ['markdown', 'preview', 'editor'],
  
  permissions: [
    {
      type: 'filesystem',
      description: 'Read markdown files and assets',
      required: true
    }
  ],

  contributes: {
    commands: [
      {
        command: 'markdown.preview.toggle',
        title: 'Toggle Markdown Preview',
        category: 'Markdown',
        icon: 'eye'
      },
      {
        command: 'markdown.preview.openSide',
        title: 'Open Preview to Side',
        category: 'Markdown',
        icon: 'split-horizontal'
      },
      {
        command: 'markdown.preview.export',
        title: 'Export as HTML',
        category: 'Markdown',
        icon: 'export'
      }
    ],
    
    menus: {
      editor: {
        title: [
          {
            command: 'markdown.preview.toggle',
            when: 'editorLangId == markdown',
            group: 'navigation'
          }
        ],
        context: [
          {
            command: 'markdown.preview.openSide',
            when: 'editorLangId == markdown',
            group: 'markdown'
          },
          {
            command: 'markdown.preview.export',
            when: 'editorLangId == markdown',
            group: 'markdown'
          }
        ]
      }
    },
    
    keybindings: [
      {
        command: 'markdown.preview.toggle',
        key: 'ctrl+shift+v',
        mac: 'cmd+shift+v',
        when: 'editorLangId == markdown'
      }
    ],

    configuration: {
      title: 'Markdown Preview',
      properties: {
        'markdown.preview.theme': {
          type: 'string',
          enum: ['github', 'dark', 'minimal', 'academic'],
          default: 'github',
          description: 'Preview theme'
        },
        'markdown.preview.fontSize': {
          type: 'number',
          default: 14,
          minimum: 8,
          maximum: 24,
          description: 'Font size in preview'
        },
        'markdown.preview.lineHeight': {
          type: 'number',
          default: 1.6,
          minimum: 1.0,
          maximum: 3.0,
          description: 'Line height in preview'
        },
        'markdown.preview.breaks': {
          type: 'boolean',
          default: false,
          description: 'Enable line breaks'
        },
        'markdown.preview.linkify': {
          type: 'boolean',
          default: true,
          description: 'Convert URLs to links'
        },
        'markdown.preview.typographer': {
          type: 'boolean',
          default: true,
          description: 'Enable typographic replacements'
        },
        'markdown.preview.math': {
          type: 'boolean',
          default: true,
          description: 'Enable math rendering (KaTeX)'
        },
        'markdown.preview.mermaid': {
          type: 'boolean',
          default: true,
          description: 'Enable Mermaid diagrams'
        }
      }
    }
  },

  async activate(context: PluginContext): Promise<void> {
    console.log('Activating Markdown Preview plugin...')

    // Register preview provider
    const previewProvider = new MarkdownPreviewProvider(context)
    
    // Register commands
    context.subscriptions.push(
      registerCommand('markdown.preview.toggle', () => previewProvider.toggle()),
      registerCommand('markdown.preview.openSide', () => previewProvider.openToSide()),
      registerCommand('markdown.preview.export', () => previewProvider.exportAsHTML()),
      
      // Register event listeners
      onDidChangeActiveEditor((editor) => {
        if (editor && isMarkdownFile(editor.document)) {
          previewProvider.updatePreview(editor.document)
        }
      }),
      
      onDidChangeTextDocument((event) => {
        if (isMarkdownFile(event.document)) {
          previewProvider.updatePreview(event.document)
        }
      })
    )

    // Initialize preview themes
    await previewProvider.loadThemes()
    
    console.log('Markdown Preview plugin activated')
  },

  async deactivate(): Promise<void> {
    console.log('Deactivating Markdown Preview plugin...')
    // Cleanup is handled by context.subscriptions disposal
  }
}

class MarkdownPreviewProvider {
  private previewPanel: WebviewPanel | null = null
  private currentDocument: TextDocument | null = null
  private themes: Map<string, string> = new Map()

  constructor(private context: PluginContext) {}

  async loadThemes(): Promise<void> {
    // Load built-in themes
    const themesPath = this.context.asAbsolutePath('themes')
    
    this.themes.set('github', await this.loadThemeCSS(`${themesPath}/github.css`))
    this.themes.set('dark', await this.loadThemeCSS(`${themesPath}/dark.css`))
    this.themes.set('minimal', await this.loadThemeCSS(`${themesPath}/minimal.css`))
    this.themes.set('academic', await this.loadThemeCSS(`${themesPath}/academic.css`))
  }

  private async loadThemeCSS(path: string): Promise<string> {
    try {
      // In real implementation, read from filesystem
      return `/* Theme CSS from ${path} */`
    } catch (error) {
      console.error(`Failed to load theme: ${path}`, error)
      return ''
    }
  }

  toggle(): void {
    if (this.previewPanel) {
      this.previewPanel.dispose()
      this.previewPanel = null
    } else {
      this.openPreview()
    }
  }

  openToSide(): void {
    this.openPreview(true)
  }

  private openPreview(preserveFocus = false): void {
    const activeEditor = getActiveTextEditor()
    if (!activeEditor || !isMarkdownFile(activeEditor.document)) {
      showErrorMessage('No active Markdown file')
      return
    }

    this.currentDocument = activeEditor.document
    
    this.previewPanel = createWebviewPanel(
      'markdownPreview',
      `Preview: ${path.basename(this.currentDocument.fileName)}`,
      preserveFocus ? ViewColumn.Beside : ViewColumn.Active,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          Uri.file(this.context.extensionPath)
        ]
      }
    )

    this.previewPanel.onDidDispose(() => {
      this.previewPanel = null
    })

    this.updatePreview(this.currentDocument)
  }

  async updatePreview(document: TextDocument): Promise<void> {
    if (!this.previewPanel || this.currentDocument !== document) {
      return
    }

    const content = await this.renderMarkdown(document.getText())
    this.previewPanel.webview.html = this.getWebviewContent(content)
  }

  private async renderMarkdown(text: string): Promise<string> {
    // Use markdown-it or similar library
    const md = new MarkdownIt({
      html: true,
      breaks: getConfiguration('markdown.preview.breaks'),
      linkify: getConfiguration('markdown.preview.linkify'),
      typographer: getConfiguration('markdown.preview.typographer')
    })

    // Add plugins
    if (getConfiguration('markdown.preview.math')) {
      md.use(require('markdown-it-katex'))
    }

    if (getConfiguration('markdown.preview.mermaid')) {
      md.use(require('markdown-it-mermaid'))
    }

    return md.render(text)
  }

  private getWebviewContent(markdownHtml: string): string {
    const theme = getConfiguration('markdown.preview.theme')
    const fontSize = getConfiguration('markdown.preview.fontSize')
    const lineHeight = getConfiguration('markdown.preview.lineHeight')
    
    const themeCSS = this.themes.get(theme) || this.themes.get('github')!

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Markdown Preview</title>
        <style>
          ${themeCSS}
          
          body {
            font-size: ${fontSize}px;
            line-height: ${lineHeight};
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          
          /* Syntax highlighting */
          pre code {
            display: block;
            padding: 1em;
            border-radius: 4px;
            overflow-x: auto;
          }
          
          /* Math rendering */
          .katex {
            font-size: 1.1em;
          }
          
          /* Mermaid diagrams */
          .mermaid {
            text-align: center;
            margin: 1em 0;
          }
        </style>
        
        <!-- KaTeX CSS -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css">
        
        <!-- Highlight.js CSS -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.8.0/build/styles/github.min.css">
      </head>
      <body>
        <div id="content">${markdownHtml}</div>
        
        <!-- KaTeX JS -->
        <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"></script>
        
        <!-- Highlight.js JS -->
        <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.8.0/build/highlight.min.js"></script>
        
        <!-- Mermaid JS -->
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.4.0/dist/mermaid.min.js"></script>
        
        <script>
          // Initialize syntax highlighting
          hljs.highlightAll();
          
          // Initialize Mermaid
          mermaid.initialize({ startOnLoad: true });
          
          // Auto-scroll to match editor cursor position
          window.addEventListener('message', event => {
            const message = event.data;
            if (message.type === 'scroll') {
              const element = document.querySelector(\`[data-line="\${message.line}"]\`);
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }
            }
          });
        </script>
      </body>
      </html>
    `
  }

  async exportAsHTML(): Promise<void> {
    if (!this.currentDocument) {
      showErrorMessage('No active Markdown file')
      return
    }

    const content = await this.renderMarkdown(this.currentDocument.getText())
    const html = this.getWebviewContent(content)
    
    const fileName = this.currentDocument.fileName.replace(/\.md$/, '.html')
    
    // In real implementation, show save dialog and write file
    showInformationMessage(`HTML exported to: ${fileName}`)
  }
}

// Mock functions (would be imported from VS Code API or similar)
function registerCommand(command: string, callback: Function): Disposable {
  return { dispose: () => {} }
}

function onDidChangeActiveEditor(callback: Function): Disposable {
  return { dispose: () => {} }
}

function onDidChangeTextDocument(callback: Function): Disposable {
  return { dispose: () => {} }
}

function getActiveTextEditor(): any {
  return null
}

function isMarkdownFile(document: any): boolean {
  return document?.languageId === 'markdown'
}

function createWebviewPanel(...args: any[]): any {
  return null
}

function showErrorMessage(message: string): void {
  console.error(message)
}

function showInformationMessage(message: string): void {
  console.log(message)
}

function getConfiguration(key: string): any {
  return null
}

// Mock classes
class MarkdownIt {
  constructor(options: any) {}
  use(plugin: any): this { return this }
  render(text: string): string { return text }
}
