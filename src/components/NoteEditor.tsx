import React, { useState, useRef, useEffect } from 'react';

// Web Speech API types
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
}

interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

declare var SpeechRecognition: {
  prototype: SpeechRecognition;
  new(): SpeechRecognition;
};
import {
  X,
  Save,
  Palette,
  Tag,
  Folder,
  Smile,
  Type,
  AlignLeft,
  Wand2,
  Mic,
  MicOff,
  Image,
  Link,
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Sparkles,
  Brain,
  Zap,
  FileText,
  Clock,
  Target,
  Lightbulb,
  BookOpen,
  CheckSquare,
  Calendar,
  MapPin,
  Users,
  Star,
  Hash,
  AtSign,
  MessageSquare,
  Paperclip,
  Download,
  Upload,
  Share2,
  Copy,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Globe,
  Shield,
  BarChart3,
  Underline,
  Strikethrough,
  Superscript,
  Subscript,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Indent,
  Outdent,
  Minus,
  MoreHorizontal,
  Table,
  Columns,
  Rows,
  Square,
  Circle,
  Triangle,
  Highlighter,
  PaintBucket,
  Scissors,
  ClipboardCopy,
  ClipboardPaste,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Maximize,
  Minimize,
  Settings,
  Sliders,
  Filter,
  Search,
  Replace,
  Regex,
  CaseSensitive,
  WholeWord,
  Check
} from 'lucide-react';
import { CATEGORIES, NOTE_COLORS, MOODS } from '../constants';

interface NoteEditorProps {
  show: boolean;
  darkMode: boolean;
  isNewNote: boolean;
  noteTitle: string;
  noteContent: string;
  noteCategory: string;
  noteTags: string[];
  noteColor: string;
  noteMood: string;
  noteIsPublic?: boolean;
  categories?: Array<{ id: string; name: string; icon: string; color: string; }>;
  onClose: () => void;
  onSave: () => void;
  setNoteTitle: (title: string) => void;
  setNoteContent: (content: string) => void;
  setNoteCategory: (category: string) => void;
  setNoteTags: (tags: string[]) => void;
  setNoteColor: (color: string) => void;
  setNoteMood: (mood: string) => void;
  setNoteIsPublic?: (isPublic: boolean) => void;
}

export const NoteEditor: React.FC<NoteEditorProps> = ({
  show,
  darkMode,
  isNewNote,
  noteTitle,
  noteContent,
  noteCategory,
  noteTags,
  noteColor,
  noteMood,
  noteIsPublic = false,
  categories,
  onClose,
  onSave,
  setNoteTitle,
  setNoteContent,
  setNoteCategory,
  setNoteTags,
  setNoteColor,
  setNoteMood,
  setNoteIsPublic
}) => {



  const handleCategoryChange = (newCategory: string) => {
    setNoteCategory(newCategory);
  };
  // Advanced features states
  const [isRecording, setIsRecording] = useState(false);
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [showFormatting, setShowFormatting] = useState(false);
  const [showAdvancedFormatting, setShowAdvancedFormatting] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);

  // Debug: Monitor noteMood changes
  useEffect(() => {
    console.log('🎭 noteMood mudou para:', noteMood, 'tipo:', typeof noteMood);
  }, [noteMood]);

  // Voice recording states
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [isListening, setIsListening] = useState(false);

  // Date detection and reminder states
  const [detectedDates, setDetectedDates] = useState<Array<{date: Date, text: string, type: string}>>([]);
  const [showDateSuggestion, setShowDateSuggestion] = useState(false);
  const [suggestedReminder, setSuggestedReminder] = useState<{date: Date, text: string} | null>(null);

  // Toast notification state
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');
  const [reminder, setReminder] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [wordCount, setWordCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);

  const [showFindReplace, setShowFindReplace] = useState(false);
  const [findText, setFindText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [textAlign, setTextAlign] = useState<'left' | 'center' | 'right' | 'justify'>('left');
  const [fontSize, setFontSize] = useState(14);
  const [lineHeight, setLineHeight] = useState(1.5);
  const [selectedColor, setSelectedColor] = useState('#000000');
  const [highlightColor, setHighlightColor] = useState('#ffff00');
  const [isRichEditor, setIsRichEditor] = useState(true);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const richEditorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update word count and reading time
  useEffect(() => {
    const words = noteContent.trim().split(/\s+/).filter(word => word.length > 0).length;
    setWordCount(words);
    setReadingTime(Math.ceil(words / 200) || 1);
  }, [noteContent]);

  // Sync rich editor with content (only when switching modes)
  useEffect(() => {
    if (richEditorRef.current && isRichEditor) {
      // Only update if content is different and we're switching to rich mode
      const currentHtml = richEditorRef.current.innerHTML;
      const expectedHtml = renderRichText(noteContent);

      if (currentHtml !== expectedHtml && noteContent) {
        console.log('🔄 Sincronizando conteúdo para editor rico');
        richEditorRef.current.innerHTML = expectedHtml;
      }
    }
  }, [isRichEditor]); // Only trigger when mode changes

  // Initialize rich editor content
  useEffect(() => {
    if (richEditorRef.current && isRichEditor && !richEditorRef.current.innerHTML && noteContent) {
      console.log('🎨 Inicializando editor rico com conteúdo');
      richEditorRef.current.innerHTML = renderRichText(noteContent);
    }
  }, [noteContent, isRichEditor]);

  // Initialize Speech Recognition
  useEffect(() => {
    // Always execute this effect, but only set up recognition if supported
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();

      recognitionInstance.continuous = true;
      recognitionInstance.interimResults = true;
      recognitionInstance.lang = 'pt-BR'; // Portuguese Brazil

      recognitionInstance.onstart = () => {
        console.log('🎙️ Reconhecimento de voz iniciado');
        setIsListening(true);
      };

      recognitionInstance.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        if (finalTranscript) {
          console.log('🎙️ Texto final transcrito:', finalTranscript);

          // Check for voice commands (more specific patterns)
          const lowerTranscript = finalTranscript.toLowerCase().trim();

          // Only stop if it's a clear command, not just containing the word
          const stopCommands = [
            'parar gravação',
            'parar a gravação',
            'finalizar gravação',
            'finalizar a gravação',
            'stop recording',
            'stop gravação',
            'encerrar gravação',
            'terminar gravação'
          ];

          const isStopCommand = stopCommands.some(command => {
            // Check if the transcript is exactly the command or ends with the command
            return lowerTranscript === command ||
                   lowerTranscript.endsWith(' ' + command) ||
                   lowerTranscript.startsWith(command + ' ') ||
                   (lowerTranscript.includes(command) && lowerTranscript.split(' ').length <= 3);
          });

          // Process the text first (before checking for stop command)
          let textToSave = finalTranscript.trim();

          // If it's a stop command, remove the command from the text but keep the rest
          if (isStopCommand) {
            console.log('🎙️ Comando de voz detectado: parar gravação');

            // Remove the stop command from the text to save
            for (const command of stopCommands) {
              if (lowerTranscript.includes(command)) {
                // Remove the command but keep any text before it
                const commandIndex = lowerTranscript.indexOf(command);
                if (commandIndex > 0) {
                  // There's text before the command, keep it
                  textToSave = finalTranscript.substring(0, commandIndex).trim();
                } else if (lowerTranscript.startsWith(command + ' ')) {
                  // Command is at the start, keep text after it
                  textToSave = finalTranscript.substring(command.length).trim();
                } else {
                  // Only the command, don't save anything
                  textToSave = '';
                }
                break;
              }
            }
          }

          // Add transcribed text to note content (if there's any text to save)
          if (textToSave) {
            if (isRichEditor && richEditorRef.current) {
              // Rich editor mode
              const currentContent = richEditorRef.current.textContent || '';
              const updatedContent = currentContent + (currentContent ? ' ' : '') + textToSave;
              richEditorRef.current.textContent = updatedContent;
              setNoteContent(updatedContent);

              // Position cursor at end
              const selection = window.getSelection();
              if (selection) {
                const range = document.createRange();
                range.selectNodeContents(richEditorRef.current);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
              }
            } else {
              // Markdown mode
              setNoteContent(prev => {
                const separator = prev && !prev.endsWith(' ') && !prev.endsWith('\n') ? ' ' : '';
                return prev + separator + textToSave;
              });

              // Position cursor at end in textarea
              setTimeout(() => {
                if (textareaRef.current) {
                  const newLength = (noteContent + textToSave).length;
                  textareaRef.current.setSelectionRange(newLength, newLength);
                  textareaRef.current.focus();
                }
              }, 10);
            }
          }

          // Stop recording if it was a stop command (after saving the text)
          if (isStopCommand) {
            setIsRecording(false);
            setIsListening(false);
            recognitionInstance.stop();
            return;
          }
        }

        // Show interim results in console for debugging
        if (interimTranscript) {
          console.log('🎙️ Texto interim:', interimTranscript);
        }
      };

      recognitionInstance.onerror = (event) => {
        console.error('🎙️ Erro no reconhecimento de voz:', event.error);
        setIsRecording(false);
        setIsListening(false);

        // Show user-friendly error messages
        let errorMessage = 'Erro no reconhecimento de voz';
        switch (event.error) {
          case 'no-speech':
            errorMessage = 'Nenhuma fala detectada. Tente falar mais alto.';
            break;
          case 'audio-capture':
            errorMessage = 'Microfone não encontrado ou sem permissão.';
            break;
          case 'not-allowed':
            errorMessage = 'Permissão para microfone negada.';
            break;
          case 'network':
            errorMessage = 'Erro de rede. Verifique sua conexão.';
            break;
        }

        alert(errorMessage);
      };

      recognitionInstance.onend = () => {
        console.log('🎙️ Reconhecimento de voz finalizado');
        setIsListening(false);
        if (isRecording) {
          // Restart if still recording (for continuous listening)
          setTimeout(() => {
            if (isRecording) {
              recognitionInstance.start();
            }
          }, 100);
        }
      };

      setRecognition(recognitionInstance);
    } else {
      console.warn('🎙️ Web Speech API não suportada neste navegador');
      setRecognition(null);
    }
  }, []);

  // Detect dates in title and suggest reminders
  useEffect(() => {
    if (noteTitle && noteTitle.length > 3) {
      const detectedDatesInTitle = detectDatesInText(noteTitle);
      setDetectedDates(detectedDatesInTitle);

      if (detectedDatesInTitle.length > 0) {
        const firstDate = detectedDatesInTitle[0];
        setSuggestedReminder({
          date: firstDate.date,
          text: `Lembrete: ${noteTitle}`
        });
        setShowDateSuggestion(true);
        console.log('📅 Data detectada no título:', firstDate);
      } else {
        setShowDateSuggestion(false);
        setSuggestedReminder(null);
      }
    } else {
      setDetectedDates([]);
      setShowDateSuggestion(false);
      setSuggestedReminder(null);
    }
  }, [noteTitle]);

  if (!show) return null;

  // Date detection function
  const detectDatesInText = (text: string) => {
    console.log('🔍 Detectando datas no texto:', text);
    const dates: Array<{date: Date, text: string, type: string}> = [];
    const today = new Date();
    const currentYear = today.getFullYear();

    // Patterns for different date formats
    const patterns = [
      // Relative dates
      {
        regex: /\b(hoje|today)\b/gi,
        handler: () => {
          const date = new Date();
          console.log('🔍 Detectado "hoje":', date);
          return date;
        },
        type: 'relative'
      },
      {
        regex: /\b(amanhã|amanha|tomorrow)\b/gi,
        handler: () => {
          const date = new Date();
          date.setDate(date.getDate() + 1);
          console.log('🔍 Detectado "amanhã":', date);
          return date;
        },
        type: 'relative'
      },
      {
        regex: /\b(depois de amanhã|day after tomorrow)\b/gi,
        handler: () => {
          const date = new Date();
          date.setDate(date.getDate() + 2);
          return date;
        },
        type: 'relative'
      },
      {
        regex: /\bem (\d+) dias?\b/gi,
        handler: (match: RegExpMatchArray) => {
          const days = parseInt(match[1]);
          const date = new Date();
          date.setDate(date.getDate() + days);
          return date;
        },
        type: 'relative'
      },
      {
        regex: /\bna próxima (segunda|terça|quarta|quinta|sexta|sábado|domingo)\b/gi,
        handler: (match: RegExpMatchArray) => {
          const weekdays = {
            'segunda': 1, 'terça': 2, 'quarta': 3, 'quinta': 4,
            'sexta': 5, 'sábado': 6, 'domingo': 0
          };
          const targetDay = weekdays[match[1].toLowerCase() as keyof typeof weekdays];
          const date = new Date();
          const currentDay = date.getDay();
          const daysUntil = (targetDay + 7 - currentDay) % 7 || 7;
          date.setDate(date.getDate() + daysUntil);
          return date;
        },
        type: 'weekday'
      },
      // Absolute dates
      {
        regex: /\b(\d{1,2})\/(\d{1,2})\/(\d{4})\b/g,
        handler: (match: RegExpMatchArray) => {
          const day = parseInt(match[1]);
          const month = parseInt(match[2]) - 1; // JS months are 0-based
          const year = parseInt(match[3]);
          return new Date(year, month, day);
        },
        type: 'absolute'
      },
      {
        regex: /\b(\d{1,2})\/(\d{1,2})\b/g,
        handler: (match: RegExpMatchArray) => {
          const day = parseInt(match[1]);
          const month = parseInt(match[2]) - 1;
          return new Date(currentYear, month, day);
        },
        type: 'absolute'
      },
      {
        regex: /\b(\d{1,2}) de (janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)\b/gi,
        handler: (match: RegExpMatchArray) => {
          const day = parseInt(match[1]);
          const months = {
            'janeiro': 0, 'fevereiro': 1, 'março': 2, 'abril': 3,
            'maio': 4, 'junho': 5, 'julho': 6, 'agosto': 7,
            'setembro': 8, 'outubro': 9, 'novembro': 10, 'dezembro': 11
          };
          const month = months[match[2].toLowerCase() as keyof typeof months];
          return new Date(currentYear, month, day);
        },
        type: 'absolute'
      },
      {
        regex: /\b(\d{1,2}) de (janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro) de (\d{4})\b/gi,
        handler: (match: RegExpMatchArray) => {
          const day = parseInt(match[1]);
          const months = {
            'janeiro': 0, 'fevereiro': 1, 'março': 2, 'abril': 3,
            'maio': 4, 'junho': 5, 'julho': 6, 'agosto': 7,
            'setembro': 8, 'outubro': 9, 'novembro': 10, 'dezembro': 11
          };
          const month = months[match[2].toLowerCase() as keyof typeof months];
          const year = parseInt(match[3]);
          return new Date(year, month, day);
        },
        type: 'absolute'
      }
    ];

    // Check each pattern
    patterns.forEach((pattern, index) => {
      console.log(`🔍 Testando padrão ${index + 1}:`, pattern.regex);
      let match;
      const regex = new RegExp(pattern.regex.source, pattern.regex.flags);
      while ((match = regex.exec(text)) !== null) {
        try {
          const date = pattern.handler(match);
          console.log('📅 Match encontrado:', match[0], 'Data:', date);
          if (date && date >= today) { // Include today and future dates
            dates.push({
              date,
              text: match[0],
              type: pattern.type
            });
            console.log('✅ Data adicionada:', date, pattern.type);
          } else {
            console.log('❌ Data rejeitada (passado):', date);
          }
        } catch (error) {
          console.log('❌ Erro ao processar data:', error);
        }
      }
    });

    console.log('📊 Total de datas encontradas:', dates.length);

    return dates;
  };

  // Handle reminder suggestion acceptance
  const acceptReminderSuggestion = () => {
    if (suggestedReminder && detectedDates.length > 0) {
      // Set time to 9:00 AM for the suggested date
      const reminderDate = new Date(suggestedReminder.date);
      reminderDate.setHours(9, 0, 0, 0);

      // Format for datetime-local input (YYYY-MM-DDTHH:MM)
      const year = reminderDate.getFullYear();
      const month = String(reminderDate.getMonth() + 1).padStart(2, '0');
      const day = String(reminderDate.getDate()).padStart(2, '0');
      const hours = String(reminderDate.getHours()).padStart(2, '0');
      const minutes = String(reminderDate.getMinutes()).padStart(2, '0');

      const datetimeLocalValue = `${year}-${month}-${day}T${hours}:${minutes}`;

      setReminder(datetimeLocalValue);

      // Remove the detected date text from the title
      let cleanedTitle = noteTitle;
      const detectedDateText = detectedDates[0].text;

      // Remove the date text and clean up extra spaces
      cleanedTitle = cleanedTitle.replace(new RegExp(detectedDateText, 'gi'), '');
      cleanedTitle = cleanedTitle.replace(/\s+/g, ' ').trim(); // Remove extra spaces

      // Update the title
      setNoteTitle(cleanedTitle);

      setShowDateSuggestion(false);
      setSuggestedReminder(null);

      console.log('✅ Lembrete aceito:', datetimeLocalValue);
      console.log('🧹 Título limpo:', cleanedTitle);

      // Show success message with readable format
      const readableDate = reminderDate.toLocaleDateString('pt-BR');
      const readableTime = reminderDate.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      });

      setTimeout(() => {
        showToastNotification(`📅 Lembrete criado para ${readableDate} às ${readableTime}`, 'success');
      }, 100);
    }
  };

  // Handle reminder suggestion rejection
  const rejectReminderSuggestion = () => {
    setShowDateSuggestion(false);
    setSuggestedReminder(null);
    showToastNotification('📅 Sugestão de lembrete dispensada', 'info');
    console.log('❌ Lembrete rejeitado');
  };

  // Show toast notification
  const showToastNotification = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);

    // Auto hide after 3 seconds
    setTimeout(() => {
      setShowToast(false);
    }, 3000);
  };

  // Math preview rendering function
  const renderMathPreview = (latex: string) => {
    // Remove $$ delimiters
    let content = latex.replace(/^\$\$|\$\$$/g, '').trim();

    if (!content) {
      return '<span style="color: #9ca3af; font-style: italic;">Digite uma fórmula matemática...</span>';
    }

    // Basic LaTeX to HTML conversion
    let html = content
      // Fractions
      .replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '<span style="display: inline-block; text-align: center; vertical-align: middle;"><span style="display: block; border-bottom: 1px solid currentColor; padding-bottom: 2px;">$1</span><span style="display: block; padding-top: 2px;">$2</span></span>')

      // Square root
      .replace(/\\sqrt\{([^}]+)\}/g, '<span style="position: relative;"><span style="border-top: 1px solid currentColor; padding-left: 2px;">$1</span><span style="position: absolute; left: -2px; top: -2px;">√</span></span>')

      // Superscript
      .replace(/\^(\w+|\{[^}]+\})/g, (match, p1) => {
        const content = p1.replace(/[{}]/g, '');
        return `<sup style="font-size: 0.8em;">${content}</sup>`;
      })

      // Subscript
      .replace(/_(\w+|\{[^}]+\})/g, (match, p1) => {
        const content = p1.replace(/[{}]/g, '');
        return `<sub style="font-size: 0.8em;">${content}</sub>`;
      })

      // Greek letters
      .replace(/\\alpha/g, 'α')
      .replace(/\\beta/g, 'β')
      .replace(/\\gamma/g, 'γ')
      .replace(/\\delta/g, 'δ')
      .replace(/\\epsilon/g, 'ε')
      .replace(/\\theta/g, 'θ')
      .replace(/\\lambda/g, 'λ')
      .replace(/\\mu/g, 'μ')
      .replace(/\\pi/g, 'π')
      .replace(/\\sigma/g, 'σ')
      .replace(/\\phi/g, 'φ')
      .replace(/\\omega/g, 'ω')
      .replace(/\\Gamma/g, 'Γ')
      .replace(/\\Delta/g, 'Δ')
      .replace(/\\Theta/g, 'Θ')
      .replace(/\\Lambda/g, 'Λ')
      .replace(/\\Sigma/g, 'Σ')
      .replace(/\\Phi/g, 'Φ')
      .replace(/\\Omega/g, 'Ω')

      // Mathematical operators
      .replace(/\\pm/g, '±')
      .replace(/\\mp/g, '∓')
      .replace(/\\times/g, '×')
      .replace(/\\div/g, '÷')
      .replace(/\\cdot/g, '·')
      .replace(/\\neq/g, '≠')
      .replace(/\\leq/g, '≤')
      .replace(/\\geq/g, '≥')
      .replace(/\\approx/g, '≈')
      .replace(/\\equiv/g, '≡')
      .replace(/\\propto/g, '∝')
      .replace(/\\infty/g, '∞')
      .replace(/\\partial/g, '∂')
      .replace(/\\nabla/g, '∇')

      // Integrals and sums
      .replace(/\\int/g, '∫')
      .replace(/\\sum/g, '∑')
      .replace(/\\prod/g, '∏')
      .replace(/\\lim/g, 'lim')

      // Limits and bounds
      .replace(/\\lim_\{([^}]+)\}/g, 'lim<sub style="font-size: 0.8em;">$1</sub>')
      .replace(/\\sum_\{([^}]+)\}\^\{([^}]+)\}/g, '∑<sub style="font-size: 0.8em;">$1</sub><sup style="font-size: 0.8em;">$2</sup>')
      .replace(/\\int_\{([^}]+)\}\^\{([^}]+)\}/g, '∫<sub style="font-size: 0.8em;">$1</sub><sup style="font-size: 0.8em;">$2</sup>')

      // Parentheses
      .replace(/\\left\(/g, '(')
      .replace(/\\right\)/g, ')')
      .replace(/\\left\[/g, '[')
      .replace(/\\right\]/g, ']')
      .replace(/\\left\{/g, '{')
      .replace(/\\right\}/g, '}')

      // Clean up remaining backslashes
      .replace(/\\/g, '');

    return `<span style="font-family: 'Times New Roman', serif; font-size: 18px; font-weight: normal;">${html}</span>`;
  };

  // Math solver function
  const solveMath = (expression: string) => {
    try {
      // Remove $$ delimiters and clean expression
      let expr = expression.replace(/^\$\$|\$\$$/g, '').trim();

      if (!expr) return null;

      // Simple arithmetic expressions
      const arithmeticMatch = expr.match(/^([0-9+\-*/().\s]+)$/);
      if (arithmeticMatch) {
        try {
          // Safe evaluation for basic arithmetic
          const result = Function('"use strict"; return (' + expr + ')')();
          if (typeof result === 'number' && !isNaN(result)) {
            return {
              type: 'arithmetic',
              input: expr,
              result: result,
              steps: [`Calculando: ${expr}`, `Resultado: ${result}`]
            };
          }
        } catch (e) {
          // Continue to other solvers
        }
      }

      // Quadratic equations: ax^2 + bx + c = 0
      const quadraticMatch = expr.match(/([+-]?\d*)\s*x\^2\s*([+-]?\d*)\s*x\s*([+-]?\d+)\s*=\s*0/);
      if (quadraticMatch) {
        const a = parseFloat(quadraticMatch[1] || '1');
        const b = parseFloat(quadraticMatch[2] || '0');
        const c = parseFloat(quadraticMatch[3] || '0');

        const discriminant = b * b - 4 * a * c;
        const steps = [
          `Equação quadrática: ${a}x² + ${b}x + ${c} = 0`,
          `Fórmula: x = (-b ± √(b²-4ac)) / 2a`,
          `Discriminante: Δ = b² - 4ac = ${b}² - 4(${a})(${c}) = ${discriminant}`
        ];

        if (discriminant > 0) {
          const x1 = (-b + Math.sqrt(discriminant)) / (2 * a);
          const x2 = (-b - Math.sqrt(discriminant)) / (2 * a);
          steps.push(`Duas raízes reais: x₁ = ${x1.toFixed(3)}, x₂ = ${x2.toFixed(3)}`);
          return {
            type: 'quadratic',
            input: expr,
            result: { x1, x2, discriminant },
            steps
          };
        } else if (discriminant === 0) {
          const x = -b / (2 * a);
          steps.push(`Uma raiz real: x = ${x.toFixed(3)}`);
          return {
            type: 'quadratic',
            input: expr,
            result: { x, discriminant },
            steps
          };
        } else {
          steps.push(`Sem raízes reais (Δ < 0)`);
          return {
            type: 'quadratic',
            input: expr,
            result: { discriminant },
            steps
          };
        }
      }

      // Linear equations: ax + b = c
      const linearMatch = expr.match(/([+-]?\d*)\s*x\s*([+-]?\d+)\s*=\s*([+-]?\d+)/);
      if (linearMatch) {
        const a = parseFloat(linearMatch[1] || '1');
        const b = parseFloat(linearMatch[2] || '0');
        const c = parseFloat(linearMatch[3] || '0');

        if (a !== 0) {
          const x = (c - b) / a;
          return {
            type: 'linear',
            input: expr,
            result: x,
            steps: [
              `Equação linear: ${a}x + ${b} = ${c}`,
              `Isolando x: ${a}x = ${c} - ${b} = ${c - b}`,
              `Resultado: x = ${c - b} / ${a} = ${x.toFixed(3)}`
            ]
          };
        }
      }

      // Derivatives: d/dx(x^n) = nx^(n-1)
      const derivativeMatch = expr.match(/d\/dx\s*\(\s*x\^(\d+)\s*\)/);
      if (derivativeMatch) {
        const n = parseInt(derivativeMatch[1]);
        if (n > 0) {
          const result = n === 1 ? '1' : `${n}x^${n-1}`;
          return {
            type: 'derivative',
            input: expr,
            result: result,
            steps: [
              `Derivada de x^${n}`,
              `Regra da potência: d/dx(x^n) = nx^(n-1)`,
              `Resultado: ${result}`
            ]
          };
        }
      }

      // Integrals: ∫x^n dx = x^(n+1)/(n+1) + C
      const integralMatch = expr.match(/\\int\s*x\^(\d+)\s*dx/);
      if (integralMatch) {
        const n = parseInt(integralMatch[1]);
        const newPower = n + 1;
        const result = `x^${newPower}/${newPower} + C`;
        return {
          type: 'integral',
          input: expr,
          result: result,
          steps: [
            `Integral de x^${n}`,
            `Regra da potência: ∫x^n dx = x^(n+1)/(n+1) + C`,
            `Resultado: ${result}`
          ]
        };
      }

      // Trigonometric values
      const trigMatch = expr.match(/(sin|cos|tan)\((\d+)\)/);
      if (trigMatch) {
        const func = trigMatch[1];
        const angle = parseInt(trigMatch[2]);
        const radians = (angle * Math.PI) / 180;
        let result;

        switch (func) {
          case 'sin':
            result = Math.sin(radians);
            break;
          case 'cos':
            result = Math.cos(radians);
            break;
          case 'tan':
            result = Math.tan(radians);
            break;
        }

        return {
          type: 'trigonometric',
          input: expr,
          result: result?.toFixed(4),
          steps: [
            `${func}(${angle}°)`,
            `Convertendo para radianos: ${angle}° = ${radians.toFixed(4)} rad`,
            `Resultado: ${result?.toFixed(4)}`
          ]
        };
      }

      return null;
    } catch (error) {
      console.error('Erro ao resolver fórmula:', error);
      return null;
    }
  };

  // Rich text rendering function
  const renderRichText = (text: string) => {
    let html = text
      // Escape HTML first
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')

      // Headers
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mt-4 mb-2">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mt-4 mb-2">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mt-4 mb-2">$1</h1>')

      // Bold and Italic
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold text-gray-900 dark:text-gray-100">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic text-gray-800 dark:text-gray-200">$1</em>')

      // Underline and Strikethrough
      .replace(/<u>(.*?)<\/u>/g, '<span class="underline text-gray-800 dark:text-gray-200">$1</span>')
      .replace(/~~(.*?)~~/g, '<span class="line-through text-gray-600 dark:text-gray-400">$1</span>')

      // Highlight
      .replace(/==(.*?)==/g, '<span class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded text-gray-900 dark:text-gray-100">$1</span>')

      // Code inline
      .replace(/`([^`]+)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-red-600 dark:text-red-400">$1</code>')

      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 dark:text-blue-400 underline hover:text-blue-800 dark:hover:text-blue-300">$1</a>')

      // Lists
      .replace(/^- (.*$)/gm, '<li class="ml-4 text-gray-800 dark:text-gray-200">• $1</li>')
      .replace(/^\d+\. (.*$)/gm, '<li class="ml-4 text-gray-800 dark:text-gray-200 list-decimal">$1</li>')

      // Checkboxes
      .replace(/^- \[ \] (.*$)/gm, '<div class="flex items-center gap-2 ml-4"><input type="checkbox" class="rounded" disabled> <span class="text-gray-800 dark:text-gray-200">$1</span></div>')
      .replace(/^- \[x\] (.*$)/gm, '<div class="flex items-center gap-2 ml-4"><input type="checkbox" checked class="rounded" disabled> <span class="text-gray-800 dark:text-gray-200 line-through">$1</span></div>')

      // Quotes
      .replace(/^> (.*$)/gm, '<blockquote class="border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic text-gray-700 dark:text-gray-300 my-2">$1</blockquote>')

      // Callouts
      .replace(/^> \[!NOTE\]\n> (.*$)/gm, '<div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-3 my-2"><div class="flex items-center gap-2 font-semibold text-blue-800 dark:text-blue-200 mb-1"><span>📝</span> Nota</div><div class="text-blue-700 dark:text-blue-300">$1</div></div>')
      .replace(/^> \[!WARNING\]\n> (.*$)/gm, '<div class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-3 my-2"><div class="flex items-center gap-2 font-semibold text-yellow-800 dark:text-yellow-200 mb-1"><span>⚠️</span> Aviso</div><div class="text-yellow-700 dark:text-yellow-300">$1</div></div>')
      .replace(/^> \[!INFO\]\n> (.*$)/gm, '<div class="bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400 p-3 my-2"><div class="flex items-center gap-2 font-semibold text-green-800 dark:text-green-200 mb-1"><span>ℹ️</span> Info</div><div class="text-green-700 dark:text-green-300">$1</div></div>')

      // Dividers
      .replace(/^---$/gm, '<hr class="border-gray-300 dark:border-gray-600 my-4">')

      // Math (basic)
      .replace(/\$\$(.*?)\$\$/g, '<div class="bg-gray-50 dark:bg-gray-800 p-2 rounded my-2 font-mono text-center text-purple-600 dark:text-purple-400">$1</div>')

      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre class="bg-gray-900 text-green-400 p-4 rounded-lg my-2 overflow-x-auto"><code>$2</code></pre>')

      // Line breaks
      .replace(/\n/g, '<br>');

    return html;
  };

  // Convert HTML to Markdown (preserving line breaks)
  const htmlToMarkdown = (html: string) => {
    if (!html || html.trim() === '') return '';

    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get clean text content first
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // If it's just plain text without HTML tags, return it directly
    if (!html.includes('<') || html === textContent) {
      return textContent;
    }

    // Process the HTML content preserving line breaks
    let markdown = html
      // Remove style attributes and classes first
      .replace(/\s+style="[^"]*"/gi, '')
      .replace(/\s+class="[^"]*"/gi, '')
      .replace(/\s+contenteditable="[^"]*"/gi, '')

      // Headers
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')

      // Text formatting (preserve nested content)
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
      .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
      .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
      .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
      .replace(/<u[^>]*>(.*?)<\/u>/gi, '<u>$1</u>')
      .replace(/<s[^>]*>(.*?)<\/s>/gi, '~~$1~~')

      // Links
      .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')

      // Code
      .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
      .replace(/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/gi, '```\n$1\n```')

      // Lists
      .replace(/<ul[^>]*>/gi, '')
      .replace(/<\/ul>/gi, '\n')
      .replace(/<ol[^>]*>/gi, '')
      .replace(/<\/ol>/gi, '\n')
      .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')

      // Blockquotes
      .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1\n\n')

      // Line breaks and paragraphs - PRESERVE LINE BREAKS
      .replace(/<br[^>]*\/?>/gi, '\n')
      .replace(/<p[^>]*>/gi, '')
      .replace(/<\/p>/gi, '\n\n')
      .replace(/<div[^>]*>/gi, '')
      .replace(/<\/div>/gi, '\n')
      .replace(/<span[^>]*>/gi, '')
      .replace(/<\/span>/gi, '')

      // Remove any remaining HTML tags
      .replace(/<[^>]*>/g, '')

      // Decode HTML entities
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      .replace(/&hellip;/g, '...')

      // Clean up excessive whitespace but preserve intentional line breaks
      .replace(/[ \t]+/g, ' ') // Replace multiple spaces/tabs with single space
      .replace(/\n[ \t]+/g, '\n') // Remove spaces at beginning of lines
      .replace(/[ \t]+\n/g, '\n') // Remove spaces at end of lines
      .replace(/\n{3,}/g, '\n\n') // Replace 3+ newlines with 2
      .replace(/^\s+|\s+$/g, ''); // Trim start and end

    // If conversion resulted in empty or significantly shorter content, fallback to text content
    if (!markdown || markdown.length < textContent.length * 0.3) {
      // Preserve line breaks in text content
      return textContent.replace(/\n/g, '\n');
    }

    return markdown;
  };

  // Auto-solve simple math expressions
  const autoSolveMath = (text: string, cursorPosition?: number, isUserTyping: boolean = true) => {
    // Only trigger auto-solve when user types "=" at the end of a math expression
    // Allow free editing when user is not actively typing at the end

    if (!isUserTyping) {
      return text; // Don't auto-solve during programmatic changes
    }

    // Find expressions like "2+3=" at the very end of the text
    const mathPattern = /(\d+(?:\.\d+)?(?:\s*[+\-*/()]\s*\d+(?:\.\d+)?)*(?:\s*[+\-*/]\s*\d+(?:\.\d+)?)+)\s*=\s*$/;

    // Only process if:
    // 1. Pattern matches at the end
    // 2. Cursor is at the very end (user just typed "=")
    // 3. There's no result already present
    const match = text.match(mathPattern);
    if (!match) {
      return text; // No math expression found
    }

    // Check if cursor is at the end (user just typed "=")
    if (cursorPosition !== undefined && cursorPosition !== text.length) {
      return text; // User is editing elsewhere, don't interfere
    }

    // Check if there's already a result (avoid re-calculating)
    const hasExistingResult = /(\d+(?:\.\d+)?(?:\s*[+\-*/()]\s*\d+(?:\.\d+)?)*(?:\s*[+\-*/]\s*\d+(?:\.\d+)?)+)\s*=\s*\d+/.test(text);
    if (hasExistingResult) {
      return text; // Already has a result, don't recalculate
    }

    try {
      const expression = match[1].trim();
      console.log('🧮 Detectada expressão matemática:', expression);

      // Validate expression (only allow numbers, operators, parentheses, spaces)
      if (!/^[\d+\-*/().\s]+$/.test(expression)) {
        return text;
      }

      // Safe evaluation for basic arithmetic
      const result = Function('"use strict"; return (' + expression + ')')();

      if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
        // Format result (remove unnecessary decimals)
        const formattedResult = result % 1 === 0 ? result.toString() : result.toFixed(3).replace(/\.?0+$/, '');
        console.log('✅ Resultado calculado:', formattedResult);

        // Replace the "expression=" with "expression=result"
        return text.replace(mathPattern, `${expression} = ${formattedResult}`);
      }
    } catch (error) {
      console.log('❌ Erro ao calcular:', error);
    }

    return text; // Return original if calculation fails
  };

  // Handle math calculation on key press
  const handleMathCalculation = (element: HTMLElement | HTMLTextAreaElement) => {
    const textContent = 'value' in element ? element.value : element.textContent || '';

    // Simple pattern: find "expression=" at the end
    const mathPattern = /(\d+(?:\.\d+)?(?:\s*[+\-*/()]\s*\d+(?:\.\d+)?)*(?:\s*[+\-*/]\s*\d+(?:\.\d+)?)+)\s*=\s*$/;
    const match = textContent.match(mathPattern);

    if (match) {
      try {
        const expression = match[1].trim();
        console.log('🧮 Calculando:', expression);

        // Validate expression
        if (!/^[\d+\-*/().\s]+$/.test(expression)) {
          return;
        }

        // Calculate
        const result = Function('"use strict"; return (' + expression + ')')();

        if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
          const formattedResult = result % 1 === 0 ? result.toString() : result.toFixed(3).replace(/\.?0+$/, '');
          const newText = textContent.replace(mathPattern, `${expression} = ${formattedResult}`);

          if ('value' in element) {
            // Textarea
            element.value = newText;
            setNoteContent(newText);
            // Position cursor at end
            setTimeout(() => {
              element.setSelectionRange(newText.length, newText.length);
            }, 0);
          } else {
            // Rich editor
            element.textContent = newText;
            setNoteContent(newText);
            // Position cursor at end
            setTimeout(() => {
              const selection = window.getSelection();
              if (selection) {
                const range = document.createRange();
                range.selectNodeContents(element);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
              }
            }, 0);
          }

          console.log('✅ Resultado:', formattedResult);
        }
      } catch (error) {
        console.log('❌ Erro ao calcular:', error);
      }
    }
  };

  // Handle rich editor input
  const handleRichEditorInput = (e: React.FormEvent<HTMLDivElement>) => {
    const htmlContent = e.currentTarget.innerHTML;
    const textContent = e.currentTarget.textContent || '';

    // Avoid processing if content is empty or just whitespace
    if (!textContent.trim()) {
      setNoteContent('');
      return;
    }

    // Convert HTML to Markdown for storage
    const markdownContent = htmlToMarkdown(htmlContent);

    // Use text content as fallback if markdown conversion fails
    const finalContent = markdownContent && markdownContent.trim() ? markdownContent : textContent;

    console.log('🔄 Convertendo HTML para Markdown:', {
      htmlContent: htmlContent.substring(0, 100) + '...',
      textContent: textContent.substring(0, 100) + '...',
      markdownContent: finalContent.substring(0, 100) + '...'
    });

    setNoteContent(finalContent);
  };

  // AI Functions
  const generateAISuggestions = async () => {
    console.log('🤖 Gerando sugestões AI...');
    setIsGeneratingAI(true);
    try {
      // Simulate AI API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const suggestions = [
        "Adicionar uma introdução mais detalhada",
        "Incluir exemplos práticos",
        "Organizar em seções com subtítulos",
        "Adicionar uma conclusão",
        "Incluir referências ou links úteis",
        "Melhorar a formatação com listas",
        "Adicionar tags relevantes: #produtividade #organização",
        "Considerar dividir em múltiplas notas"
      ];

      setAiSuggestions(suggestions.slice(0, Math.floor(Math.random() * 4) + 3));
    } catch (error) {
      console.error('Erro ao gerar sugestões AI:', error);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const generateAIContent = async (prompt: string) => {
    console.log('🤖 Gerando conteúdo AI para:', prompt);
    setIsGeneratingAI(true);
    try {
      // Simulate AI content generation
      await new Promise(resolve => setTimeout(resolve, 3000));

      const aiContent = {
        'resumo': `## Resumo\n\n${noteContent.substring(0, 200)}...\n\n### Pontos Principais:\n- Ponto 1\n- Ponto 2\n- Ponto 3`,
        'expandir': `${noteContent}\n\n## Detalhamento\n\nEsta seção expande os conceitos apresentados acima...\n\n### Análise Detalhada\n\nConsiderando os pontos mencionados...`,
        'melhorar': noteContent.replace(/\n/g, '\n\n').replace(/([.!?])\s/g, '$1\n\n'),
        'traduzir': `# ${noteTitle} (English)\n\n${noteContent.replace(/\b\w+/g, word => word + '_EN')}`,
        'outline': `# ${noteTitle}\n\n## I. Introdução\n   A. Contexto\n   B. Objetivos\n\n## II. Desenvolvimento\n   A. Ponto Principal 1\n   B. Ponto Principal 2\n\n## III. Conclusão\n   A. Resumo\n   B. Próximos Passos`
      };

      const generated = aiContent[prompt as keyof typeof aiContent] || noteContent;
      setNoteContent(generated);
    } catch (error) {
      console.error('Erro ao gerar conteúdo AI:', error);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // Voice Recording with Web Speech API

  const toggleRecording = async () => {
    console.log('🎙️ Toggle gravação:', isRecording ? 'Parar' : 'Iniciar');

    if (!recognition) {
      alert('Reconhecimento de voz não suportado neste navegador. Use Chrome, Edge ou Safari.');
      return;
    }

    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      setIsListening(false);
      recognition.stop();
      console.log('🎙️ Gravação parada');
    } else {
      try {
        // Start recording
        setIsRecording(true);
        recognition.start();
        console.log('🎙️ Gravação iniciada');
      } catch (error) {
        console.error('🎙️ Erro ao iniciar gravação:', error);
        setIsRecording(false);
        alert('Erro ao iniciar gravação. Verifique as permissões do microfone.');
      }
    }
  };

  // Add animation to button
  const animateButton = (buttonElement: HTMLElement) => {
    buttonElement.style.transform = 'scale(0.95)';
    buttonElement.style.transition = 'transform 0.1s ease';

    setTimeout(() => {
      buttonElement.style.transform = 'scale(1.05)';
      setTimeout(() => {
        buttonElement.style.transform = 'scale(1)';
      }, 100);
    }, 100);
  };

  // Enhanced formatting functions with animations
  const insertFormatting = (format: string) => {
    console.log('✏️ Inserindo formatação:', format);

    // Find and animate the clicked button
    const buttons = document.querySelectorAll('.formatting-btn');
    buttons.forEach(btn => {
      if (btn.getAttribute('data-format') === format) {
        animateButton(btn as HTMLElement);
      }
    });

    if (isRichEditor && richEditorRef.current) {
      // Rich editor formatting with visual effects
      richEditorRef.current.focus();

      try {
        const selection = window.getSelection();
        const range = selection?.getRangeAt(0);

        switch (format) {
          case 'bold':
            if (document.queryCommandSupported('bold')) {
              document.execCommand('bold', false);
              console.log('✅ Negrito aplicado visualmente');
            }
            return;

          case 'italic':
            if (document.queryCommandSupported('italic')) {
              document.execCommand('italic', false);
              console.log('✅ Itálico aplicado visualmente');
            }
            return;

          case 'underline':
            if (document.queryCommandSupported('underline')) {
              document.execCommand('underline', false);
              console.log('✅ Sublinhado aplicado visualmente');
            }
            return;

          case 'strikethrough':
            if (document.queryCommandSupported('strikeThrough')) {
              document.execCommand('strikeThrough', false);
              console.log('✅ Riscado aplicado visualmente');
            }
            return;

          case 'heading1':
            if (document.queryCommandSupported('formatBlock')) {
              document.execCommand('formatBlock', false, 'h1');
              console.log('✅ Título H1 aplicado visualmente');
            }
            return;

          case 'heading2':
            if (document.queryCommandSupported('formatBlock')) {
              document.execCommand('formatBlock', false, 'h2');
              console.log('✅ Título H2 aplicado visualmente');
            }
            return;

          case 'heading3':
            if (document.queryCommandSupported('formatBlock')) {
              document.execCommand('formatBlock', false, 'h3');
              console.log('✅ Título H3 aplicado visualmente');
            }
            return;

          case 'list':
            // Enhanced unordered list creation
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);

              // Try native command first
              if (document.queryCommandSupported('insertUnorderedList')) {
                document.execCommand('insertUnorderedList', false);
                console.log('✅ Lista aplicada com comando nativo');
              } else {
                // Fallback: create manual list
                const ul = document.createElement('ul');
                ul.style.cssText = `
                  margin: 8px 0;
                  padding-left: 20px;
                  list-style-type: disc;
                `;

                const li = document.createElement('li');
                li.style.cssText = `
                  margin: 4px 0;
                  color: ${darkMode ? '#e5e7eb' : '#374151'};
                `;

                const selectedText = range.toString();
                li.textContent = selectedText || 'Item da lista';
                ul.appendChild(li);

                if (selectedText) {
                  range.deleteContents();
                }
                range.insertNode(ul);

                // Position cursor in list item
                const newRange = document.createRange();
                newRange.selectNodeContents(li);
                newRange.collapse(false);
                selection.removeAllRanges();
                selection.addRange(newRange);

                console.log('✅ Lista aplicada manualmente');
              }
            }
            return;

          case 'orderedList':
            // Enhanced ordered list creation
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);

              // Try native command first
              if (document.queryCommandSupported('insertOrderedList')) {
                document.execCommand('insertOrderedList', false);
                console.log('✅ Lista numerada aplicada com comando nativo');
              } else {
                // Fallback: create manual list
                const ol = document.createElement('ol');
                ol.style.cssText = `
                  margin: 8px 0;
                  padding-left: 20px;
                  list-style-type: decimal;
                `;

                const li = document.createElement('li');
                li.style.cssText = `
                  margin: 4px 0;
                  color: ${darkMode ? '#e5e7eb' : '#374151'};
                `;

                const selectedText = range.toString();
                li.textContent = selectedText || 'Item da lista';
                ol.appendChild(li);

                if (selectedText) {
                  range.deleteContents();
                }
                range.insertNode(ol);

                // Position cursor in list item
                const newRange = document.createRange();
                newRange.selectNodeContents(li);
                newRange.collapse(false);
                selection.removeAllRanges();
                selection.addRange(newRange);

                console.log('✅ Lista numerada aplicada manualmente');
              }
            }
            return;
          case 'quote':
            // Enhanced blockquote creation
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString();

              const blockquote = document.createElement('blockquote');
              blockquote.className = 'rich-editor-quote';
              blockquote.style.cssText = `
                border-left: 4px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                padding: 12px 16px;
                margin: 16px 0;
                font-style: italic;
                background-color: ${darkMode ? '#1f2937' : '#f9fafb'};
                border-radius: 6px;
                color: ${darkMode ? '#9ca3af' : '#6b7280'};
              `;

              if (selectedText) {
                blockquote.textContent = selectedText;
                range.deleteContents();
                range.insertNode(blockquote);
              } else {
                blockquote.textContent = 'Digite sua citação aqui';
                range.insertNode(blockquote);
                // Position cursor inside blockquote
                const newRange = document.createRange();
                newRange.selectNodeContents(blockquote);
                selection.removeAllRanges();
                selection.addRange(newRange);
              }
              console.log('✅ Citação aplicada visualmente');
            }
            return;
          case 'code':
            // Enhanced code element creation
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString();

              const code = document.createElement('code');
              code.className = 'rich-editor-code';
              code.style.cssText = `
                background-color: ${darkMode ? '#374151' : '#f1f5f9'};
                color: ${darkMode ? '#fca5a5' : '#dc2626'};
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                font-weight: 500;
              `;

              if (selectedText) {
                code.textContent = selectedText;
                range.deleteContents();
                range.insertNode(code);
              } else {
                code.textContent = 'código';
                range.insertNode(code);
                // Position cursor after code
                const newRange = document.createRange();
                newRange.setStartAfter(code);
                newRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(newRange);
              }
              console.log('✅ Código aplicado visualmente');
            }
            return;
          case 'link':
            // Enhanced link creation
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString();

              const url = prompt('Digite a URL:', 'https://') || '';
              if (url && url !== 'https://') {
                const link = document.createElement('a');
                link.href = url;
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
                link.className = 'rich-editor-link';
                link.style.cssText = `
                  color: ${darkMode ? '#60a5fa' : '#2563eb'};
                  text-decoration: underline;
                  cursor: pointer;
                `;

                if (selectedText) {
                  link.textContent = selectedText;
                  range.deleteContents();
                  range.insertNode(link);
                } else {
                  link.textContent = 'Link';
                  range.insertNode(link);
                }
                console.log('✅ Link aplicado visualmente');
              }
            }
            return;

          case 'highlight':
            // Enhanced highlight creation
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString();

              const highlight = document.createElement('span');
              highlight.className = 'rich-editor-highlight';
              highlight.style.cssText = `
                background-color: ${darkMode ? '#fbbf24' : '#fef08a'};
                color: ${darkMode ? '#111827' : '#1f2937'};
                padding: 1px 3px;
                border-radius: 3px;
                font-weight: 500;
              `;

              if (selectedText) {
                highlight.textContent = selectedText;
                range.deleteContents();
                range.insertNode(highlight);
              } else {
                highlight.textContent = 'texto destacado';
                range.insertNode(highlight);
                // Position cursor after highlight
                const newRange = document.createRange();
                newRange.setStartAfter(highlight);
                newRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(newRange);
              }
              console.log('✅ Destaque aplicado visualmente');
            }
            return;

          case 'divider':
            // Create horizontal divider
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);

              const hr = document.createElement('hr');
              hr.className = 'rich-editor-divider';
              hr.style.cssText = `
                border: none;
                border-top: 2px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                margin: 24px 0;
                width: 100%;
              `;

              range.insertNode(hr);

              // Position cursor after divider
              const newRange = document.createRange();
              newRange.setStartAfter(hr);
              newRange.collapse(true);
              selection.removeAllRanges();
              selection.addRange(newRange);

              console.log('✅ Divisor aplicado visualmente');
            }
            return;

          case 'table':
            // Create enhanced interactive table
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);

              // Create table container
              const tableContainer = document.createElement('div');
              tableContainer.className = 'rich-editor-table-container';
              tableContainer.style.cssText = `
                margin: 16px 0;
                position: relative;
                border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                border-radius: 8px;
                overflow: hidden;
              `;

              // Create table controls
              const tableControls = document.createElement('div');
              tableControls.className = 'table-controls';
              tableControls.style.cssText = `
                background-color: ${darkMode ? '#374151' : '#f3f4f6'};
                padding: 8px 12px;
                border-bottom: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                display: flex;
                gap: 8px;
                align-items: center;
                font-size: 12px;
              `;

              // Add row button
              const addRowBtn = document.createElement('button');
              addRowBtn.textContent = '+ Linha';
              addRowBtn.style.cssText = `
                background: ${darkMode ? '#1f2937' : '#ffffff'};
                border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
              `;

              // Add column button
              const addColBtn = document.createElement('button');
              addColBtn.textContent = '+ Coluna';
              addColBtn.style.cssText = `
                background: ${darkMode ? '#1f2937' : '#ffffff'};
                border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
              `;

              // Remove row button
              const removeRowBtn = document.createElement('button');
              removeRowBtn.textContent = '- Linha';
              removeRowBtn.style.cssText = `
                background: ${darkMode ? '#7f1d1d' : '#fef2f2'};
                border: 1px solid ${darkMode ? '#991b1b' : '#fecaca'};
                color: ${darkMode ? '#fca5a5' : '#dc2626'};
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
              `;

              // Remove column button
              const removeColBtn = document.createElement('button');
              removeColBtn.textContent = '- Coluna';
              removeColBtn.style.cssText = `
                background: ${darkMode ? '#7f1d1d' : '#fef2f2'};
                border: 1px solid ${darkMode ? '#991b1b' : '#fecaca'};
                color: ${darkMode ? '#fca5a5' : '#dc2626'};
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
              `;

              tableControls.appendChild(addRowBtn);
              tableControls.appendChild(addColBtn);
              tableControls.appendChild(removeRowBtn);
              tableControls.appendChild(removeColBtn);

              // Create the actual table
              const table = document.createElement('table');
              table.className = 'rich-editor-table';
              table.style.cssText = `
                border-collapse: collapse;
                width: 100%;
                background: ${darkMode ? '#1f2937' : '#ffffff'};
              `;

              // Create header row
              const thead = document.createElement('thead');
              const headerRow = document.createElement('tr');
              for (let i = 0; i < 3; i++) {
                const th = document.createElement('th');
                th.contentEditable = 'true';
                th.style.cssText = `
                  border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                  padding: 8px 12px;
                  background-color: ${darkMode ? '#374151' : '#f9fafb'};
                  font-weight: 600;
                  min-width: 100px;
                `;
                th.textContent = `Coluna ${i + 1}`;
                headerRow.appendChild(th);
              }
              thead.appendChild(headerRow);
              table.appendChild(thead);

              // Create body rows
              const tbody = document.createElement('tbody');
              for (let row = 0; row < 2; row++) {
                const tr = document.createElement('tr');
                for (let col = 0; col < 3; col++) {
                  const td = document.createElement('td');
                  td.contentEditable = 'true';
                  td.style.cssText = `
                    border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                    padding: 8px 12px;
                    min-width: 100px;
                    background: ${darkMode ? '#1f2937' : '#ffffff'};
                  `;
                  td.textContent = `Célula ${row + 1}-${col + 1}`;
                  tr.appendChild(td);
                }
                tbody.appendChild(tr);
              }
              table.appendChild(tbody);

              // Add event listeners for table controls
              addRowBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const tbody = table.querySelector('tbody');
                const colCount = table.querySelector('thead tr').children.length;
                const newRow = document.createElement('tr');

                for (let i = 0; i < colCount; i++) {
                  const td = document.createElement('td');
                  td.contentEditable = 'true';
                  td.style.cssText = `
                    border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                    padding: 8px 12px;
                    min-width: 100px;
                    background: ${darkMode ? '#1f2937' : '#ffffff'};
                  `;
                  td.textContent = 'Nova célula';
                  newRow.appendChild(td);
                }
                tbody.appendChild(newRow);
                console.log('✅ Linha adicionada à tabela');
              });

              addColBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const headerRow = table.querySelector('thead tr');
                const bodyRows = table.querySelectorAll('tbody tr');

                // Add header cell
                const newTh = document.createElement('th');
                newTh.contentEditable = 'true';
                newTh.style.cssText = `
                  border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                  padding: 8px 12px;
                  background-color: ${darkMode ? '#374151' : '#f9fafb'};
                  font-weight: 600;
                  min-width: 100px;
                `;
                newTh.textContent = `Coluna ${headerRow.children.length + 1}`;
                headerRow.appendChild(newTh);

                // Add body cells
                bodyRows.forEach((row, index) => {
                  const newTd = document.createElement('td');
                  newTd.contentEditable = 'true';
                  newTd.style.cssText = `
                    border: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
                    padding: 8px 12px;
                    min-width: 100px;
                    background: ${darkMode ? '#1f2937' : '#ffffff'};
                  `;
                  newTd.textContent = 'Nova célula';
                  row.appendChild(newTd);
                });
                console.log('✅ Coluna adicionada à tabela');
              });

              removeRowBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const tbody = table.querySelector('tbody');
                if (tbody.children.length > 1) {
                  tbody.removeChild(tbody.lastElementChild);
                  console.log('✅ Linha removida da tabela');
                }
              });

              removeColBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const headerRow = table.querySelector('thead tr');
                const bodyRows = table.querySelectorAll('tbody tr');

                if (headerRow.children.length > 1) {
                  // Remove header cell
                  headerRow.removeChild(headerRow.lastElementChild);

                  // Remove body cells
                  bodyRows.forEach(row => {
                    if (row.children.length > 0) {
                      row.removeChild(row.lastElementChild);
                    }
                  });
                  console.log('✅ Coluna removida da tabela');
                }
              });

              // Assemble table container
              tableContainer.appendChild(tableControls);
              tableContainer.appendChild(table);

              // Insert table and add spacing
              range.insertNode(tableContainer);

              // Add line breaks after table for writing space
              const lineBreak1 = document.createElement('br');
              const lineBreak2 = document.createElement('br');
              const paragraph = document.createElement('p');
              paragraph.innerHTML = '&nbsp;'; // Non-breaking space

              // Position cursor after table
              const afterTableRange = document.createRange();
              afterTableRange.setStartAfter(tableContainer);
              afterTableRange.insertNode(lineBreak1);
              afterTableRange.setStartAfter(lineBreak1);
              afterTableRange.insertNode(lineBreak2);
              afterTableRange.setStartAfter(lineBreak2);
              afterTableRange.insertNode(paragraph);

              // Set cursor in the paragraph
              const newRange = document.createRange();
              newRange.selectNodeContents(paragraph);
              newRange.collapse(true);
              selection.removeAllRanges();
              selection.addRange(newRange);

              console.log('✅ Tabela interativa aplicada com espaçamento');
            }
            return;

          case 'checkbox':
            // Create checkbox list item
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString();

              const checkboxContainer = document.createElement('div');
              checkboxContainer.className = 'rich-editor-checkbox';
              checkboxContainer.style.cssText = `
                display: flex;
                align-items: center;
                gap: 8px;
                margin: 4px 0;
              `;

              const checkbox = document.createElement('input');
              checkbox.type = 'checkbox';
              checkbox.style.cssText = `
                margin: 0;
                cursor: pointer;
              `;

              const label = document.createElement('span');
              label.textContent = selectedText || 'Item da lista de tarefas';
              label.style.cssText = `
                cursor: pointer;
              `;

              checkboxContainer.appendChild(checkbox);
              checkboxContainer.appendChild(label);

              if (selectedText) {
                range.deleteContents();
              }
              range.insertNode(checkboxContainer);

              console.log('✅ Checkbox aplicado visualmente');
            }
            return;

          case 'codeblock':
            // Create code block element
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString();

              const pre = document.createElement('pre');
              pre.className = 'rich-editor-codeblock';
              pre.style.cssText = `
                background-color: ${darkMode ? '#111827' : '#1f2937'};
                color: #10b981;
                padding: 16px;
                border-radius: 8px;
                margin: 16px 0;
                overflow-x: auto;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
                border: 1px solid ${darkMode ? '#374151' : '#e5e7eb'};
              `;

              const code = document.createElement('code');
              code.contentEditable = 'true';
              code.style.cssText = `
                background: none;
                color: inherit;
                padding: 0;
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
                white-space: pre;
              `;

              if (selectedText) {
                code.textContent = selectedText;
                range.deleteContents();
              } else {
                code.textContent = '// Seu código aqui\nconsole.log("Hello World");';
              }

              pre.appendChild(code);
              range.insertNode(pre);

              // Add line breaks after code block for writing space
              const lineBreak1 = document.createElement('br');
              const lineBreak2 = document.createElement('br');
              const paragraph = document.createElement('p');
              paragraph.innerHTML = '&nbsp;';

              // Position after code block
              const afterCodeRange = document.createRange();
              afterCodeRange.setStartAfter(pre);
              afterCodeRange.insertNode(lineBreak1);
              afterCodeRange.setStartAfter(lineBreak1);
              afterCodeRange.insertNode(lineBreak2);
              afterCodeRange.setStartAfter(lineBreak2);
              afterCodeRange.insertNode(paragraph);

              // Focus on code content
              const newRange = document.createRange();
              newRange.selectNodeContents(code);
              newRange.collapse(true);
              selection.removeAllRanges();
              selection.addRange(newRange);

              console.log('✅ Bloco de código aplicado visualmente');
            }
            return;

          case 'math':
            // Create math formula element
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString();

              const mathContainer = document.createElement('div');
              mathContainer.className = 'rich-editor-math';
              mathContainer.style.cssText = `
                background-color: ${darkMode ? '#1e1b4b' : '#f0f9ff'};
                border: 2px solid ${darkMode ? '#3730a3' : '#0ea5e9'};
                border-radius: 8px;
                padding: 16px;
                margin: 16px 0;
                text-align: center;
                position: relative;
              `;

              // Math label
              const mathLabel = document.createElement('div');
              mathLabel.style.cssText = `
                position: absolute;
                top: -10px;
                left: 12px;
                background-color: ${darkMode ? '#1e1b4b' : '#f0f9ff'};
                color: ${darkMode ? '#a5b4fc' : '#0369a1'};
                font-size: 12px;
                font-weight: 600;
                padding: 0 8px;
              `;
              mathLabel.textContent = '📐 Fórmula Matemática';

              // Math content
              const mathContent = document.createElement('div');
              mathContent.contentEditable = 'true';
              mathContent.style.cssText = `
                font-family: 'Times New Roman', 'Computer Modern', serif;
                font-size: 18px;
                color: ${darkMode ? '#c7d2fe' : '#1e40af'};
                min-height: 24px;
                outline: none;
                background: transparent;
              `;

              if (selectedText) {
                mathContent.textContent = `$$${selectedText}$$`;
                range.deleteContents();
              } else {
                mathContent.textContent = '$$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$$';
              }

              // Math preview
              const mathPreview = document.createElement('div');
              mathPreview.className = 'math-preview';
              mathPreview.style.cssText = `
                margin-top: 12px;
                padding: 8px;
                background-color: ${darkMode ? '#0f172a' : '#ffffff'};
                border: 1px solid ${darkMode ? '#334155' : '#e2e8f0'};
                border-radius: 4px;
                font-size: 16px;
                color: ${darkMode ? '#e2e8f0' : '#1e293b'};
                min-height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
              `;

              // Solve button
              const solveButton = document.createElement('button');
              solveButton.textContent = '🧮 Resolver';
              solveButton.style.cssText = `
                margin-top: 8px;
                padding: 6px 12px;
                background-color: ${darkMode ? '#059669' : '#10b981'};
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
              `;

              // Solution area
              const solutionArea = document.createElement('div');
              solutionArea.className = 'math-solution';
              solutionArea.style.cssText = `
                margin-top: 8px;
                padding: 8px;
                background-color: ${darkMode ? '#064e3b' : '#ecfdf5'};
                border: 1px solid ${darkMode ? '#059669' : '#10b981'};
                border-radius: 4px;
                font-size: 14px;
                color: ${darkMode ? '#a7f3d0' : '#047857'};
                display: none;
              `;

              // Function to update preview
              const updatePreview = () => {
                const content = mathContent.textContent || '';
                const rendered = renderMathPreview(content);
                mathPreview.innerHTML = rendered;
              };

              // Function to solve math
              const solveMathExpression = () => {
                const content = mathContent.textContent || '';
                const solution = solveMath(content);

                if (solution) {
                  let solutionHTML = `<div style="font-weight: 600; margin-bottom: 8px;">🧮 Solução:</div>`;

                  // Show steps
                  solution.steps.forEach((step, index) => {
                    solutionHTML += `<div style="margin: 4px 0; padding-left: 12px;">
                      <span style="color: ${darkMode ? '#6ee7b7' : '#059669'}; font-weight: 600;">${index + 1}.</span> ${step}
                    </div>`;
                  });

                  // Show final result
                  if (solution.type === 'arithmetic') {
                    solutionHTML += `<div style="margin-top: 8px; padding: 8px; background-color: ${darkMode ? '#047857' : '#d1fae5'}; border-radius: 4px; font-weight: 600;">
                      📊 Resultado Final: ${solution.result}
                    </div>`;
                  } else if (solution.type === 'quadratic') {
                    if (solution.result.x1 !== undefined) {
                      solutionHTML += `<div style="margin-top: 8px; padding: 8px; background-color: ${darkMode ? '#047857' : '#d1fae5'}; border-radius: 4px; font-weight: 600;">
                        📊 Raízes: x₁ = ${solution.result.x1.toFixed(3)}, x₂ = ${solution.result.x2.toFixed(3)}
                      </div>`;
                    } else if (solution.result.x !== undefined) {
                      solutionHTML += `<div style="margin-top: 8px; padding: 8px; background-color: ${darkMode ? '#047857' : '#d1fae5'}; border-radius: 4px; font-weight: 600;">
                        📊 Raiz: x = ${solution.result.x.toFixed(3)}
                      </div>`;
                    } else {
                      solutionHTML += `<div style="margin-top: 8px; padding: 8px; background-color: ${darkMode ? '#7f1d1d' : '#fee2e2'}; border-radius: 4px; font-weight: 600;">
                        ❌ Sem raízes reais
                      </div>`;
                    }
                  } else {
                    solutionHTML += `<div style="margin-top: 8px; padding: 8px; background-color: ${darkMode ? '#047857' : '#d1fae5'}; border-radius: 4px; font-weight: 600;">
                      📊 Resultado: ${solution.result}
                    </div>`;
                  }

                  solutionArea.innerHTML = solutionHTML;
                  solutionArea.style.display = 'block';

                  console.log('✅ Fórmula resolvida:', solution);
                } else {
                  solutionArea.innerHTML = `<div style="color: ${darkMode ? '#fca5a5' : '#dc2626'}; font-weight: 600;">
                    ❌ Não foi possível resolver esta expressão. Tipos suportados:
                    <ul style="margin: 8px 0; padding-left: 20px;">
                      <li>Aritmética: 2 + 3 * 4</li>
                      <li>Equações lineares: 2x + 3 = 7</li>
                      <li>Equações quadráticas: x^2 + 2x - 3 = 0</li>
                      <li>Derivadas: d/dx(x^3)</li>
                      <li>Integrais: \\int x^2 dx</li>
                      <li>Trigonometria: sin(30)</li>
                    </ul>
                  </div>`;
                  solutionArea.style.display = 'block';
                }
              };

              // Add event listeners
              solveButton.addEventListener('click', solveMathExpression);
              solveButton.addEventListener('mouseenter', () => {
                solveButton.style.backgroundColor = darkMode ? '#047857' : '#059669';
                solveButton.style.transform = 'scale(1.05)';
              });
              solveButton.addEventListener('mouseleave', () => {
                solveButton.style.backgroundColor = darkMode ? '#059669' : '#10b981';
                solveButton.style.transform = 'scale(1)';
              });

              // Initial preview
              updatePreview();

              // Update preview on input
              mathContent.addEventListener('input', updatePreview);

              mathContainer.appendChild(mathLabel);
              mathContainer.appendChild(mathContent);
              mathContainer.appendChild(mathPreview);
              mathContainer.appendChild(solveButton);
              mathContainer.appendChild(solutionArea);

              range.insertNode(mathContainer);

              // Add line breaks after math for writing space
              const lineBreak1 = document.createElement('br');
              const lineBreak2 = document.createElement('br');
              const paragraph = document.createElement('p');
              paragraph.innerHTML = '&nbsp;';

              // Position after math
              const afterMathRange = document.createRange();
              afterMathRange.setStartAfter(mathContainer);
              afterMathRange.insertNode(lineBreak1);
              afterMathRange.setStartAfter(lineBreak1);
              afterMathRange.insertNode(lineBreak2);
              afterMathRange.setStartAfter(lineBreak2);
              afterMathRange.insertNode(paragraph);

              // Focus on math content
              const newRange = document.createRange();
              newRange.selectNodeContents(mathContent);
              newRange.collapse(true);
              selection.removeAllRanges();
              selection.addRange(newRange);

              console.log('✅ Fórmula matemática aplicada visualmente');
            }
            return;

          case 'clearFormatting':
            // Enhanced clear formatting function
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);

              if (!range.collapsed) {
                // Has selection - clear formatting from selected content
                const selectedContent = range.extractContents();
                const plainText = selectedContent.textContent || '';

                // Create clean text node
                const textNode = document.createTextNode(plainText);
                range.insertNode(textNode);

                // Add animation effect
                const tempSpan = document.createElement('span');
                tempSpan.style.cssText = `
                  background-color: ${darkMode ? '#fbbf24' : '#fef08a'};
                  transition: background-color 0.5s ease;
                  padding: 1px 2px;
                  border-radius: 2px;
                `;

                // Wrap text node temporarily for animation
                if (textNode.parentNode) {
                  textNode.parentNode.insertBefore(tempSpan, textNode);
                  tempSpan.appendChild(textNode);

                  // Remove animation after delay
                  setTimeout(() => {
                    if (tempSpan.parentNode) {
                      tempSpan.parentNode.insertBefore(textNode, tempSpan);
                      tempSpan.parentNode.removeChild(tempSpan);
                    }
                  }, 500);
                }

                console.log('✅ Formatação removida do texto selecionado');
              } else {
                // No selection - use removeFormat command
                if (document.queryCommandSupported('removeFormat')) {
                  document.execCommand('removeFormat', false);
                  console.log('✅ Formatação removida com removeFormat');
                } else {
                  // Fallback: clear all formatting from editor
                  const editor = richEditorRef.current;
                  if (editor) {
                    const plainText = editor.textContent || '';
                    editor.innerHTML = '';
                    editor.appendChild(document.createTextNode(plainText));
                    console.log('✅ Toda formatação removida (fallback)');
                  }
                }
              }
            }
            return;

          default:
            // For complex formatting, fall back to text insertion
            break;
        }
      } catch (error) {
        console.error('Erro ao aplicar formatação visual:', error);
      }
    }

    // Fallback to textarea formatting (only for markdown mode)
    if (!isRichEditor) {
      const textarea = textareaRef.current;
      if (!textarea) {
        console.log('❌ Textarea não encontrada');
        return;
      }

      const start = textarea.selectionStart || 0;
      const end = textarea.selectionEnd || 0;
      const selectedText = noteContent.substring(start, end);

      console.log('📍 Posição cursor:', { start, end, selectedText });

      let formattedText = '';
      let cursorOffset = 0;

      switch (format) {
      case 'bold':
        if (selectedText) {
          formattedText = `**${selectedText}**`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `**texto em negrito**`;
          cursorOffset = 2; // Position cursor inside the **
        }
        break;
      case 'italic':
        if (selectedText) {
          formattedText = `*${selectedText}*`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `*texto em itálico*`;
          cursorOffset = 1;
        }
        break;
      case 'heading1':
        formattedText = `\n# ${selectedText || 'Título Principal'}\n`;
        cursorOffset = selectedText ? formattedText.length - 1 : 3;
        break;
      case 'heading2':
        formattedText = `\n## ${selectedText || 'Subtítulo'}\n`;
        cursorOffset = selectedText ? formattedText.length - 1 : 4;
        break;
      case 'heading3':
        formattedText = `\n### ${selectedText || 'Subtítulo Menor'}\n`;
        cursorOffset = selectedText ? formattedText.length - 1 : 5;
        break;
      case 'list':
        formattedText = `\n- ${selectedText || 'Item da lista'}`;
        cursorOffset = selectedText ? formattedText.length : 3;
        break;
      case 'orderedList':
        formattedText = `\n1. ${selectedText || 'Item numerado'}`;
        cursorOffset = selectedText ? formattedText.length : 4;
        break;
      case 'quote':
        formattedText = `\n> ${selectedText || 'Citação'}`;
        cursorOffset = selectedText ? formattedText.length : 3;
        break;
      case 'code':
        if (selectedText) {
          formattedText = `\`${selectedText}\``;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `\`código\``;
          cursorOffset = 1;
        }
        break;
      case 'link':
        if (selectedText) {
          formattedText = `[${selectedText}](url)`;
          cursorOffset = formattedText.length - 4; // Position cursor at "url"
        } else {
          formattedText = `[texto do link](url)`;
          cursorOffset = 1;
        }
        break;
      case 'underline':
        if (selectedText) {
          formattedText = `<u>${selectedText}</u>`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `<u>texto sublinhado</u>`;
          cursorOffset = 3;
        }
        break;
      case 'strikethrough':
        if (selectedText) {
          formattedText = `~~${selectedText}~~`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `~~texto riscado~~`;
          cursorOffset = 2;
        }
        break;
      case 'highlight':
        if (selectedText) {
          formattedText = `==${selectedText}==`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `==texto destacado==`;
          cursorOffset = 2;
        }
        break;
      case 'superscript':
        if (selectedText) {
          formattedText = `^${selectedText}^`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `^sobrescrito^`;
          cursorOffset = 1;
        }
        break;
      case 'subscript':
        if (selectedText) {
          formattedText = `~${selectedText}~`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `~subscrito~`;
          cursorOffset = 1;
        }
        break;
      case 'table':
        formattedText = `\n| Coluna 1 | Coluna 2 | Coluna 3 |\n|----------|----------|----------|\n| Célula 1 | Célula 2 | Célula 3 |\n| Célula 4 | Célula 5 | Célula 6 |\n`;
        cursorOffset = 12; // Position at first cell
        break;
      case 'checkbox':
        formattedText = `\n- [ ] ${selectedText || 'Item da lista de tarefas'}`;
        cursorOffset = selectedText ? formattedText.length : 6;
        break;
      case 'checkedbox':
        formattedText = `\n- [x] ${selectedText || 'Item concluído'}`;
        cursorOffset = selectedText ? formattedText.length : 6;
        break;
      case 'divider':
        formattedText = `\n---\n`;
        cursorOffset = formattedText.length;
        break;
      case 'codeblock':
        formattedText = `\n\`\`\`javascript\n${selectedText || '// Seu código aqui'}\n\`\`\`\n`;
        cursorOffset = selectedText ? formattedText.length - 5 : 16;
        break;
      case 'math':
        if (selectedText) {
          formattedText = `$$${selectedText}$$`;
          cursorOffset = formattedText.length;
        } else {
          formattedText = `$$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$$`;
          cursorOffset = 2;
        }
        break;
      case 'callout':
        formattedText = `\n> [!NOTE]\n> ${selectedText || 'Sua nota importante aqui'}\n`;
        cursorOffset = selectedText ? formattedText.length - 1 : 12;
        break;
      case 'warning':
        formattedText = `\n> [!WARNING]\n> ${selectedText || 'Aviso importante'}\n`;
        cursorOffset = selectedText ? formattedText.length - 1 : 15;
        break;
      case 'info':
        formattedText = `\n> [!INFO]\n> ${selectedText || 'Informação útil'}\n`;
        cursorOffset = selectedText ? formattedText.length - 1 : 12;
        break;
      default:
        formattedText = selectedText;
        cursorOffset = formattedText.length;
      }

      const newContent = noteContent.substring(0, start) + formattedText + noteContent.substring(end);
      console.log('📝 Novo conteúdo:', newContent.substring(Math.max(0, start - 10), start + formattedText.length + 10));

      // Update content using React state
      setNoteContent(newContent);

      // Alternative approach: directly manipulate textarea value
      textarea.value = newContent;

      // Trigger input event to sync with React
      const event = new Event('input', { bubbles: true });
      textarea.dispatchEvent(event);

      // Focus back to editor and set cursor position
      setTimeout(() => {
        if (isRichEditor && richEditorRef.current) {
          // Rich editor cursor positioning
          richEditorRef.current.focus();
          console.log('📍 Foco retornado ao editor rico');
        } else if (textarea) {
          // Textarea cursor positioning
          textarea.focus();
          const newCursorPos = start + cursorOffset;
          if (typeof textarea.setSelectionRange === 'function') {
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            console.log('📍 Nova posição cursor:', newCursorPos);
          }
        }
      }, 10);
    }
  };

  // File attachment
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // Handle special Enter behavior for checkboxes and lists in rich editor
  const handleRichEditorKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    // Handle math calculation when user types "="
    if (e.key === '=' && richEditorRef.current) {
      setTimeout(() => {
        handleMathCalculation(richEditorRef.current!);
      }, 10);
    }

    if (e.key === 'Enter' && isRichEditor && richEditorRef.current) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const currentElement = range.startContainer.nodeType === Node.TEXT_NODE
          ? range.startContainer.parentElement
          : range.startContainer as Element;

        // Check if we're in a checkbox container
        const checkboxContainer = currentElement?.closest('.rich-editor-checkbox');
        if (checkboxContainer) {
          e.preventDefault();

          // Create new checkbox item
          const newCheckboxContainer = document.createElement('div');
          newCheckboxContainer.className = 'rich-editor-checkbox';
          newCheckboxContainer.style.cssText = `
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 4px 0;
          `;

          const newCheckbox = document.createElement('input');
          newCheckbox.type = 'checkbox';
          newCheckbox.style.cssText = `
            margin: 0;
            cursor: pointer;
          `;

          const newLabel = document.createElement('span');
          newLabel.textContent = 'Novo item da lista';
          newLabel.style.cssText = `
            cursor: pointer;
          `;
          newLabel.contentEditable = 'true';

          newCheckboxContainer.appendChild(newCheckbox);
          newCheckboxContainer.appendChild(newLabel);

          // Insert after current checkbox
          if (checkboxContainer.parentNode) {
            checkboxContainer.parentNode.insertBefore(newCheckboxContainer, checkboxContainer.nextSibling);
          }

          // Focus on new label
          const newRange = document.createRange();
          newRange.selectNodeContents(newLabel);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          console.log('✅ Novo checkbox adicionado com Enter');
          return;
        }

        // Check if we're in a math formula
        const mathFormula = currentElement?.closest('.rich-editor-math [contenteditable]');
        if (mathFormula) {
          // Allow normal Enter behavior in math formulas (creates new line)
          console.log('✅ Enter em fórmula matemática - nova linha');
          return;
        }

        // Check if we're in a code block
        const codeBlock = currentElement?.closest('pre code');
        if (codeBlock) {
          // Allow normal Enter behavior in code blocks (creates new line)
          console.log('✅ Enter em bloco de código - nova linha');
          return;
        }

        // Check if we're in a list item
        const listItem = currentElement?.closest('li');
        if (listItem) {
          const list = listItem.closest('ul, ol');
          if (list) {
            e.preventDefault();

            // Create new list item
            const newLi = document.createElement('li');
            newLi.style.cssText = `
              margin: 4px 0;
              color: ${darkMode ? '#e5e7eb' : '#374151'};
              display: list-item;
              padding-left: 0.25rem;
            `;
            newLi.textContent = 'Novo item';
            newLi.contentEditable = 'true';

            // Insert after current item
            if (listItem.parentNode) {
              listItem.parentNode.insertBefore(newLi, listItem.nextSibling);
            }

            // Focus on new item
            const newRange = document.createRange();
            newRange.selectNodeContents(newLi);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            console.log('✅ Novo item de lista adicionado com Enter');
            return;
          }
        }
      }
    }

    // Handle keyboard shortcuts in rich editor
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          insertFormatting('bold');
          break;
        case 'i':
          e.preventDefault();
          insertFormatting('italic');
          break;
        case 'u':
          e.preventDefault();
          insertFormatting('underline');
          break;
        case 'd':
          e.preventDefault();
          insertFormatting('strikethrough');
          break;
        case 'h':
          e.preventDefault();
          insertFormatting('highlight');
          break;
        case 'k':
          e.preventDefault();
          insertFormatting('link');
          break;
        case '1':
          e.preventDefault();
          insertFormatting('heading1');
          break;
        case '2':
          e.preventDefault();
          insertFormatting('heading2');
          break;
        case '3':
          e.preventDefault();
          insertFormatting('heading3');
          break;
        case 'l':
          e.preventDefault();
          insertFormatting('list');
          break;
        case 'q':
          e.preventDefault();
          insertFormatting('quote');
          break;
        case 'e':
          e.preventDefault();
          insertFormatting('code');
          break;
        case 't':
          e.preventDefault();
          insertFormatting('table');
          break;
        case 'm':
          e.preventDefault();
          insertFormatting('math');
          break;
        case 'r':
          e.preventDefault();
          insertFormatting('divider');
          break;
        case 'f':
          e.preventDefault();
          setShowFindReplace(!showFindReplace);
          break;
        case '\\':
          e.preventDefault();
          insertFormatting('clearFormatting');
          break;
      }
    }
  };

  // Keyboard shortcuts for formatting (textarea mode)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle math calculation when user types "="
    if (e.key === '=' && textareaRef.current) {
      setTimeout(() => {
        handleMathCalculation(textareaRef.current!);
      }, 10);
    }

    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          insertFormatting('bold');
          break;
        case 'i':
          e.preventDefault();
          insertFormatting('italic');
          break;
        case 'k':
          e.preventDefault();
          insertFormatting('link');
          break;
        case '1':
          e.preventDefault();
          insertFormatting('heading1');
          break;
        case '2':
          e.preventDefault();
          insertFormatting('heading2');
          break;
        case '3':
          e.preventDefault();
          insertFormatting('heading3');
          break;
        case 'l':
          e.preventDefault();
          insertFormatting('list');
          break;
        case 'q':
          e.preventDefault();
          insertFormatting('quote');
          break;
        case 'e':
          e.preventDefault();
          insertFormatting('code');
          break;
        case 's':
          e.preventDefault();
          handleSave();
          break;
        case 'u':
          e.preventDefault();
          insertFormatting('underline');
          break;
        case 'd':
          e.preventDefault();
          insertFormatting('strikethrough');
          break;
        case 'h':
          e.preventDefault();
          insertFormatting('highlight');
          break;
        case 't':
          e.preventDefault();
          insertFormatting('table');
          break;
        case 'm':
          e.preventDefault();
          insertFormatting('math');
          break;
        case 'r':
          e.preventDefault();
          insertFormatting('divider');
          break;
        case 'f':
          e.preventDefault();
          setShowFindReplace(!showFindReplace);
          break;
        case '\\':
          e.preventDefault();
          insertFormatting('clearFormatting');
          break;
      }
    }
  };

  // Find and Replace functions
  const handleFind = () => {
    if (!findText) return;

    const textarea = textareaRef.current;
    if (!textarea) return;

    const content = noteContent.toLowerCase();
    const searchText = findText.toLowerCase();
    const index = content.indexOf(searchText, textarea.selectionStart);

    if (index !== -1) {
      textarea.focus();
      textarea.setSelectionRange(index, index + findText.length);
      console.log('🔍 Texto encontrado na posição:', index);
    } else {
      console.log('🔍 Texto não encontrado');
      alert('Texto não encontrado');
    }
  };

  const handleReplace = () => {
    if (!findText) return;

    const newContent = noteContent.replace(new RegExp(findText, 'g'), replaceText);
    setNoteContent(newContent);
    console.log('🔄 Substituição realizada:', findText, '→', replaceText);
  };

  const handleReplaceAll = () => {
    if (!findText) return;

    const newContent = noteContent.replace(new RegExp(findText, 'gi'), replaceText);
    setNoteContent(newContent);
    console.log('🔄 Substituição global realizada:', findText, '→', replaceText);
  };

  // Text formatting utilities
  const applyTextStyle = (style: string, value?: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    switch (style) {
      case 'fontSize':
        textarea.style.fontSize = `${value}px`;
        setFontSize(parseInt(value || '14'));
        break;
      case 'lineHeight':
        textarea.style.lineHeight = value || '1.5';
        setLineHeight(parseFloat(value || '1.5'));
        break;
      case 'textAlign':
        textarea.style.textAlign = value || 'left';
        setTextAlign(value as 'left' | 'center' | 'right' | 'justify');
        break;
    }
  };

  const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const input = e.currentTarget;
      const tag = input.value.trim();
      if (tag && !noteTags.includes(tag)) {
        setNoteTags([...noteTags, tag]);
        input.value = '';
      }
    }
  };

  const removeTag = (tagToRemove: string) => {
    setNoteTags(noteTags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = () => {
    if (!noteTitle.trim()) {
      showToastNotification('❌ Título é obrigatório', 'error');
      return;
    }
    showToastNotification('✅ Nota salva com sucesso!', 'success');
    onSave();
  };

  return (
    <>
      {/* Toast Notification */}
      {showToast && (
        <div className={`fixed top-4 right-4 max-w-sm w-full transform transition-all duration-300 ease-in-out ${
          showToast ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
        }`} style={{ zIndex: 99999 }}>
          <div className={`rounded-lg shadow-lg p-4 flex items-center gap-3 ${
            toastType === 'success'
              ? 'bg-green-500 text-white'
              : toastType === 'error'
              ? 'bg-red-500 text-white'
              : 'bg-blue-500 text-white'
          }`}>
            <div className="flex-shrink-0">
              {toastType === 'success' && (
                <div className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <Check className="w-4 h-4" />
                </div>
              )}
              {toastType === 'error' && (
                <div className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <X className="w-4 h-4" />
                </div>
              )}
              {toastType === 'info' && (
                <div className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <Calendar className="w-4 h-4" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">{toastMessage}</p>
            </div>
            <button
              onClick={() => setShowToast(false)}
              className="flex-shrink-0 w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Custom CSS for Rich Editor */}
      <style>{`
        .rich-editor [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9CA3AF;
          pointer-events: none;
        }

        /* Formatting button animations */
        .formatting-btn {
          position: relative;
          overflow: hidden;
        }

        .formatting-btn::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          background: rgba(59, 130, 246, 0.3);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          transition: width 0.3s ease, height 0.3s ease;
        }

        .formatting-btn:active::before {
          width: 100px;
          height: 100px;
        }

        /* Table controls animations */
        .table-controls button {
          transition: all 0.2s ease;
          transform: scale(1);
        }

        .table-controls button:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-controls button:active {
          transform: scale(0.95);
        }

        /* Success animation */
        @keyframes successPulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); background-color: #10b981; }
          100% { transform: scale(1); }
        }

        .success-animation {
          animation: successPulse 0.3s ease;
        }

        /* Clear formatting animation */
        @keyframes clearFormatting {
          0% { background-color: transparent; }
          50% { background-color: #fef08a; }
          100% { background-color: transparent; }
        }

        .clear-formatting-animation {
          animation: clearFormatting 0.5s ease;
        }

        /* Math formulas */
        .rich-editor-math {
          transition: all 0.2s ease;
        }
        .rich-editor-math:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .rich-editor-math [contenteditable]:focus {
          outline: none;
        }
        .rich-editor-math [contenteditable]:empty:before {
          content: '$$x = y + z$$';
          color: ${darkMode ? '#6b7280' : '#9ca3af'};
          font-style: italic;
        }

        /* Math preview */
        .math-preview {
          transition: all 0.2s ease;
        }
        .math-preview:hover {
          background-color: ${darkMode ? '#1e293b' : '#f8fafc'};
        }
        .math-preview sup, .math-preview sub {
          line-height: 0;
        }
        .math-preview .fraction {
          display: inline-block;
          text-align: center;
          vertical-align: middle;
        }
        .math-preview .fraction-top {
          display: block;
          border-bottom: 1px solid currentColor;
          padding-bottom: 2px;
        }
        .math-preview .fraction-bottom {
          display: block;
          padding-top: 2px;
        }

        /* Typography */
        .rich-editor h1 {
          font-size: 2rem;
          font-weight: bold;
          margin: 1.5rem 0 0.75rem 0;
          color: ${darkMode ? '#f9fafb' : '#111827'};
        }
        .rich-editor h2 {
          font-size: 1.5rem;
          font-weight: bold;
          margin: 1.25rem 0 0.5rem 0;
          color: ${darkMode ? '#f3f4f6' : '#1f2937'};
        }
        .rich-editor h3 {
          font-size: 1.25rem;
          font-weight: 600;
          margin: 1rem 0 0.5rem 0;
          color: ${darkMode ? '#e5e7eb' : '#374151'};
        }
        .rich-editor h1:first-child,
        .rich-editor h2:first-child,
        .rich-editor h3:first-child {
          margin-top: 0;
        }

        /* Text formatting */
        .rich-editor strong, .rich-editor b {
          font-weight: bold;
          color: ${darkMode ? '#ffffff' : '#000000'};
        }
        .rich-editor em, .rich-editor i {
          font-style: italic;
          color: ${darkMode ? '#e5e7eb' : '#374151'};
        }
        .rich-editor u {
          text-decoration: underline;
        }
        .rich-editor s {
          text-decoration: line-through;
          color: ${darkMode ? '#9ca3af' : '#6b7280'};
        }

        /* Lists */
        .rich-editor ul, .rich-editor ol {
          margin: 0.75rem 0;
          padding-left: 1.5rem;
          list-style-position: outside;
        }
        .rich-editor ul {
          list-style-type: disc;
        }
        .rich-editor ol {
          list-style-type: decimal;
        }
        .rich-editor li {
          margin: 0.25rem 0;
          color: ${darkMode ? '#e5e7eb' : '#374151'};
          display: list-item;
          padding-left: 0.25rem;
        }
        .rich-editor li::marker {
          color: ${darkMode ? '#9ca3af' : '#6b7280'};
        }

        /* Blockquotes */
        .rich-editor blockquote {
          border-left: 4px solid ${darkMode ? '#4b5563' : '#d1d5db'};
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: ${darkMode ? '#9ca3af' : '#6b7280'};
          background-color: ${darkMode ? '#1f2937' : '#f9fafb'};
          padding: 0.75rem 1rem;
          border-radius: 0.375rem;
        }

        /* Code */
        .rich-editor code {
          background-color: ${darkMode ? '#374151' : '#f1f5f9'};
          color: ${darkMode ? '#fca5a5' : '#dc2626'};
          padding: 0.125rem 0.25rem;
          border-radius: 0.25rem;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.875rem;
        }
        .rich-editor pre {
          background-color: ${darkMode ? '#111827' : '#1f2937'};
          color: #10b981;
          padding: 1rem;
          border-radius: 0.5rem;
          margin: 1rem 0;
          overflow-x: auto;
          border: 1px solid ${darkMode ? '#374151' : '#e5e7eb'};
          position: relative;
        }
        .rich-editor pre code {
          background: none;
          color: inherit;
          padding: 0;
          font-size: 0.875rem;
          line-height: 1.4;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          white-space: pre;
          display: block;
          width: 100%;
          min-height: 1.4em;
        }
        .rich-editor pre code:focus {
          outline: none;
        }
        .rich-editor pre code:empty:before {
          content: '// Seu código aqui';
          color: ${darkMode ? '#6b7280' : '#9ca3af'};
          font-style: italic;
        }

        /* Links */
        .rich-editor a {
          color: ${darkMode ? '#60a5fa' : '#2563eb'};
          text-decoration: underline;
          cursor: pointer;
        }
        .rich-editor a:hover {
          color: ${darkMode ? '#93c5fd' : '#1d4ed8'};
        }

        /* Dividers */
        .rich-editor hr {
          border: none;
          border-top: 1px solid ${darkMode ? '#4b5563' : '#d1d5db'};
          margin: 1.5rem 0;
        }

        /* Paragraphs */
        .rich-editor p {
          margin: 0.5rem 0;
          line-height: 1.6;
        }

        /* Focus styles */
        .rich-editor:focus {
          outline: none;
          box-shadow: 0 0 0 2px #3b82f6;
        }

        /* Selection */
        .rich-editor ::selection {
          background-color: ${darkMode ? '#3b82f6' : '#bfdbfe'};
        }
      `}</style>

      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[200] flex items-center justify-center">
      <div className={`w-full h-full ${darkMode ? 'bg-gray-900' : 'bg-white'} shadow-2xl overflow-hidden flex flex-col`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-bold">
              {isNewNote ? 'Nova Nota' : 'Editar Nota'}
            </h2>

            {/* Priority Indicator */}
            <div className="flex items-center gap-1">
              <Target className={`w-4 h-4 ${
                priority === 'high' ? 'text-red-500' :
                priority === 'medium' ? 'text-yellow-500' :
                'text-green-500'
              }`} />
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value as 'low' | 'medium' | 'high')}
                className={`text-sm border-none bg-transparent outline-none ${
                  priority === 'high' ? 'text-red-500' :
                  priority === 'medium' ? 'text-yellow-500' :
                  'text-green-500'
                }`}
              >
                <option value="low">Baixa</option>
                <option value="medium">Média</option>
                <option value="high">Alta</option>
              </select>
            </div>
          </div>

          {/* Toolbar */}
          <div className="flex items-center gap-2">
            {/* AI Panel Toggle */}
            <button
              onClick={() => setShowAIPanel(!showAIPanel)}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${
                showAIPanel
                  ? 'bg-purple-500 text-white'
                  : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
              title="Assistente AI"
            >
              <Brain className="w-5 h-5" />
            </button>

            {/* Voice Recording */}
            <button
              onClick={toggleRecording}
              className={`p-2 rounded-lg transition-all hover:scale-110 relative ${
                isRecording
                  ? 'bg-red-500 text-white animate-pulse'
                  : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
              title={isRecording ? 'Parar Gravação (Clique ou fale "parar")' : 'Gravar Voz'}
            >
              {isRecording ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              {isListening && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
              )}
            </button>

            {/* Formatting Toggle */}
            <button
              onClick={() => setShowFormatting(!showFormatting)}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${
                showFormatting
                  ? 'bg-blue-500 text-white'
                  : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
              title="Formatação"
            >
              <Type className="w-5 h-5" />
            </button>

            {/* Rich Editor Toggle */}
            <button
              onClick={() => setIsRichEditor(!isRichEditor)}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${
                isRichEditor
                  ? 'bg-purple-500 text-white'
                  : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
              title={isRichEditor ? 'Modo Markdown' : 'Modo Visual'}
            >
              {isRichEditor ? <Code className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>

            {/* Privacy Toggle */}
            <button
              onClick={() => setNoteIsPublic?.(!noteIsPublic)}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${
                !noteIsPublic
                  ? 'text-gray-500'
                  : 'text-green-500'
              } ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              title={!noteIsPublic ? 'Nota Privada - Clique para tornar pública' : 'Nota Pública - Clique para tornar privada'}
            >
              {!noteIsPublic ? <Lock className="w-5 h-5" /> : <Globe className="w-5 h-5" />}
            </button>

            {/* Save Button */}
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all hover:scale-105 flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Salvar
            </button>

            {/* Close Button */}
            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Formatting Toolbar */}
        {showFormatting && (
          <div className={`border-b ${darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
            {/* Basic Formatting */}
            <div className="p-3 border-b border-gray-300 dark:border-gray-600">
              <div className="flex items-center gap-1 flex-wrap">
                <span className="text-xs font-medium text-gray-500 mr-2">Básico:</span>
                <button
                  onClick={() => insertFormatting('bold')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Negrito (Ctrl+B)"
                  data-format="bold"
                >
                  <Bold className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('italic')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Itálico (Ctrl+I)"
                  data-format="italic"
                >
                  <Italic className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('underline')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Sublinhado (Ctrl+U)"
                  data-format="underline"
                >
                  <Underline className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('strikethrough')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Riscado (Ctrl+D)"
                  data-format="strikethrough"
                >
                  <Strikethrough className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('highlight')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Destacar (Ctrl+H)"
                  data-format="highlight"
                >
                  <Highlighter className="w-4 h-4" />
                </button>
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
                <button
                  onClick={() => insertFormatting('superscript')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Sobrescrito"
                  data-format="superscript"
                >
                  <Superscript className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('subscript')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Subscrito"
                  data-format="subscript"
                >
                  <Subscript className="w-4 h-4" />
                </button>
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
                <button
                  onClick={() => insertFormatting('clearFormatting')}
                  className="formatting-btn p-2 rounded hover:bg-red-100 dark:hover:bg-red-900 transition-all duration-200 hover:scale-110 active:scale-95 text-red-600 dark:text-red-400"
                  title="Limpar Formatação (Ctrl+\\)"
                  data-format="clearFormatting"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Structure */}
            <div className="p-3 border-b border-gray-300 dark:border-gray-600">
              <div className="flex items-center gap-1 flex-wrap">
                <span className="text-xs font-medium text-gray-500 mr-2">Estrutura:</span>
                <button
                  onClick={() => insertFormatting('heading1')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Título 1 (Ctrl+1)"
                  data-format="heading1"
                >
                  <Heading1 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('heading2')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Título 2 (Ctrl+2)"
                  data-format="heading2"
                >
                  <Heading2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('heading3')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Título 3 (Ctrl+3)"
                  data-format="heading3"
                >
                  <Heading3 className="w-4 h-4" />
                </button>
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
                <button
                  onClick={() => insertFormatting('list')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Lista (Ctrl+L)"
                  data-format="list"
                >
                  <List className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('orderedList')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Lista Numerada"
                  data-format="orderedList"
                >
                  <ListOrdered className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('checkbox')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Lista de Tarefas"
                  data-format="checkbox"
                >
                  <CheckSquare className="w-4 h-4" />
                </button>
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
                <button
                  onClick={() => insertFormatting('quote')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Citação (Ctrl+Q)"
                  data-format="quote"
                >
                  <Quote className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('divider')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Divisor (Ctrl+R)"
                  data-format="divider"
                >
                  <Minus className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Advanced */}
            <div className="p-3">
              <div className="flex items-center gap-1 flex-wrap">
                <span className="text-xs font-medium text-gray-500 mr-2">Avançado:</span>
                <button
                  onClick={() => insertFormatting('code')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Código Inline (Ctrl+E)"
                  data-format="code"
                >
                  <Code className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('codeblock')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Bloco de Código"
                  data-format="codeblock"
                >
                  <Square className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('link')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Link (Ctrl+K)"
                  data-format="link"
                >
                  <Link className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('table')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Tabela (Ctrl+T)"
                  data-format="table"
                >
                  <Table className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('math')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Fórmula Matemática (Ctrl+M)"
                  data-format="math"
                >
                  <Hash className="w-4 h-4" />
                </button>
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
                <button
                  onClick={() => insertFormatting('callout')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Nota Importante"
                  data-format="callout"
                >
                  <MessageSquare className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('warning')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Aviso"
                  data-format="warning"
                >
                  <Triangle className="w-4 h-4" />
                </button>
                <button
                  onClick={() => insertFormatting('info')}
                  className="formatting-btn p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-110 active:scale-95"
                  title="Informação"
                  data-format="info"
                >
                  <Circle className="w-4 h-4" />
                </button>
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
                <button
                  onClick={() => setShowFindReplace(!showFindReplace)}
                  className={`p-2 rounded transition-all duration-200 hover:scale-110 active:scale-95 ${
                    showFindReplace
                      ? 'bg-blue-500 text-white'
                      : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                  }`}
                  title="Buscar e Substituir (Ctrl+F)"
                >
                  <Search className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Find and Replace Panel */}
        {showFindReplace && (
          <div className={`p-3 border-b ${darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
            <div className="flex items-center gap-2 flex-wrap">
              <input
                type="text"
                placeholder="Buscar..."
                value={findText}
                onChange={(e) => setFindText(e.target.value)}
                className={`px-3 py-1 rounded border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'} text-sm`}
              />
              <input
                type="text"
                placeholder="Substituir por..."
                value={replaceText}
                onChange={(e) => setReplaceText(e.target.value)}
                className={`px-3 py-1 rounded border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'} text-sm`}
              />
              <button onClick={handleFind} className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
                Buscar
              </button>
              <button onClick={handleReplace} className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600">
                Substituir
              </button>
              <button onClick={handleReplaceAll} className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600">
                Substituir Todos
              </button>
              <button onClick={() => setShowFindReplace(false)} className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Main Editor */}
          <div className="flex-1 flex flex-col p-6">
            {/* Title */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Type className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">Título</label>
              </div>
              <input
                type="text"
                value={noteTitle}
                onChange={(e) => setNoteTitle(e.target.value)}
                placeholder="Digite o título da nota..."
                className={`w-full text-2xl font-bold bg-transparent border-none outline-none placeholder-gray-400 ${
                  darkMode ? 'text-white' : 'text-gray-900'
                }`}
                autoFocus
              />
            </div>

            {/* Date Suggestion Panel */}
            {showDateSuggestion && suggestedReminder && (
              <div className={`mb-4 p-4 rounded-lg border-2 border-dashed ${
                darkMode
                  ? 'border-blue-400 bg-blue-900/20'
                  : 'border-blue-400 bg-blue-50'
              } animate-pulse`}>
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <Calendar className="w-5 h-5 text-blue-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className={`font-medium ${darkMode ? 'text-blue-300' : 'text-blue-700'} mb-1`}>
                      📅 Data detectada no título!
                    </h4>
                    <p className={`text-sm ${darkMode ? 'text-blue-200' : 'text-blue-600'} mb-3`}>
                      Detectamos "{detectedDates[0]?.text}" no título.
                      Deseja criar um lembrete para{' '}
                      <strong>
                        {suggestedReminder.date.toLocaleDateString('pt-BR', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </strong>?
                    </p>
                    <div className="flex gap-2">
                      <button
                        onClick={acceptReminderSuggestion}
                        className="px-3 py-1.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium flex items-center gap-1"
                      >
                        <Check className="w-4 h-4" />
                        Criar Lembrete
                      </button>
                      <button
                        onClick={rejectReminderSuggestion}
                        className={`px-3 py-1.5 rounded-lg transition-colors text-sm font-medium flex items-center gap-1 ${
                          darkMode
                            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        <X className="w-4 h-4" />
                        Não, obrigado
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Content */}
            <div className="flex-1 flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <AlignLeft className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">
                  Conteúdo {isRichEditor ? '(Visual)' : '(Markdown)'}
                </label>
                <div className="flex items-center gap-1 ml-auto">
                  <span className="text-xs text-gray-400">
                    {isRichEditor ? '🎨 Rico' : '📝 Markdown'}
                  </span>
                </div>
              </div>

              {isRichEditor ? (
                /* Rich Editor */
                <div
                  ref={richEditorRef}
                  contentEditable
                  onInput={handleRichEditorInput}
                  onKeyDown={handleRichEditorKeyDown}
                  className={`rich-editor flex-1 w-full bg-transparent border-none outline-none resize-none p-2 rounded ${
                    darkMode ? 'text-white' : 'text-gray-900'
                  } focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
                  style={{
                    minHeight: '300px',
                    maxHeight: '600px',
                    overflowY: 'auto',
                    lineHeight: '1.6'
                  }}
                  suppressContentEditableWarning={true}
                  data-placeholder="Escreva sua nota aqui... Use **negrito**, *itálico*, # títulos, etc."
                />
              ) : (
                /* Markdown Editor */
                <textarea
                  ref={textareaRef}
                  value={noteContent}
                  onChange={(e) => setNoteContent(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Escreva sua nota aqui... Use **negrito**, *itálico*, # títulos, etc. Digite 2+3= para calcular automaticamente!"
                  className={`flex-1 w-full bg-transparent border-none outline-none resize-none placeholder-gray-400 font-mono ${
                    darkMode ? 'text-white' : 'text-gray-900'
                  }`}
                  style={{
                    lineHeight: '1.6',
                    fontSize: '14px'
                  }}
                />
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className={`w-80 border-l ${darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'} overflow-y-auto`}>
            {/* AI Panel */}
            {showAIPanel && (
              <div className="p-4 border-b border-gray-300 dark:border-gray-600">
                <div className="flex items-center gap-2 mb-4">
                  <Brain className="w-5 h-5 text-purple-500" />
                  <h3 className="font-semibold text-purple-500">Assistente AI</h3>
                </div>

                {/* AI Actions */}
                <div className="space-y-2 mb-4">
                  <button
                    onClick={() => generateAIContent('resumo')}
                    disabled={isGeneratingAI}
                    className="w-full p-2 text-left rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center gap-2 disabled:opacity-50"
                  >
                    <FileText className="w-4 h-4" />
                    Gerar Resumo
                  </button>
                  <button
                    onClick={() => generateAIContent('expandir')}
                    disabled={isGeneratingAI}
                    className="w-full p-2 text-left rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center gap-2 disabled:opacity-50"
                  >
                    <Zap className="w-4 h-4" />
                    Expandir Conteúdo
                  </button>
                  <button
                    onClick={() => generateAIContent('melhorar')}
                    disabled={isGeneratingAI}
                    className="w-full p-2 text-left rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center gap-2 disabled:opacity-50"
                  >
                    <Sparkles className="w-4 h-4" />
                    Melhorar Texto
                  </button>
                  <button
                    onClick={() => generateAIContent('outline')}
                    disabled={isGeneratingAI}
                    className="w-full p-2 text-left rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center gap-2 disabled:opacity-50"
                  >
                    <BookOpen className="w-4 h-4" />
                    Criar Outline
                  </button>
                </div>

                {/* AI Suggestions */}
                <button
                  onClick={generateAISuggestions}
                  disabled={isGeneratingAI}
                  className="w-full p-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 flex items-center gap-2 justify-center"
                >
                  <Lightbulb className="w-4 h-4" />
                  {isGeneratingAI ? 'Gerando...' : 'Sugestões AI'}
                </button>

                {/* Display AI Suggestions */}
                {aiSuggestions.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Sugestões:</h4>
                    <div className="space-y-2">
                      {aiSuggestions.map((suggestion, index) => (
                        <div
                          key={index}
                          className={`p-2 text-xs rounded ${darkMode ? 'bg-gray-700' : 'bg-white'} border`}
                        >
                          {suggestion}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="p-6">
            {/* Category */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Folder className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">Categoria</label>
              </div>
              <select
                value={noteCategory}
                onChange={(e) => handleCategoryChange(e.target.value)}
                className={`w-full p-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none`}
              >
                <option value="">📝 Sem categoria</option>
                {/* Sempre mostrar categorias do Firebase se disponíveis, senão mostrar constantes */}
                {categories && categories.length > 0 ? (
                  categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))
                ) : (
                  CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))
                )}
              </select>
            </div>

            {/* Color */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Palette className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">Cor</label>
              </div>
              <div className="grid grid-cols-5 gap-2">
                {NOTE_COLORS.map(color => (
                  <button
                    key={color}
                    onClick={() => setNoteColor(color)}
                    className={`w-8 h-8 rounded-lg transition-all hover:scale-110 ${
                      noteColor === color ? 'ring-2 ring-offset-2 ring-blue-500' : ''
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>

            {/* Mood */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Smile className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">Humor</label>
              </div>
              <div className="grid grid-cols-3 gap-2">
                {/* Opção "Nenhum" */}
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setNoteMood('');
                  }}
                  className={`p-2 rounded-lg text-center transition-all hover:scale-105 ${
                    noteMood === ''
                      ? 'bg-blue-500 text-white'
                      : darkMode
                        ? 'bg-gray-700 hover:bg-gray-600'
                        : 'bg-white hover:bg-gray-100'
                  }`}
                >
                  <div className="text-lg">🚫</div>
                  <div className="text-xs">Nenhum</div>
                </button>

                {MOODS.map(mood => (
                  <button
                    key={mood.id}
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setNoteMood(mood.id);
                    }}
                    className={`p-2 rounded-lg text-center transition-all hover:scale-105 ${
                      noteMood === mood.id
                        ? 'bg-blue-500 text-white'
                        : darkMode
                          ? 'bg-gray-700 hover:bg-gray-600'
                          : 'bg-white hover:bg-gray-100'
                    }`}
                  >
                    <div className="text-lg">{mood.emoji}</div>
                    <div className="text-xs">{mood.name}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Tag className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">Tags</label>
              </div>
              <input
                type="text"
                placeholder="Digite uma tag e pressione Enter"
                onKeyDown={handleTagInput}
                className={`w-full p-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none`}
              />
              <div className="flex flex-wrap gap-2 mt-2">
                {noteTags.map(tag => (
                  <span
                    key={tag}
                    className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                      darkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-600'
                    }`}
                  >
                    #{tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="hover:text-red-500 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* Attachments */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Paperclip className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">Anexos</label>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileUpload}
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                className={`w-full p-2 border-2 border-dashed rounded-lg transition-colors ${
                  darkMode
                    ? 'border-gray-600 hover:border-gray-500'
                    : 'border-gray-300 hover:border-gray-400'
                } flex items-center justify-center gap-2`}
              >
                <Upload className="w-4 h-4" />
                Adicionar Arquivo
              </button>

              {attachments.length > 0 && (
                <div className="mt-2 space-y-1">
                  {attachments.map((file, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-2 rounded ${
                        darkMode ? 'bg-gray-700' : 'bg-gray-100'
                      }`}
                    >
                      <span className="text-xs truncate">{file.name}</span>
                      <button
                        onClick={() => removeAttachment(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Reminder */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Calendar className="w-4 h-4 text-gray-500" />
                <label className="text-sm font-medium text-gray-500">Lembrete</label>
              </div>
              <input
                type="datetime-local"
                value={reminder}
                onChange={(e) => setReminder(e.target.value)}
                className={`w-full p-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none`}
              />
            </div>

            {/* Advanced Stats */}
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-white'} border`}>
              <div className="flex items-center gap-2 mb-3">
                <BarChart3 className="w-4 h-4 text-gray-500" />
                <h4 className="text-sm font-medium">Estatísticas</h4>
              </div>
              <div className="text-sm text-gray-500 space-y-2">
                <div className="flex justify-between">
                  <span>Palavras:</span>
                  <span className="font-medium">{wordCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Caracteres:</span>
                  <span className="font-medium">{noteContent.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Parágrafos:</span>
                  <span className="font-medium">{noteContent.split('\n\n').filter(p => p.trim()).length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tempo de leitura:</span>
                  <span className="font-medium">~{readingTime} min</span>
                </div>
                <div className="flex justify-between">
                  <span>Tags:</span>
                  <span className="font-medium">{noteTags.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Anexos:</span>
                  <span className="font-medium">{attachments.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Prioridade:</span>
                  <span className={`font-medium ${
                    priority === 'high' ? 'text-red-500' :
                    priority === 'medium' ? 'text-yellow-500' :
                    'text-green-500'
                  }`}>
                    {priority === 'high' ? 'Alta' : priority === 'medium' ? 'Média' : 'Baixa'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Visibilidade:</span>
                  <span className={`font-medium flex items-center gap-1 ${
                    noteIsPublic ? 'text-green-500' : 'text-gray-500'
                  }`}>
                    {noteIsPublic ? (
                      <>
                        <Globe className="w-3 h-3" />
                        Pública
                      </>
                    ) : (
                      <>
                        <Lock className="w-3 h-3" />
                        Privada
                      </>
                    )}
                  </span>
                </div>
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </>
  );
};
