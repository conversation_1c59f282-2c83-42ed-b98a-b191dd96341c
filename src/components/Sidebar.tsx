import React from 'react';
import {
  Folder, Tag, Star, Archive, Hash, Calendar,
  ChevronRight, X, Settings, Lock, Globe, Clock
} from 'lucide-react';
import { Note } from '../types';
import { useLanguage } from '../hooks/useLanguage';

interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  description?: string;
}

interface SidebarProps {
  darkMode: boolean;
  showSidebar: boolean;
  setShowSidebar: (value: boolean) => void;
  filterCategory: string;
  setFilterCategory: (value: string) => void;
  filterTag: string;
  setFilterTag: (value: string) => void;
  notes: Note[];
  allTags: string[];
  categories: Category[];
  onManageCategories: () => void;
  onEditNote?: (note: Note) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  darkMode,
  showSidebar,
  setShowSidebar,
  filterCategory,
  setFilterCategory,
  filterTag,
  setFilterTag,
  notes,
  allTags,
  categories,
  onManageCategories,
  onEditNote
}) => {
  const { t } = useLanguage();

  if (!showSidebar) return null;

  // Helper functions
  const getCategoryCount = (categoryId: string) => {
    return notes.filter(note => note.category === categoryId).length;
  };

  const getUncategorizedCount = () => {
    return notes.filter(note => !note.category || note.category === '').length;
  };

  const getTagCount = (tag: string) => {
    return notes.filter(note => note.tags.includes(tag)).length;
  };

  const starredCount = notes.filter(note => note.starred).length;
  const pinnedCount = notes.filter(note => note.pinned).length;
  const lockedCount = notes.filter(note => note.locked).length;
  const publicCount = notes.filter(note => note.isPublic).length;

  return (
    <>
      {/* Mobile Overlay */}
      <div
        className={`fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden transition-opacity duration-300 ${
          showSidebar ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={() => setShowSidebar(false)}
      />

      {/* Sidebar */}
      <aside className={`
        fixed left-0 top-20 bottom-0 w-80 z-30 overflow-y-auto transition-transform duration-300
        ${darkMode ? 'bg-gray-900' : 'bg-white'}
        border-r ${darkMode ? 'border-gray-800' : 'border-gray-200'}
        ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
        md:translate-x-0
      `}>
        <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">{t('sidebar.navigation')}</h2>
          <button
            onClick={() => setShowSidebar(false)}
            className={`p-2 rounded-lg transition-all hover:scale-110 md:hidden ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Quick Filters */}
        <div className="space-y-2 mb-8">
          <button
            onClick={() => {
              setFilterCategory('');
              setFilterTag('');
            }}
            className={`w-full flex items-center justify-between p-3 rounded-lg transition-all hover:scale-105 ${!filterCategory && !filterTag ? 'bg-blue-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <div className="flex items-center gap-3">
              <Folder className="w-5 h-5" />
              <span>{t('sidebar.allNotes')}</span>
            </div>
            <span className="text-sm opacity-75">{notes.length}</span>
          </button>

          <button
            onClick={() => {
              setFilterCategory('');
              setFilterTag('starred');
            }}
            className={`w-full flex items-center justify-between p-3 rounded-lg transition-all hover:scale-105 ${filterTag === 'starred' ? 'bg-yellow-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <div className="flex items-center gap-3">
              <Star className="w-5 h-5" />
              <span>{t('sidebar.favoriteNotes')}</span>
            </div>
            <span className="text-sm opacity-75">{starredCount}</span>
          </button>

          <button
            onClick={() => {
              setFilterCategory('');
              setFilterTag('pinned');
            }}
            className={`w-full flex items-center justify-between p-3 rounded-lg transition-all hover:scale-105 ${filterTag === 'pinned' ? 'bg-purple-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <div className="flex items-center gap-3">
              <Archive className="w-5 h-5" />
              <span>{t('sidebar.pinnedNotes')}</span>
            </div>
            <span className="text-sm opacity-75">{pinnedCount}</span>
          </button>

          <button
            onClick={() => {
              setFilterCategory('');
              setFilterTag('locked');
            }}
            className={`w-full flex items-center justify-between p-3 rounded-lg transition-all hover:scale-105 ${filterTag === 'locked' ? 'bg-amber-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <div className="flex items-center gap-3">
              <Lock className="w-5 h-5" />
              <span>{t('sidebar.protectedNotes')}</span>
            </div>
            <span className="text-sm opacity-75">{lockedCount}</span>
          </button>

          <button
            onClick={() => {
              setFilterCategory('');
              setFilterTag('public');
            }}
            className={`w-full flex items-center justify-between p-3 rounded-lg transition-all hover:scale-105 ${filterTag === 'public' ? 'bg-green-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          >
            <div className="flex items-center gap-3">
              <Globe className="w-5 h-5" />
              <span>{t('sidebar.publicNotes')}</span>
            </div>
            <span className="text-sm opacity-75">{publicCount}</span>
          </button>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider">{t('sidebar.categories')}</h3>
            <button
              onClick={onManageCategories}
              className={`p-1.5 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              title={t('sidebar.manageCategories')}
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
          <div className="space-y-1">
            {/* Sem categoria */}
            <button
              onClick={() => {
                setFilterCategory(filterCategory === 'uncategorized' ? '' : 'uncategorized');
                setFilterTag('');
              }}
              className={`w-full flex items-center justify-between p-2 rounded-lg transition-all hover:scale-105 ${
                filterCategory === 'uncategorized' ? 'bg-gray-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-3">
                <span className="text-lg">📝</span>
                <span className="text-sm">{t('sidebar.uncategorized')}</span>
              </div>
              <span className="text-xs opacity-75">{getUncategorizedCount()}</span>
            </button>

            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => {
                  setFilterCategory(filterCategory === category.id ? '' : category.id);
                  setFilterTag('');
                }}
                className={`w-full flex items-center justify-between p-2 rounded-lg transition-all hover:scale-105 capitalize ${filterCategory === category.id ? 'text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                style={{
                  backgroundColor: filterCategory === category.id ? category.color : 'transparent'
                }}
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">{category.icon}</span>
                  <span className="text-sm">{category.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xs opacity-75">{getCategoryCount(category.id)}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Tags */}
        <div>
          <h3 className="text-sm font-semibold text-gray-500 mb-3 uppercase tracking-wider">{t('sidebar.tags')}</h3>
          <div className="space-y-1">
            {allTags.slice(0, 10).map(tag => (
              <button
                key={tag}
                onClick={() => {
                  setFilterTag(filterTag === tag ? '' : tag);
                  setFilterCategory('');
                }}
                className={`w-full flex items-center justify-between p-2 rounded-lg transition-all hover:scale-105 ${filterTag === tag ? 'bg-purple-500 text-white' : darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              >
                <div className="flex items-center gap-3">
                  <Hash className="w-4 h-4" />
                  <span className="text-sm">{tag}</span>
                </div>
                <span className="text-xs opacity-75">{getTagCount(tag)}</span>
              </button>
            ))}

            {allTags.length > 10 && (
              <button className={`w-full p-2 text-sm text-gray-500 hover:text-gray-300 transition-colors`}>
                +{allTags.length - 10} {t('sidebar.moreTags')}
              </button>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-8">
          <h3 className="text-sm font-semibold text-gray-500 mb-3 uppercase tracking-wider">{t('sidebar.recentActivity')}</h3>
          <div className="space-y-2">
            {notes.slice(0, 5).map(note => (
              <button
                key={note.id}
                onClick={() => onEditNote?.(note)}
                className={`w-full p-2 rounded-lg text-left transition-all hover:scale-105 ${
                  darkMode ? 'bg-gray-800/50 hover:bg-gray-800' : 'bg-gray-100/50 hover:bg-gray-200'
                }`}
              >
                <p className="text-sm font-medium truncate">{note.title}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="w-3 h-3 text-gray-500" />
                  <span className="text-xs text-gray-500">
                    {new Date(note.updatedAt).toLocaleDateString('pt-BR')}
                  </span>
                  {note.starred && <Star className="w-3 h-3 text-yellow-400 fill-current" />}
                  {note.pinned && <Archive className="w-3 h-3 text-purple-400" />}
                  {note.locked && <Lock className="w-3 h-3 text-amber-400" />}
                </div>
              </button>
            ))}
          </div>
        </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
