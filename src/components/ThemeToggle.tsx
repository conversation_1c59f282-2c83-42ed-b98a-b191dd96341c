import React, { useState } from 'react';
import { Sun, Moon, Monitor, ChevronDown } from 'lucide-react';
import { useTheme, ThemeMode } from '../hooks/useTheme';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  variant?: 'button' | 'dropdown';
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  showLabel = false,
  variant = 'button'
}) => {
  const { mode, isDark, setThemeMode, cycleTheme, getThemeInfo } = useTheme();
  const [showDropdown, setShowDropdown] = useState(false);

  const themeOptions = [
    {
      mode: 'light' as ThemeMode,
      icon: Sun,
      label: 'Claro',
      description: 'Tema claro sempre ativo'
    },
    {
      mode: 'dark' as ThemeMode,
      icon: Moon,
      label: 'Escuro',
      description: 'Tema escuro sempre ativo'
    },
    {
      mode: 'system' as ThemeMode,
      icon: Monitor,
      label: 'Sistema',
      description: 'Segue a preferência do sistema'
    }
  ];

  const currentTheme = themeOptions.find(option => option.mode === mode);
  const CurrentIcon = currentTheme?.icon || Sun;

  const handleThemeSelect = (selectedMode: ThemeMode) => {
    setShowDropdown(false);
    setThemeMode(selectedMode);
  };

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className={`
            flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200
            ${isDark
              ? 'bg-gray-800 hover:bg-gray-700 text-gray-200 border border-gray-700'
              : 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-200'
            }
            hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
          `}
          title={`Tema atual: ${currentTheme?.label}`}
        >
          <CurrentIcon className="w-4 h-4" />
          {showLabel && <span className="text-sm">{currentTheme?.label}</span>}
          <ChevronDown className={`w-3 h-3 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
        </button>

        {showDropdown && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-10"
              onClick={() => setShowDropdown(false)}
            />

            {/* Dropdown */}
            <div className={`
              absolute right-0 top-full mt-2 w-64 rounded-xl shadow-lg border z-20
              ${isDark
                ? 'bg-gray-800 border-gray-700'
                : 'bg-white border-gray-200'
              }
            `}>
              <div className="p-2">
                {themeOptions.map((option) => {
                  const Icon = option.icon;
                  const isSelected = option.mode === mode;

                  return (
                    <button
                      key={option.mode}
                      onClick={() => handleThemeSelect(option.mode)}
                      className={`
                        w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200
                        ${isSelected
                          ? isDark
                            ? 'bg-blue-600 text-white'
                            : 'bg-blue-500 text-white'
                          : isDark
                            ? 'hover:bg-gray-700 text-gray-200'
                            : 'hover:bg-gray-100 text-gray-700'
                        }
                      `}
                    >
                      <Icon className="w-4 h-4" />
                      <div className="flex-1 text-left">
                        <div className="text-sm font-medium">{option.label}</div>
                        <div className={`text-xs ${
                          isSelected
                            ? 'text-blue-100'
                            : isDark
                              ? 'text-gray-400'
                              : 'text-gray-500'
                        }`}>
                          {option.description}
                        </div>
                      </div>
                      {isSelected && (
                        <div className="w-2 h-2 rounded-full bg-current" />
                      )}
                    </button>
                  );
                })}
              </div>

              {/* Info sobre tema atual */}
              <div className={`
                px-3 py-2 border-t text-xs
                ${isDark
                  ? 'border-gray-700 text-gray-400'
                  : 'border-gray-200 text-gray-500'
                }
              `}>
                {mode === 'system' && (
                  <div className="flex items-center gap-2">
                    <Monitor className="w-3 h-3" />
                    <span>Sistema: {getThemeInfo().systemPreference ? 'Escuro' : 'Claro'}</span>
                  </div>
                )}
                {mode !== 'system' && (
                  <div className="flex items-center gap-2">
                    <CurrentIcon className="w-3 h-3" />
                    <span>Tema fixo: {currentTheme?.label}</span>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  // Variant: button (cicla entre os temas)
  return (
    <button
      onClick={cycleTheme}
      className={`
        p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
        ${isDark
          ? 'hover:bg-gray-800 text-yellow-400'
          : 'hover:bg-gray-100 text-gray-600'
        }
        ${className}
      `}
      title={`Tema: ${currentTheme?.label} (clique para alternar)`}
    >
      <CurrentIcon className="w-5 h-5" />
    </button>
  );
};

export default ThemeToggle;
