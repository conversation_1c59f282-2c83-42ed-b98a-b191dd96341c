import React, { useState, useMemo } from 'react';
import {
  X,
  BarChart3,
  TrendingUp,
  Calendar,
  Clock,
  FileText,
  Tag,
  Star,
  Target,
  Zap,
  Users,
  Eye,
  Heart,
  Award,
  Activity,
  PieChart,
  LineChart
} from 'lucide-react';

interface AnalyticsModalProps {
  show: boolean;
  darkMode: boolean;
  onClose: () => void;
  notes: any[];
  userProfile: any;
}

export const AnalyticsModal: React.FC<AnalyticsModalProps> = ({
  show,
  darkMode,
  onClose,
  notes,
  userProfile
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'productivity' | 'content' | 'trends'>('overview');

  // Calculate analytics data
  const analytics = useMemo(() => {
    if (!notes || notes.length === 0) {
      return {
        totalNotes: 0,
        totalWords: 0,
        starredNotes: 0,
        publicNotes: 0,
        lockedNotes: 0,
        categoryStats: {},
        tagStats: {},
        recentNotes: 0,
        avgWordsPerNote: 0,
        mostProductiveDay: 'N/A',
        dailyStreak: 0,
        totalReadTime: 0,
        longestNote: 0,
        shortestNote: 0,
        notesThisMonth: 0,
        wordsThisMonth: 0,
        averageNotesPerDay: 0,
        dayStats: {},
        monthStats: {},
        moodStats: {}
      };
    }

    const totalNotes = notes.length;
    const totalWords = notes.reduce((sum, note) => sum + (note.wordCount || 0), 0);
    const starredNotes = notes.filter(note => note.starred).length;
    const publicNotes = notes.filter(note => note.isPublic).length;
    const lockedNotes = notes.filter(note => note.locked).length;

    // Category distribution
    const categoryStats = notes.reduce((acc, note) => {
      const category = note.category || 'sem categoria';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Tag usage
    const tagStats = notes.reduce((acc, note) => {
      (note.tags || []).forEach((tag: string) => {
        acc[tag] = (acc[tag] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    // Mood statistics
    const moodStats = notes.reduce((acc, note) => {
      if (note.mood && note.mood.trim()) {
        acc[note.mood] = (acc[note.mood] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    // Time-based analytics
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const recentNotes = notes.filter(note =>
      new Date(note.createdAt) >= sevenDaysAgo
    ).length;

    const notesThisMonth = notes.filter(note =>
      new Date(note.createdAt) >= startOfMonth
    ).length;

    const wordsThisMonth = notes
      .filter(note => new Date(note.createdAt) >= startOfMonth)
      .reduce((sum, note) => sum + (note.wordCount || 0), 0);

    // Average words per note
    const avgWordsPerNote = totalNotes > 0 ? Math.round(totalWords / totalNotes) : 0;

    // Total read time (assuming 200 words per minute)
    const totalReadTime = Math.round(totalWords / 200);

    // Longest and shortest notes
    const wordCounts = notes.map(note => note.wordCount || 0).filter(count => count > 0);
    const longestNote = wordCounts.length > 0 ? Math.max(...wordCounts) : 0;
    const shortestNote = wordCounts.length > 0 ? Math.min(...wordCounts) : 0;

    // Day of week statistics
    const dayStats = notes.reduce((acc, note) => {
      const day = new Date(note.createdAt).toLocaleDateString('pt-BR', { weekday: 'long' });
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Month statistics (last 12 months)
    const monthStats = notes.reduce((acc, note) => {
      const month = new Date(note.createdAt).toLocaleDateString('pt-BR', { year: 'numeric', month: 'short' });
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostProductiveDay = Object.entries(dayStats).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';

    // Calculate average notes per day (based on account age)
    const oldestNote = notes.reduce((oldest, note) => {
      const noteDate = new Date(note.createdAt);
      return noteDate < oldest ? noteDate : oldest;
    }, new Date());

    const daysSinceFirstNote = Math.max(1, Math.floor((now.getTime() - oldestNote.getTime()) / (24 * 60 * 60 * 1000)));
    const averageNotesPerDay = totalNotes / daysSinceFirstNote;

    return {
      totalNotes,
      totalWords,
      starredNotes,
      publicNotes,
      lockedNotes,
      categoryStats,
      tagStats,
      moodStats,
      recentNotes,
      avgWordsPerNote,
      mostProductiveDay,
      dailyStreak: userProfile?.stats?.dailyStreak || 0,
      totalReadTime,
      longestNote,
      shortestNote,
      notesThisMonth,
      wordsThisMonth,
      averageNotesPerDay,
      dayStats,
      monthStats
    };
  }, [notes, userProfile]);

  if (!show) return null;

  const tabs = [
    { id: 'overview', label: 'Visão Geral', icon: BarChart3 },
    { id: 'productivity', label: 'Produtividade', icon: TrendingUp },
    { id: 'content', label: 'Conteúdo', icon: FileText },
    { id: 'trends', label: 'Tendências', icon: Activity }
  ];

  const StatCard = ({ icon: Icon, title, value, subtitle, color = 'blue' }: any) => (
    <div className={`p-4 rounded-xl border transition-all hover:shadow-md ${
      darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
    }`}>
      <div className="flex items-center gap-3 mb-2">
        <div className={`p-2 rounded-lg bg-${color}-500/10`}>
          <Icon className={`w-5 h-5 text-${color}-500`} />
        </div>
        <h3 className="font-medium text-sm">{title}</h3>
      </div>
      <div className="text-2xl font-bold mb-1">{value}</div>
      {subtitle && (
        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {subtitle}
        </div>
      )}
    </div>
  );

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          icon={FileText}
          title="Total de Notas"
          value={analytics.totalNotes}
          subtitle="notas criadas"
          color="blue"
        />
        <StatCard
          icon={Target}
          title="Palavras Escritas"
          value={analytics.totalWords.toLocaleString()}
          subtitle={`${analytics.avgWordsPerNote} por nota`}
          color="green"
        />
        <StatCard
          icon={Star}
          title="Notas Favoritas"
          value={analytics.starredNotes}
          subtitle={`${Math.round((analytics.starredNotes / analytics.totalNotes) * 100) || 0}% do total`}
          color="yellow"
        />
        <StatCard
          icon={Zap}
          title="Sequência Diária"
          value={`${analytics.dailyStreak} dias`}
          subtitle="consecutivos"
          color="orange"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          icon={Eye}
          title="Notas Públicas"
          value={analytics.publicNotes}
          subtitle="visíveis para outros"
          color="purple"
        />
        <StatCard
          icon={Heart}
          title="Notas Privadas"
          value={analytics.lockedNotes}
          subtitle="protegidas"
          color="red"
        />
        <StatCard
          icon={Activity}
          title="Atividade Recente"
          value={analytics.recentNotes}
          subtitle="últimos 7 dias"
          color="indigo"
        />
      </div>
    </div>
  );

  const renderProductivity = () => (
    <div className="space-y-6">
      {/* Monthly Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          icon={Calendar}
          title="Notas Este Mês"
          value={analytics.notesThisMonth}
          subtitle={`${analytics.wordsThisMonth.toLocaleString()} palavras`}
          color="blue"
        />
        <StatCard
          icon={TrendingUp}
          title="Média Diária"
          value={analytics.averageNotesPerDay.toFixed(1)}
          subtitle="notas por dia"
          color="green"
        />
        <StatCard
          icon={Clock}
          title="Tempo de Leitura"
          value={`${analytics.totalReadTime} min`}
          subtitle="total estimado"
          color="purple"
        />
        <StatCard
          icon={Target}
          title="Nota Mais Longa"
          value={analytics.longestNote.toLocaleString()}
          subtitle="palavras"
          color="orange"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={`p-6 rounded-xl border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            <Calendar className="w-5 h-5 text-blue-500" />
            <h3 className="font-semibold">Dia Mais Produtivo</h3>
          </div>
          <div className="text-3xl font-bold text-blue-500 mb-2 capitalize">
            {analytics.mostProductiveDay}
          </div>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Dia da semana com mais notas criadas
          </p>

          {/* Day statistics */}
          <div className="mt-4 space-y-2">
            {Object.entries(analytics.dayStats)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 3)
              .map(([day, count]) => (
                <div key={day} className="flex items-center justify-between text-sm">
                  <span className="capitalize">{day}</span>
                  <span className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {count} notas
                  </span>
                </div>
              ))}
          </div>
        </div>

        <div className={`p-6 rounded-xl border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            <Activity className="w-5 h-5 text-green-500" />
            <h3 className="font-semibold">Estatísticas de Escrita</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Nota mais longa:
              </span>
              <span className="font-medium">{analytics.longestNote} palavras</span>
            </div>
            <div className="flex justify-between">
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Nota mais curta:
              </span>
              <span className="font-medium">{analytics.shortestNote} palavras</span>
            </div>
            <div className="flex justify-between">
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Média por nota:
              </span>
              <span className="font-medium">{analytics.avgWordsPerNote} palavras</span>
            </div>
            <div className="flex justify-between">
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total de palavras:
              </span>
              <span className="font-medium">{analytics.totalWords.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>

      <div className={`p-6 rounded-xl border ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="flex items-center gap-3 mb-4">
          <Award className="w-5 h-5 text-purple-500" />
          <h3 className="font-semibold">Conquistas</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 rounded-lg bg-blue-500/10">
            <div className="text-2xl mb-2">🏆</div>
            <div className="font-medium">Escritor Ativo</div>
            <div className="text-sm text-gray-500">{analytics.totalNotes}+ notas</div>
          </div>
          <div className="text-center p-4 rounded-lg bg-green-500/10">
            <div className="text-2xl mb-2">⭐</div>
            <div className="font-medium">Organizador</div>
            <div className="text-sm text-gray-500">{analytics.starredNotes} favoritas</div>
          </div>
          <div className="text-center p-4 rounded-lg bg-orange-500/10">
            <div className="text-2xl mb-2">🔥</div>
            <div className="font-medium">Consistente</div>
            <div className="text-sm text-gray-500">{analytics.dailyStreak} dias</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Categories */}
        <div className={`p-6 rounded-xl border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            <PieChart className="w-5 h-5 text-blue-500" />
            <h3 className="font-semibold">Distribuição por Categoria</h3>
          </div>
          <div className="space-y-3">
            {Object.entries(analytics.categoryStats).length > 0 ? (
              Object.entries(analytics.categoryStats)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([category, count]) => (
                  <div key={category} className="flex items-center justify-between">
                    <span className="capitalize">{category}</span>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 rounded-full bg-blue-500`}
                           style={{ width: `${Math.max(20, (count / analytics.totalNotes) * 100)}px` }} />
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                  </div>
                ))
            ) : (
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Nenhuma categoria encontrada
              </p>
            )}
          </div>
        </div>

        {/* Tags */}
        <div className={`p-6 rounded-xl border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            <Tag className="w-5 h-5 text-green-500" />
            <h3 className="font-semibold">Tags Mais Usadas</h3>
          </div>
          <div className="flex flex-wrap gap-2">
            {Object.entries(analytics.tagStats).length > 0 ? (
              Object.entries(analytics.tagStats)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([tag, count]) => (
                  <span
                    key={tag}
                    className={`px-3 py-1 rounded-full text-sm ${
                      darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    {tag} ({count})
                  </span>
                ))
            ) : (
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Nenhuma tag encontrada
              </p>
            )}
          </div>
        </div>

        {/* Moods */}
        <div className={`p-6 rounded-xl border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            <Heart className="w-5 h-5 text-pink-500" />
            <h3 className="font-semibold">Estados de Humor</h3>
          </div>
          <div className="space-y-3">
            {Object.entries(analytics.moodStats).length > 0 ? (
              Object.entries(analytics.moodStats)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([mood, count]) => {
                  const moodEmojis: Record<string, string> = {
                    'feliz': '😊',
                    'triste': '😢',
                    'animado': '🎉',
                    'calmo': '😌',
                    'ansioso': '😰',
                    'inspirado': '✨',
                    'cansado': '😴',
                    'focado': '🎯',
                    'criativo': '🎨',
                    'reflexivo': '🤔'
                  };

                  return (
                    <div key={mood} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{moodEmojis[mood] || '😐'}</span>
                        <span className="capitalize">{mood}</span>
                      </div>
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                  );
                })
            ) : (
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Nenhum humor registrado
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderTrends = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Monthly Trends */}
        <div className={`p-6 rounded-xl border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            <LineChart className="w-5 h-5 text-purple-500" />
            <h3 className="font-semibold">Tendência Mensal</h3>
          </div>
          <div className="space-y-3">
            {Object.entries(analytics.monthStats).length > 0 ? (
              Object.entries(analytics.monthStats)
                .sort(([a], [b]) => new Date(a + ' 1').getTime() - new Date(b + ' 1').getTime())
                .slice(-6) // Last 6 months
                .map(([month, count]) => (
                  <div key={month} className="flex items-center justify-between">
                    <span className="text-sm">{month}</span>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 rounded-full bg-purple-500`}
                           style={{ width: `${Math.max(20, (count / Math.max(...Object.values(analytics.monthStats))) * 100)}px` }} />
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                  </div>
                ))
            ) : (
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Dados insuficientes para tendências
              </p>
            )}
          </div>
        </div>

        {/* Weekly Pattern */}
        <div className={`p-6 rounded-xl border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="w-5 h-5 text-blue-500" />
            <h3 className="font-semibold">Padrão Semanal</h3>
          </div>
          <div className="space-y-3">
            {Object.entries(analytics.dayStats).length > 0 ? (
              ['segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado', 'domingo']
                .map(day => {
                  const count = analytics.dayStats[day] || 0;
                  const maxCount = Math.max(...Object.values(analytics.dayStats));
                  return (
                    <div key={day} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{day.slice(0, 3)}</span>
                      <div className="flex items-center gap-2">
                        <div className={`h-2 rounded-full bg-blue-500`}
                             style={{ width: `${Math.max(10, maxCount > 0 ? (count / maxCount) * 80 : 0)}px` }} />
                        <span className="text-sm font-medium w-6 text-right">{count}</span>
                      </div>
                    </div>
                  );
                })
            ) : (
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Nenhum padrão semanal encontrado
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          icon={TrendingUp}
          title="Crescimento"
          value={analytics.recentNotes > 0 ? '+' + analytics.recentNotes : '0'}
          subtitle="notas nos últimos 7 dias"
          color="green"
        />
        <StatCard
          icon={Target}
          title="Consistência"
          value={`${(analytics.averageNotesPerDay * 7).toFixed(1)}`}
          subtitle="notas por semana"
          color="blue"
        />
        <StatCard
          icon={Award}
          title="Produtividade"
          value={analytics.totalWords > 1000 ? 'Alta' : analytics.totalWords > 500 ? 'Média' : 'Baixa'}
          subtitle="baseado no total de palavras"
          color="purple"
        />
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className={`w-full max-w-6xl max-h-[90vh] ${
        darkMode ? 'bg-gray-900' : 'bg-white'
      } rounded-2xl shadow-2xl overflow-hidden`}>
        {/* Header */}
        <div className={`p-6 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Analytics</h2>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Insights sobre sua produtividade e conteúdo
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${
                darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex gap-1 mt-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                  activeTab === tab.id
                    ? 'bg-blue-500 text-white'
                    : darkMode
                      ? 'hover:bg-gray-800 text-gray-400'
                      : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="text-sm font-medium">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'productivity' && renderProductivity()}
          {activeTab === 'content' && renderContent()}
          {activeTab === 'trends' && renderTrends()}
        </div>
      </div>
    </div>
  );
};
