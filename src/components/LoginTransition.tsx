import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  Sparkles, 
  ArrowRight, 
  User, 
  FileText, 
  Zap,
  Target,
  Heart
} from 'lucide-react';

interface LoginTransitionProps {
  show: boolean;
  darkMode: boolean;
  userProfile: any;
  onComplete: () => void;
}

export const LoginTransition: React.FC<LoginTransitionProps> = ({
  show,
  darkMode,
  userProfile,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showWelcome, setShowWelcome] = useState(false);
  const [showFeatures, setShowFeatures] = useState(false);
  const [showComplete, setShowComplete] = useState(false);

  const steps = [
    {
      icon: CheckCircle,
      title: 'Login Realizado!',
      description: 'Bem-vindo de volta ao NoteFlow',
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/30'
    },
    {
      icon: User,
      title: 'Carregando Perfil',
      description: 'Sincronizando suas informações',
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/30'
    },
    {
      icon: FileText,
      title: 'Preparando Notas',
      description: 'Organizando seu workspace',
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-500/30'
    },
    {
      icon: Zap,
      title: 'Tudo Pronto!',
      description: 'Vamos começar a criar',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500/10',
      borderColor: 'border-yellow-500/30'
    }
  ];

  const features = [
    {
      icon: Target,
      title: 'Foco Total',
      description: 'Modo foco e Pomodoro'
    },
    {
      icon: Sparkles,
      title: 'IA Integrada',
      description: 'Assistente inteligente'
    },
    {
      icon: Heart,
      title: 'Sincronização',
      description: 'Dados sempre seguros'
    }
  ];

  useEffect(() => {
    if (!show) return;

    const timer1 = setTimeout(() => {
      setShowWelcome(true);
    }, 500);

    const timer2 = setTimeout(() => {
      setCurrentStep(1);
    }, 1500);

    const timer3 = setTimeout(() => {
      setCurrentStep(2);
    }, 2500);

    const timer4 = setTimeout(() => {
      setCurrentStep(3);
      setShowFeatures(true);
    }, 3500);

    const timer5 = setTimeout(() => {
      setShowComplete(true);
    }, 4500);

    const timer6 = setTimeout(() => {
      onComplete();
    }, 6000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      clearTimeout(timer4);
      clearTimeout(timer5);
      clearTimeout(timer6);
    };
  }, [show, onComplete]);

  if (!show) return null;

  const currentStepData = steps[currentStep];

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 z-50 flex items-center justify-center">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center text-white max-w-md mx-auto px-6">
        {/* Logo */}
        <div className={`mb-8 transform transition-all duration-1000 ${
          showWelcome ? 'scale-100 opacity-100' : 'scale-50 opacity-0'
        }`}>
          <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4 border border-white/30">
            <span className="text-3xl font-bold">N</span>
          </div>
          <h1 className="text-3xl font-bold mb-2">NoteFlow</h1>
          <p className="text-white/80">Organize suas ideias</p>
        </div>

        {/* Welcome Message */}
        <div className={`mb-8 transform transition-all duration-1000 delay-300 ${
          showWelcome ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
        }`}>
          <h2 className="text-2xl font-bold mb-2">
            Olá, {userProfile?.displayName || 'Usuário'}! 👋
          </h2>
          <p className="text-white/80">
            Bem-vindo de volta ao seu espaço de produtividade
          </p>
        </div>

        {/* Progress Steps */}
        <div className={`mb-8 transform transition-all duration-1000 delay-500 ${
          showWelcome ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
        }`}>
          <div className={`p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 ${currentStepData.bgColor}`}>
            <div className="flex items-center justify-center mb-4">
              <div className={`p-3 rounded-full ${currentStepData.bgColor} border ${currentStepData.borderColor}`}>
                <currentStepData.icon className={`w-8 h-8 ${currentStepData.color}`} />
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2">{currentStepData.title}</h3>
            <p className="text-white/80">{currentStepData.description}</p>
          </div>

          {/* Progress Bar */}
          <div className="mt-6">
            <div className="flex justify-between mb-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-500 ${
                    index <= currentStep ? 'bg-white' : 'bg-white/30'
                  }`}
                />
              ))}
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div
                className="bg-white h-2 rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Features Preview */}
        <div className={`mb-8 transform transition-all duration-1000 delay-700 ${
          showFeatures ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
        }`}>
          <h3 className="text-lg font-semibold mb-4">O que você pode fazer:</h3>
          <div className="grid grid-cols-1 gap-3">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`flex items-center gap-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 transform transition-all duration-500 delay-${index * 100}`}
                style={{ 
                  transform: showFeatures ? 'translateX(0)' : 'translateX(-20px)',
                  opacity: showFeatures ? 1 : 0,
                  transitionDelay: `${700 + index * 100}ms`
                }}
              >
                <div className="p-2 bg-white/20 rounded-lg">
                  <feature.icon className="w-5 h-5" />
                </div>
                <div className="text-left">
                  <h4 className="font-medium">{feature.title}</h4>
                  <p className="text-sm text-white/70">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Complete Button */}
        <div className={`transform transition-all duration-1000 delay-1000 ${
          showComplete ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
        }`}>
          <button
            onClick={onComplete}
            className="group px-8 py-4 bg-white text-purple-600 rounded-xl font-semibold hover:bg-white/90 transition-all duration-300 flex items-center gap-3 mx-auto hover:scale-105 shadow-lg"
          >
            <span>Começar a Criar</span>
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </button>
          <p className="text-sm text-white/60 mt-3">
            Pressione qualquer tecla para continuar
          </p>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
    </div>
  );
};
