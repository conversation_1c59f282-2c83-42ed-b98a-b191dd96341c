import React, { useState } from 'react';
import {
  PenTool, Brain, Zap, Shield, Users, Smartphone,
  Star, ArrowRight, CheckCircle, Play, Sparkles,
  BookOpen, Target, Clock, Palette, Search, Share2,
  Lock, Cloud, Mic, Calculator, Table, Image,
  ChevronDown, ChevronUp, Menu, X
} from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';

interface HomepageProps {
  darkMode: boolean;
  onLogin: () => void;
  onRegister: () => void;
}

export const Homepage: React.FC<HomepageProps> = ({ darkMode, onLogin, onRegister }) => {
  const { t } = useLanguage();
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [expandedFeature, setExpandedFeature] = useState<number | null>(null);

  const features = [
    {
      icon: <PenTool className="w-8 h-8" />,
      title: t('homepage.features.advancedEditor.title'),
      description: t('homepage.features.advancedEditor.description'),
      details: [
        t('homepage.features.advancedEditor.details.realTimeFormatting'),
        t('homepage.features.advancedEditor.details.dynamicTables'),
        t('homepage.features.advancedEditor.details.mathFormulas'),
        t('homepage.features.advancedEditor.details.syntaxHighlighting'),
        t('homepage.features.advancedEditor.details.keyboardShortcuts')
      ]
    },
    {
      icon: <Brain className="w-8 h-8" />,
      title: t('homepage.features.mindMap.title'),
      description: t('homepage.features.mindMap.description'),
      details: [
        t('homepage.features.mindMap.details.interactiveVisualization'),
        t('homepage.features.mindMap.details.automaticConnections'),
        t('homepage.features.mindMap.details.categoryFilters'),
        t('homepage.features.mindMap.details.zoomNavigation'),
        t('homepage.features.mindMap.details.imageExport')
      ]
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: t('homepage.features.quickCapture.title'),
      description: t('homepage.features.quickCapture.description'),
      details: [
        t('homepage.features.quickCapture.details.customShortcuts'),
        t('homepage.features.quickCapture.details.commandPalette'),
        t('homepage.features.quickCapture.details.readyTemplates'),
        t('homepage.features.quickCapture.details.voiceCapture'),
        t('homepage.features.quickCapture.details.quickNotes')
      ]
    },
    {
      icon: <Palette className="w-8 h-8" />,
      title: t('homepage.features.customCategories.title'),
      description: t('homepage.features.customCategories.description'),
      details: [
        t('homepage.features.customCategories.details.customColors'),
        t('homepage.features.customCategories.details.variedIcons'),
        t('homepage.features.customCategories.details.defaultCategories'),
        t('homepage.features.customCategories.details.advancedFilters'),
        t('homepage.features.customCategories.details.automaticOrganization')
      ]
    },
    {
      icon: <Search className="w-8 h-8" />,
      title: t('homepage.features.smartSearch.title'),
      description: t('homepage.features.smartSearch.description'),
      details: [
        t('homepage.features.smartSearch.details.realTimeSearch'),
        t('homepage.features.smartSearch.details.multipleFilters'),
        t('homepage.features.smartSearch.details.tagSearch'),
        t('homepage.features.smartSearch.details.searchHistory'),
        t('homepage.features.smartSearch.details.relevantResults')
      ]
    },
    {
      icon: <Cloud className="w-8 h-8" />,
      title: t('homepage.features.synchronization.title'),
      description: t('homepage.features.synchronization.description'),
      details: [
        t('homepage.features.synchronization.details.realTimeSync'),
        t('homepage.features.synchronization.details.automaticBackup'),
        t('homepage.features.synchronization.details.offlineAccess'),
        t('homepage.features.synchronization.details.multipleDevices'),
        t('homepage.features.synchronization.details.guaranteedSecurity')
      ]
    }
  ];

  const stats = [
    { number: "10K+", label: t('homepage.stats.activeUsers') },
    { number: "50K+", label: t('homepage.stats.notesCreated') },
    { number: "99.9%", label: t('homepage.stats.uptime') },
    { number: "4.9★", label: t('homepage.stats.rating') }
  ];

  const testimonials = [
    {
      name: t('homepage.testimonials.ana.name'),
      role: t('homepage.testimonials.ana.role'),
      avatar: "👩‍⚕️",
      text: t('homepage.testimonials.ana.text')
    },
    {
      name: t('homepage.testimonials.carlos.name'),
      role: t('homepage.testimonials.carlos.role'),
      avatar: "👨‍💻",
      text: t('homepage.testimonials.carlos.text')
    },
    {
      name: t('homepage.testimonials.maria.name'),
      role: t('homepage.testimonials.maria.role'),
      avatar: "👩‍🏫",
      text: t('homepage.testimonials.maria.text')
    }
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}`}>
      {/* Navigation */}
      <nav className={`fixed top-0 w-full z-50 backdrop-blur-md ${darkMode ? 'bg-gray-900/80' : 'bg-white/80'} border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <span className="text-xl font-bold text-white">N</span>
              </div>
              <span className="text-xl font-bold">NoteFlow</span>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="hover:text-blue-500 transition-colors">{t('homepage.nav.features')}</a>
              <a href="#testimonials" className="hover:text-blue-500 transition-colors">{t('homepage.nav.testimonials')}</a>
              <a href="#pricing" className="hover:text-blue-500 transition-colors">{t('homepage.nav.pricing')}</a>
              <button
                onClick={onLogin}
                className="px-4 py-2 text-blue-500 hover:text-blue-600 transition-colors"
              >
                {t('homepage.nav.login')}
              </button>
              <button
                onClick={onRegister}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all hover:scale-105"
              >
                {t('homepage.nav.startFree')}
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              {showMobileMenu ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>

          {/* Mobile Menu */}
          {showMobileMenu && (
            <div className="md:hidden py-4 border-t border-gray-200 dark:border-gray-800">
              <div className="flex flex-col space-y-4">
                <a href="#features" className="hover:text-blue-500 transition-colors">{t('homepage.nav.features')}</a>
                <a href="#testimonials" className="hover:text-blue-500 transition-colors">{t('homepage.nav.testimonials')}</a>
                <a href="#pricing" className="hover:text-blue-500 transition-colors">{t('homepage.nav.pricing')}</a>
                <button
                  onClick={onLogin}
                  className="text-left text-blue-500 hover:text-blue-600 transition-colors"
                >
                  {t('homepage.nav.login')}
                </button>
                <button
                  onClick={onRegister}
                  className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all text-center"
                >
                  {t('homepage.nav.startFree')}
                </button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mb-6">
              <Sparkles className="w-4 h-4 mr-2" />
              Novo: Fórmulas matemáticas e tabelas dinâmicas
            </span>
          </div>

          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Suas ideias,
            <br />
            organizadas perfeitamente
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            O NoteFlow é a plataforma definitiva para capturar, organizar e visualizar suas notas com tecnologia avançada e design intuitivo.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <button
              onClick={onRegister}
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:shadow-xl transition-all hover:scale-105 flex items-center justify-center"
            >
              <Play className="w-5 h-5 mr-2" />
              Começar Grátis
            </button>
            <button
              onClick={onLogin}
              className="px-8 py-4 border-2 border-gray-300 dark:border-gray-600 rounded-xl hover:border-blue-500 transition-all flex items-center justify-center"
            >
              Já tenho conta
              <ArrowRight className="w-5 h-5 ml-2" />
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{stat.number}</div>
                <div className="text-gray-600 dark:text-gray-400">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Funcionalidades Poderosas
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Descubra todas as ferramentas que fazem do NoteFlow a escolha perfeita para suas anotações.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`p-6 rounded-2xl border transition-all hover:shadow-lg cursor-pointer ${
                  darkMode ? 'bg-gray-900 border-gray-700 hover:border-blue-500' : 'bg-white border-gray-200 hover:border-blue-300'
                }`}
                onClick={() => setExpandedFeature(expandedFeature === index ? null : index)}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-semibold">{feature.title}</h3>
                  </div>
                  {expandedFeature === index ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {feature.description}
                </p>

                {expandedFeature === index && (
                  <div className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-300">{detail}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              O que nossos usuários dizem
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Milhares de pessoas já transformaram sua produtividade com o NoteFlow
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className={`p-6 rounded-2xl border ${
                  darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                } hover:shadow-lg transition-all`}
              >
                <div className="flex items-center space-x-4 mb-4">
                  <div className="text-3xl">{testimonial.avatar}</div>
                  <div>
                    <h4 className="font-semibold">{testimonial.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-600 dark:text-gray-300 italic">
                  "{testimonial.text}"
                </p>
                <div className="flex text-yellow-400 mt-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Planos Simples e Transparentes
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Comece grátis e evolua conforme suas necessidades
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Free Plan */}
            <div className={`p-8 rounded-2xl border ${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2">Gratuito</h3>
                <div className="text-4xl font-bold mb-2">R$ 0</div>
                <p className="text-gray-600 dark:text-gray-400">Para sempre</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Até 100 notas</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Editor básico</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Sincronização</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Categorias básicas</span>
                </li>
              </ul>
              <button
                onClick={onRegister}
                className="w-full py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl hover:border-blue-500 transition-all"
              >
                Começar Grátis
              </button>
            </div>

            {/* Pro Plan */}
            <div className={`p-8 rounded-2xl border-2 border-blue-500 relative ${darkMode ? 'bg-gray-900' : 'bg-white'}`}>
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  Mais Popular
                </span>
              </div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2">Pro</h3>
                <div className="text-4xl font-bold mb-2">R$ 19</div>
                <p className="text-gray-600 dark:text-gray-400">por mês</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Notas ilimitadas</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Editor avançado</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Mapa mental</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Categorias personalizadas</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Colaboração</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Suporte prioritário</span>
                </li>
              </ul>
              <button
                onClick={onRegister}
                className="w-full py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:shadow-lg transition-all hover:scale-105"
              >
                Começar Teste Grátis
              </button>
            </div>

            {/* Enterprise Plan */}
            <div className={`p-8 rounded-2xl border ${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-white border-gray-200'}`}>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2">Enterprise</h3>
                <div className="text-4xl font-bold mb-2">R$ 49</div>
                <p className="text-gray-600 dark:text-gray-400">por usuário/mês</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Tudo do Pro</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>SSO e SAML</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Controles de admin</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>API personalizada</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Suporte 24/7</span>
                </li>
              </ul>
              <button
                onClick={onRegister}
                className="w-full py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl hover:border-blue-500 transition-all"
              >
                Falar com Vendas
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Pronto para transformar suas anotações?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Junte-se a milhares de usuários que já descobriram o poder do NoteFlow
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={onRegister}
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:shadow-xl transition-all hover:scale-105 flex items-center justify-center"
            >
              <Sparkles className="w-5 h-5 mr-2" />
              Começar Grátis Agora
            </button>
            <button
              onClick={onLogin}
              className="px-8 py-4 border-2 border-gray-300 dark:border-gray-600 rounded-xl hover:border-blue-500 transition-all flex items-center justify-center"
            >
              Já tenho conta
              <ArrowRight className="w-5 h-5 ml-2" />
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className={`py-12 px-4 sm:px-6 lg:px-8 border-t ${darkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-gray-50'}`}>
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <span className="text-xl font-bold text-white">N</span>
                </div>
                <span className="text-xl font-bold">NoteFlow</span>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
                A plataforma definitiva para capturar, organizar e visualizar suas ideias com tecnologia avançada.
              </p>
              <div className="flex space-x-4">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Produto</h4>
              <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                <li><a href="#" className="hover:text-blue-500 transition-colors">Funcionalidades</a></li>
                <li><a href="#" className="hover:text-blue-500 transition-colors">Preços</a></li>
                <li><a href="#" className="hover:text-blue-500 transition-colors">API</a></li>
                <li><a href="#" className="hover:text-blue-500 transition-colors">Integrações</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Suporte</h4>
              <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                <li><a href="#" className="hover:text-blue-500 transition-colors">Central de Ajuda</a></li>
                <li><a href="#" className="hover:text-blue-500 transition-colors">Contato</a></li>
                <li><a href="#" className="hover:text-blue-500 transition-colors">Status</a></li>
                <li><a href="#" className="hover:text-blue-500 transition-colors">Comunidade</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-gray-800 mt-8 pt-8 text-center text-gray-600 dark:text-gray-400">
            <p>&copy; 2024 NoteFlow. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
