import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import {
  Star, Pin, Lock, Unlock, Trash2, Copy, Check,
  Download, Share2, Clock, Users, Shield,
  MoreVertical, Loader2, AlertTriangle, Eye, EyeOff
} from 'lucide-react';
import { Note } from '../types';
import { MOODS } from '../constants';

interface NoteCardProps {
  note: Note;
  darkMode: boolean;
  viewMode: 'grid' | 'list';
  copiedId: string | null;
  onEdit: (note: Note) => void;
  onDelete: (id: string) => void;
  onToggleStar: (id: string) => void;
  onTogglePin: (id: string) => void;
  onToggleLock: (id: string) => void;
  onTogglePublic: (id: string) => void;
  onCopy: (note: Note) => void;
  onExport: (note: Note) => void;
  onShare: (note: Note) => void;
}

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  darkMode: boolean;
  noteTitle: string;
}

const ConfirmDeleteModal: React.FC<ConfirmModalProps> = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  darkMode,
  noteTitle 
}) => {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className={`
          ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
          border rounded-xl p-6 max-w-md w-full shadow-2xl animate-in fade-in zoom-in-95 duration-200
        `}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Confirmar exclusão
            </h3>
            <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Esta ação não pode ser desfeita.
            </p>
          </div>
        </div>
        
        <div className={`mb-6 p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-1`}>
            Nota a ser excluída:
          </p>
          <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'} truncate`}>
            {noteTitle || 'Sem título'}
          </p>
        </div>

        <div className="flex gap-3 justify-end">
          <button
            onClick={onClose}
            className={`
              px-4 py-2 rounded-lg font-medium transition-all duration-200
              ${darkMode 
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white border border-gray-600' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900 border border-gray-300'
              }
              hover:scale-105 active:scale-95
            `}
          >
            Cancelar
          </button>
          <button
            onClick={onConfirm}
            className="
              px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium 
              transition-all duration-200 hover:scale-105 active:scale-95
              hover:shadow-lg active:shadow-md
            "
          >
            Excluir
          </button>
        </div>
      </div>
    </div>
  );
};

const NoteCard: React.FC<NoteCardProps> = ({
  note,
  darkMode,
  viewMode,
  copiedId,
  onEdit,
  onDelete,
  onToggleStar,
  onTogglePin,
  onToggleLock,
  onTogglePublic,
  onCopy,
  onExport,
  onShare,
}) => {
  const [showActionsMenu, setShowActionsMenu] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [loadingAction, setLoadingAction] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Enhanced click outside detection
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current && 
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowActionsMenu(false);
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setShowActionsMenu(false);
    };

    if (showActionsMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showActionsMenu]);

  // Enhanced action handlers with loading states
  const handleAction = useCallback(async (actionType: string, action: () => void) => {
    setLoadingAction(actionType);
    try {
      await action();
    } finally {
      setTimeout(() => setLoadingAction(null), 500);
    }
  }, []);

  const handleDeleteConfirm = useCallback(() => {
    handleAction('delete', () => onDelete(note.id));
    setShowDeleteModal(false);
    setShowActionsMenu(false);
  }, [note.id, onDelete, handleAction]);

  // Enhanced mood configuration
  const moodConfig = useMemo(() => {
    const mood = MOODS.find(m => m.id === note.mood);
    return mood ? {
      ...mood,
      bgGradient: `bg-gradient-to-br from-${mood.color.replace('#', '')}-50 to-${mood.color.replace('#', '')}-100`,
      darkBgGradient: `dark:from-${mood.color.replace('#', '')}-900/20 dark:to-${mood.color.replace('#', '')}-800/20`,
      borderColor: `border-${mood.color.replace('#', '')}-200 dark:border-${mood.color.replace('#', '')}-700/50`,
      accentColor: `text-${mood.color.replace('#', '')}-600 dark:text-${mood.color.replace('#', '')}-400`
    } : null;
  }, [note.mood]);

  const isCopied = copiedId === note.id;
  const isProtected = note.locked;
  const hasActions = note.starred || note.pinned || note.isPublic;

  // Enhanced date formatting
  const formatDate = useCallback((date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Hoje';
    if (diffDays === 2) return 'Ontem';
    if (diffDays <= 7) return `${diffDays} dias atrás`;
    return date.toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: 'short',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }, []);

  // Enhanced card classes
  const cardClasses = useMemo(() => {
    const baseClasses = `
      relative group transition-all duration-300 ease-out cursor-pointer
      transform-gpu will-change-transform
      ${viewMode === 'list' ? 'flex items-start gap-4 p-4' : 'flex flex-col h-full'}
      ${darkMode 
        ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' 
        : 'bg-white border-gray-200 hover:bg-gray-50'
      }
      border rounded-xl overflow-hidden
      ${moodConfig?.borderColor || ''}
      ${isHovered ? 'shadow-lg -translate-y-1' : 'shadow-sm'}
      ${isPressed ? 'scale-[0.98]' : 'scale-100'}
      ${note.pinned ? 'ring-2 ring-blue-500/20' : ''}
      hover:shadow-xl hover:-translate-y-2
      active:scale-[0.98] active:shadow-md
    `;
    return baseClasses.replace(/\s+/g, ' ').trim();
  }, [viewMode, darkMode, moodConfig, isHovered, isPressed, note.pinned]);

  return (
    <>
      <div
        className={cardClasses}
        onClick={() => onEdit(note)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => {
          setIsHovered(false);
          setIsPressed(false);
        }}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        role="button"
        tabIndex={0}
        aria-label={`Editar nota: ${note.title || 'Sem título'}`}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onEdit(note);
          }
        }}
      >
        {/* Enhanced background gradient */}
        {moodConfig && (
          <div className={`absolute inset-0 ${moodConfig.bgGradient} ${moodConfig.darkBgGradient} opacity-30`} />
        )}

        {/* Pin indicator */}
        {note.pinned && (
          <div className="absolute top-2 left-2 z-10">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
          </div>
        )}

        {/* Enhanced action buttons - positioned based on view mode */}
        <div className={`
          absolute z-20 flex items-center gap-1
          ${viewMode === 'list' ? 'top-2 right-2' : 'top-3 right-3'}
          opacity-0 group-hover:opacity-100 transition-all duration-200
          transform translate-y-1 group-hover:translate-y-0
        `}>
          {/* Quick action buttons */}
          <div className="flex items-center gap-1 mr-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleAction('star', () => onToggleStar(note.id));
              }}
              className={`
                p-1.5 rounded-full transition-all duration-200 
                hover:scale-110 active:scale-95
                ${note.starred 
                  ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400' 
                  : `${darkMode ? 'bg-gray-700 text-gray-400 hover:text-yellow-400' : 'bg-gray-100 text-gray-500 hover:text-yellow-600'}`
                }
                hover:shadow-md
              `}
              disabled={loadingAction === 'star'}
              aria-label={note.starred ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
            >
              {loadingAction === 'star' ? (
                <Loader2 className="w-3.5 h-3.5 animate-spin" />
              ) : (
                <Star 
                  className={`w-3.5 h-3.5 transition-transform duration-200 ${note.starred ? 'fill-current scale-110' : ''}`} 
                />
              )}
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation();
                handleAction('pin', () => onTogglePin(note.id));
              }}
              className={`
                p-1.5 rounded-full transition-all duration-200 
                hover:scale-110 active:scale-95
                ${note.pinned 
                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400' 
                  : `${darkMode ? 'bg-gray-700 text-gray-400 hover:text-blue-400' : 'bg-gray-100 text-gray-500 hover:text-blue-600'}`
                }
                hover:shadow-md
              `}
              disabled={loadingAction === 'pin'}
              aria-label={note.pinned ? 'Desafixar nota' : 'Fixar nota'}
            >
              {loadingAction === 'pin' ? (
                <Loader2 className="w-3.5 h-3.5 animate-spin" />
              ) : (
                <Pin 
                  className={`w-3.5 h-3.5 transition-transform duration-200 ${note.pinned ? 'fill-current scale-110' : ''}`} 
                />
              )}
            </button>
          </div>

          {/* Enhanced actions menu */}
          <div className="relative">
            <button
              ref={buttonRef}
              onClick={(e) => {
                e.stopPropagation();
                setShowActionsMenu(!showActionsMenu);
              }}
              className={`
                p-1.5 rounded-full transition-all duration-200 
                hover:scale-110 active:scale-95 hover:shadow-md
                ${darkMode ? 'bg-gray-700 text-gray-400 hover:text-white' : 'bg-gray-100 text-gray-500 hover:text-gray-700'}
                ${showActionsMenu ? 'ring-2 ring-blue-500/20 bg-blue-50 dark:bg-blue-900/20' : ''}
              `}
              aria-label="Mais opções"
              aria-expanded={showActionsMenu}
            >
              <MoreVertical className="w-3.5 h-3.5" />
            </button>

            {/* Enhanced dropdown menu */}
            {showActionsMenu && (
              <div
                ref={menuRef}
                className={`
                  absolute right-0 mt-2 w-52 rounded-xl shadow-xl border z-30
                  ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
                  animate-in fade-in slide-in-from-top-2 duration-200
                  before:absolute before:-top-1 before:right-4 before:w-2 before:h-2 
                  before:rotate-45 before:border-l before:border-t 
                  ${darkMode ? 'before:border-gray-700 before:bg-gray-800' : 'before:border-gray-200 before:bg-white'}
                `}
              >
                <div className="py-2">
                  {/* Primary actions */}
                  <div className="px-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAction('copy', () => onCopy(note));
                        setShowActionsMenu(false);
                      }}
                      disabled={loadingAction === 'copy'}
                      className={`
                        w-full px-3 py-2.5 text-left text-sm flex items-center gap-3 transition-all duration-200 rounded-lg
                        ${darkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-50 text-gray-700'}
                        hover:translate-x-1 disabled:opacity-50
                      `}
                    >
                      {loadingAction === 'copy' ? (
                        <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                      ) : isCopied ? (
                        <Check className="w-4 h-4 text-green-500" />
                      ) : (
                        <Copy className="w-4 h-4 text-blue-500" />
                      )}
                      <span className="flex-1">
                        {isCopied ? 'Copiado!' : 'Copiar conteúdo'}
                      </span>
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAction('lock', () => onToggleLock(note.id));
                        setShowActionsMenu(false);
                      }}
                      disabled={loadingAction === 'lock'}
                      className={`
                        w-full px-3 py-2.5 text-left text-sm flex items-center gap-3 transition-all duration-200 rounded-lg
                        ${darkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-50 text-gray-700'}
                        hover:translate-x-1 disabled:opacity-50
                      `}
                    >
                      {loadingAction === 'lock' ? (
                        <Loader2 className="w-4 h-4 animate-spin text-amber-500" />
                      ) : note.locked ? (
                        <Unlock className="w-4 h-4 text-amber-500" />
                      ) : (
                        <Lock className="w-4 h-4 text-amber-500" />
                      )}
                      <span className="flex-1">
                        {note.locked ? 'Desbloquear' : 'Bloquear'}
                      </span>
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAction('public', () => onTogglePublic(note.id));
                        setShowActionsMenu(false);
                      }}
                      disabled={loadingAction === 'public'}
                      className={`
                        w-full px-3 py-2.5 text-left text-sm flex items-center gap-3 transition-all duration-200 rounded-lg
                        ${darkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-50 text-gray-700'}
                        hover:translate-x-1 disabled:opacity-50
                      `}
                    >
                      {loadingAction === 'public' ? (
                        <Loader2 className="w-4 h-4 animate-spin text-green-500" />
                      ) : note.isPublic ? (
                        <EyeOff className="w-4 h-4 text-green-500" />
                      ) : (
                        <Eye className="w-4 h-4 text-green-500" />
                      )}
                      <span className="flex-1">
                        {note.isPublic ? 'Ocultar publicamente' : 'Tornar pública'}
                      </span>
                    </button>
                  </div>

                  <div className="border-t border-gray-200 dark:border-gray-600 my-2"></div>

                  {/* Secondary actions */}
                  <div className="px-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAction('export', () => onExport(note));
                        setShowActionsMenu(false);
                      }}
                      disabled={loadingAction === 'export'}
                      className={`
                        w-full px-3 py-2.5 text-left text-sm flex items-center gap-3 transition-all duration-200 rounded-lg
                        ${darkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-50 text-gray-700'}
                        hover:translate-x-1 disabled:opacity-50
                      `}
                    >
                      {loadingAction === 'export' ? (
                        <Loader2 className="w-4 h-4 animate-spin text-purple-500" />
                      ) : (
                        <Download className="w-4 h-4 text-purple-500" />
                      )}
                      <span className="flex-1">Exportar</span>
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAction('share', () => onShare(note));
                        setShowActionsMenu(false);
                      }}
                      disabled={loadingAction === 'share'}
                      className={`
                        w-full px-3 py-2.5 text-left text-sm flex items-center gap-3 transition-all duration-200 rounded-lg
                        ${darkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-50 text-gray-700'}
                        hover:translate-x-1 disabled:opacity-50
                      `}
                    >
                      {loadingAction === 'share' ? (
                        <Loader2 className="w-4 h-4 animate-spin text-indigo-500" />
                      ) : (
                        <Share2 className="w-4 h-4 text-indigo-500" />
                      )}
                      <span className="flex-1">Compartilhar</span>
                    </button>
                  </div>

                  <div className="border-t border-gray-200 dark:border-gray-600 my-2"></div>

                  {/* Danger actions */}
                  <div className="px-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowDeleteModal(true);
                        setShowActionsMenu(false);
                      }}
                      className={`
                        w-full px-3 py-2.5 text-left text-sm flex items-center gap-3 transition-all duration-200 rounded-lg
                        hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400
                        hover:translate-x-1
                      `}
                    >
                      <Trash2 className="w-4 h-4" />
                      <span className="flex-1">Excluir nota</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced content area */}
        <div className={`relative z-10 ${viewMode === 'list' ? 'flex-1' : 'flex-1 p-4'}`}>
          {/* Enhanced header */}
          <div className={`${viewMode === 'list' ? 'mb-2' : 'mb-3'}`}>
            <div className="flex items-start justify-between gap-3 mb-2">
              <h3 className={`
                font-semibold leading-snug line-clamp-2 flex-1
                ${viewMode === 'list' ? 'text-base' : 'text-lg'}
                ${darkMode ? 'text-white' : 'text-gray-900'}
                group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200
              `}>
                {note.title || 'Sem título'}
              </h3>
              
              {/* Enhanced status indicators */}
              <div className="flex items-center gap-1 flex-shrink-0">
                {hasActions && (
                  <div className="flex items-center gap-1">
                    {note.starred && (
                      <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse" />
                    )}
                    {note.pinned && (
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse" />
                    )}
                    {note.isPublic && (
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse" />
                    )}
                  </div>
                )}
                
                {moodConfig && (
                  <span className={`text-lg ${moodConfig.accentColor}`} title={moodConfig.name}>
                    {moodConfig.emoji}
                  </span>
                )}
              </div>
            </div>

            {/* Enhanced metadata */}
            <div className="flex items-center gap-3 text-xs">
              <div className={`flex items-center gap-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                <Clock className="w-3 h-3" />
                <span>{formatDate(note.createdAt)}</span>
              </div>
              
              {note.isPublic && (
                <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                  <Users className="w-3 h-3" />
                  <span>Público</span>
                </div>
              )}
              
              {isProtected && (
                <div className="flex items-center gap-1 text-amber-600 dark:text-amber-400">
                  <Shield className="w-3 h-3" />
                  <span>Protegida</span>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced content preview */}
          <div className="relative">
            <p className={`
              text-sm leading-relaxed
              ${viewMode === 'list' ? 'line-clamp-2' : 'line-clamp-4'}
              ${darkMode ? 'text-gray-300' : 'text-gray-600'}
              transition-colors duration-200 group-hover:text-gray-700 dark:group-hover:text-gray-200
            `}>
              {note.content || 'Nota vazia'}
            </p>
            
            {/* Enhanced read more indicator */}
            {note.content && note.content.length > (viewMode === 'list' ? 150 : 300) && (
              <div className={`
                absolute bottom-0 right-0 px-2 py-1 text-xs font-medium rounded-tl-lg
                ${darkMode ? 'bg-gray-800 text-blue-400' : 'bg-white text-blue-600'}
                opacity-70 group-hover:opacity-100 transition-opacity duration-200
              `}>
                Continuar lendo...
              </div>
            )}
          </div>

          {/* Enhanced tags */}
          {note.tags && note.tags.length > 0 && (
            <div className="flex flex-wrap gap-1.5 mt-3">
              {note.tags.slice(0, viewMode === 'list' ? 2 : 4).map((tag, index) => (
                <span
                  key={index}
                  className={`
                    px-2 py-1 text-xs font-medium rounded-full transition-all duration-200
                    ${darkMode 
                      ? 'bg-blue-900/30 text-blue-300 border border-blue-700/50' 
                      : 'bg-blue-50 text-blue-700 border border-blue-200'
                    }
                    hover:scale-105
                  `}
                >
                  #{tag}
                </span>
              ))}
              {note.tags.length > (viewMode === 'list' ? 2 : 4) && (
                <span className={`
                  px-2 py-1 text-xs rounded-full
                  ${darkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-100 text-gray-500'}
                `}>
                  +{note.tags.length - (viewMode === 'list' ? 2 : 4)}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Enhanced protected note overlay */}
        {isProtected && (
          <div className={`
            absolute inset-0 backdrop-blur-sm rounded-xl flex items-center justify-center z-20
            ${darkMode ? 'bg-gray-900/80' : 'bg-white/80'}
            transition-opacity duration-300 group-hover:opacity-90
          `}>
            <div className="text-center">
              <div className={`
                w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3
                ${darkMode ? 'bg-amber-900/50' : 'bg-amber-100'}
                ring-2 ring-amber-400/50
              `}>
                <Lock className="w-6 h-6 text-amber-600 dark:text-amber-400" />
              </div>
              <p className={`text-sm font-medium ${darkMode ? 'text-amber-400' : 'text-amber-700'}`}>
                Nota Protegida
              </p>
              <p className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Clique para visualizar
              </p>
            </div>
          </div>
        )}

        {/* Enhanced loading overlay */}
        {loadingAction && (
          <div className={`
            absolute inset-0 backdrop-blur-sm rounded-xl flex items-center justify-center z-30
            ${darkMode ? 'bg-gray-900/60' : 'bg-white/60'}
          `}>
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-blue-500" />
              <p className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Processando...
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced delete confirmation modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        darkMode={darkMode}
        noteTitle={note.title}
      />
    </>
  );
};

export default NoteCard;
