import React from 'react';
import { X, Zap, Command, Plus, Search, Target, BarChart3, UserPlus, FileText, Sidebar, Moon, Folder } from 'lucide-react';
import { Template } from '../types';
import { TEMPLATES } from '../constants';

interface QuickCaptureModalProps {
  show: boolean;
  darkMode: boolean;
  quickCaptureText: string;
  setQuickCaptureText: (text: string) => void;
  onClose: () => void;
  onSave: () => void;
}

export const QuickCaptureModal: React.FC<QuickCaptureModalProps> = ({
  show,
  darkMode,
  quickCaptureText,
  setQuickCaptureText,
  onClose,
  onSave
}) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-6">
      <div className={`w-full max-w-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-500" />
              Quick Capture
            </h3>
            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <textarea
            value={quickCaptureText}
            onChange={(e) => setQuickCaptureText(e.target.value)}
            placeholder="Capture sua ideia rapidamente..."
            className={`w-full h-32 p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} outline-none resize-none`}
            autoFocus
          />

          <div className="flex items-center justify-end mt-4">
            <button
              onClick={onSave}
              className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:shadow-lg transition-all hover:scale-105"
            >
              Salvar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

interface CommandPaletteModalProps {
  show: boolean;
  darkMode: boolean;
  onClose: () => void;
  onNewNote: () => void;
  onQuickCapture: () => void;
  onFocusMode: () => void;
  onAnalytics: () => void;
  onToggleSidebar: () => void;
  onToggleTheme: () => void;
  onShowTemplates: () => void;
  onShowCategories: () => void;
}

export const CommandPaletteModal: React.FC<CommandPaletteModalProps> = ({
  show,
  darkMode,
  onClose,
  onNewNote,
  onQuickCapture,
  onFocusMode,
  onAnalytics,
  onToggleSidebar,
  onToggleTheme,
  onShowTemplates,
  onShowCategories
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [selectedIndex, setSelectedIndex] = React.useState(0);

  if (!show) return null;

  const allCommands = [
    {
      icon: Plus,
      label: 'Nova Nota',
      shortcut: 'Ctrl+N',
      action: onNewNote,
      category: 'Notas'
    },
    {
      icon: Zap,
      label: 'Captura Rápida',
      shortcut: 'Ctrl+Shift+L',
      action: onQuickCapture,
      category: 'Notas'
    },
    {
      icon: FileText,
      label: 'Templates',
      shortcut: 'Ctrl+Shift+T',
      action: onShowTemplates,
      category: 'Notas'
    },
    {
      icon: Target,
      label: 'Modo Foco',
      shortcut: 'Ctrl+Shift+F',
      action: onFocusMode,
      category: 'Interface'
    },
    {
      icon: Sidebar,
      label: 'Alternar Sidebar',
      shortcut: 'Ctrl+B',
      action: onToggleSidebar,
      category: 'Interface'
    },
    {
      icon: Moon,
      label: 'Alternar Tema',
      shortcut: 'Ctrl+T',
      action: onToggleTheme,
      category: 'Interface'
    },
    {
      icon: BarChart3,
      label: 'Analytics',
      shortcut: 'Ctrl+Shift+A',
      action: onAnalytics,
      category: 'Dados'
    },
    {
      icon: Folder,
      label: 'Gerenciar Categorias',
      shortcut: 'Ctrl+Shift+C',
      action: onShowCategories,
      category: 'Organização'
    },
    {
      icon: Search,
      label: 'Buscar Notas',
      shortcut: 'Ctrl+F',
      action: () => {
        const searchInput = document.querySelector('input[placeholder*="Buscar"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      },
      category: 'Navegação'
    }
  ];

  // Filter commands based on search term
  const filteredCommands = allCommands.filter(command =>
    command.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    command.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle keyboard navigation
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!show) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev =>
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev =>
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
            onClose();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [show, filteredCommands, selectedIndex, onClose]);

  // Reset selection when search changes
  React.useEffect(() => {
    setSelectedIndex(0);
  }, [searchTerm]);

  // Reset search when modal opens
  React.useEffect(() => {
    if (show) {
      setSearchTerm('');
      setSelectedIndex(0);
    }
  }, [show]);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-6">
      <div className={`w-full max-w-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}>
        <div className="p-6">
          <div className={`flex items-center gap-2 px-4 py-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
            <Command className="w-5 h-5 text-gray-500" />
            <input
              type="text"
              placeholder="Digite um comando ou busque..."
              className="flex-1 bg-transparent outline-none"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              autoFocus
            />
          </div>

          <div className="mt-4 max-h-80 overflow-y-auto">
            {filteredCommands.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>Nenhum comando encontrado</p>
              </div>
            ) : (
              <div className="space-y-1">
                {filteredCommands.map((command, index) => {
                  const Icon = command.icon;
                  const isSelected = index === selectedIndex;
                  return (
                    <button
                      key={index}
                      onClick={() => {
                        command.action();
                        onClose();
                      }}
                      className={`w-full text-left px-4 py-3 rounded-lg transition-all ${
                        isSelected
                          ? darkMode
                            ? 'bg-blue-600 text-white'
                            : 'bg-blue-500 text-white'
                          : darkMode
                            ? 'hover:bg-gray-800'
                            : 'hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <Icon className="w-4 h-4" />
                        <div className="flex-1">
                          <div className="font-medium">{command.label}</div>
                          <div className={`text-xs ${
                            isSelected
                              ? 'text-blue-100'
                              : 'text-gray-500'
                          }`}>
                            {command.category}
                          </div>
                        </div>
                        {command.shortcut && (
                          <kbd className={`text-xs px-2 py-1 rounded ${
                            isSelected
                              ? 'bg-blue-700 text-blue-100'
                              : 'bg-gray-700 text-gray-300'
                          }`}>
                            {command.shortcut}
                          </kbd>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            )}
          </div>

          <div className={`mt-4 pt-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Use ↑↓ para navegar, Enter para executar</span>
              <span>Esc para fechar</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface TemplatesModalProps {
  show: boolean;
  darkMode: boolean;
  onClose: () => void;
  onUseTemplate: (template: Template) => void;
}

export const TemplatesModal: React.FC<TemplatesModalProps> = ({
  show,
  darkMode,
  onClose,
  onUseTemplate
}) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-6">
      <div className={`w-full max-w-2xl ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">Templates</h2>
            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {TEMPLATES.map((template: Template) => (
              <button
                key={template.id}
                onClick={() => {
                  onUseTemplate(template);
                  onClose();
                }}
                className={`p-6 rounded-xl border-2 border-dashed transition-all hover:scale-105 text-left ${
                  darkMode
                    ? 'border-gray-700 hover:border-gray-600 hover:bg-gray-800/50'
                    : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className={`p-2 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                    <span className="text-xl">{template.icon}</span>
                  </div>
                  <h3 className="font-semibold">{template.name}</h3>
                </div>
                <p className="text-sm text-gray-500">
                  {template.content.replace(/<[^>]*>/g, '').substring(0, 100)}...
                </p>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
