import React, { useState } from 'react'
import {
  Mail,
  Lock,
  User,
  Eye,
  EyeOff,
  Chrome,
  ArrowRight,
  Loader2,
  X,
  Sparkles,
  Shield,
  Zap
} from 'lucide-react'
import { useAuth } from '../hooks/useAuth'
import { useLanguage } from '../hooks/useLanguage'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  darkMode: boolean
  onLoginSuccess?: () => void
}

type AuthMode = 'signin' | 'signup' | 'reset'

export const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, darkMode, onLoginSuccess }) => {
  const [mode, setMode] = useState<AuthMode>('signin')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')

  const { signIn, signUp, signInWithGoogle, loading } = useAuth()
  const { t } = useLanguage()

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (loading) return

    setError('')

    try {
      if (mode === 'signin') {
        await signIn(email, password)
        onLoginSuccess?.()
      } else if (mode === 'signup') {
        await signUp(email, password, displayName)
        onLoginSuccess?.()
      } else if (mode === 'reset') {
        // Simulate password reset
        await new Promise(resolve => setTimeout(resolve, 1000))
        alert(t('auth.recoveryEmailSent'))
        setMode('signin')
        return
      }

      onClose()
      resetForm()
    } catch (error: any) {
      setError(error.message || t('auth.authError'))
    }
  }

  const handleGoogleAuth = async () => {
    if (loading) return

    setError('')

    try {
      await signInWithGoogle()
      onLoginSuccess?.()
      onClose()
      resetForm()
    } catch (error: any) {
      setError(error.message || t('auth.googleAuthError'))
    }
  }

  const resetForm = () => {
    setEmail('')
    setPassword('')
    setDisplayName('')
    setShowPassword(false)
    setError('')
  }

  const switchMode = (newMode: AuthMode) => {
    setMode(newMode)
    resetForm()
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className={`relative w-full max-w-md rounded-2xl shadow-2xl transform transition-all ${
        darkMode ? 'bg-gray-900 border border-gray-800' : 'bg-white border border-gray-200'
      }`}>
        {/* Close Button */}
        <button
          onClick={onClose}
          className={`absolute top-4 right-4 p-2 rounded-lg transition-colors z-10 ${
            darkMode ? 'hover:bg-gray-800 text-gray-400' : 'hover:bg-gray-100 text-gray-600'
          }`}
        >
          <X className="w-5 h-5" />
        </button>

        {/* Header */}
        <div className="p-6 pb-0">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
              <span className="text-2xl font-bold text-white">N</span>
            </div>
            <h2 className="text-2xl font-bold mb-2">
              {mode === 'signin' && (
                <span className="flex items-center justify-center gap-2">
                  <Shield className="w-6 h-6 text-blue-500" />
                  {t('auth.enterNoteFlow')}
                </span>
              )}
              {mode === 'signup' && (
                <span className="flex items-center justify-center gap-2">
                  <Sparkles className="w-6 h-6 text-purple-500" />
                  {t('auth.createAccountTitle')}
                </span>
              )}
              {mode === 'reset' && (
                <span className="flex items-center justify-center gap-2">
                  <Zap className="w-6 h-6 text-orange-500" />
                  {t('auth.recoverPassword')}
                </span>
              )}
            </h2>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {mode === 'signin' && t('auth.enterToAccess')}
              {mode === 'signup' && t('auth.createFreeAccount')}
              {mode === 'reset' && t('auth.enterEmailRecover')}
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Display Name (only for signup) */}
            {mode === 'signup' && (
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {t('auth.name')}
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                      darkMode
                        ? 'bg-gray-800 border-gray-700 text-white focus:border-blue-500'
                        : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-blue-500'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                    placeholder={t('auth.yourName')}
                    required
                  />
                </div>
              </div>
            )}

            {/* Email */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {t('auth.email')}
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                    darkMode
                      ? 'bg-gray-800 border-gray-700 text-white focus:border-blue-500'
                      : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-blue-500'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                  placeholder={t('auth.yourEmail')}
                  required
                />
              </div>
            </div>

            {/* Password (not for reset) */}
            {mode !== 'reset' && (
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {t('auth.password')}
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={`w-full pl-10 pr-12 py-3 rounded-lg border transition-colors ${
                      darkMode
                        ? 'bg-gray-800 border-gray-700 text-white focus:border-blue-500'
                        : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-blue-500'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                    placeholder={t('auth.yourPassword')}
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2 mt-6"
            >
              {loading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <>
                  {mode === 'signin' && (
                    <>
                      <Shield className="w-5 h-5" />
                      {t('auth.enterButton')}
                    </>
                  )}
                  {mode === 'signup' && (
                    <>
                      <Sparkles className="w-5 h-5" />
                      {t('auth.createFreeAccountButton')}
                    </>
                  )}
                  {mode === 'reset' && (
                    <>
                      <Zap className="w-5 h-5" />
                      {t('auth.sendEmailButton')}
                    </>
                  )}
                </>
              )}
            </button>
          </form>

          {/* Social Auth (not for reset) */}
          {mode !== 'reset' && (
            <>
              <div className="my-6 flex items-center">
                <div className={`flex-1 h-px ${darkMode ? 'bg-gray-700' : 'bg-gray-300'}`} />
                <span className={`px-4 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {t('auth.orContinueWith')}
                </span>
                <div className={`flex-1 h-px ${darkMode ? 'bg-gray-700' : 'bg-gray-300'}`} />
              </div>

              <button
                type="button"
                onClick={handleGoogleAuth}
                disabled={loading}
                className={`w-full flex items-center justify-center gap-3 py-3 rounded-lg border-2 transition-all hover:scale-105 ${
                  darkMode
                    ? 'border-gray-700 hover:bg-gray-800 hover:border-gray-600'
                    : 'border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                } disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-sm hover:shadow-md`}
              >
                <Chrome className="w-5 h-5 text-blue-500" />
                <span className="font-medium">{t('auth.continueWithGoogle')}</span>
              </button>
            </>
          )}

          {/* Mode Switch */}
          <div className="mt-6 text-center space-y-3">
            {mode === 'signin' && (
              <>
                <button
                  type="button"
                  onClick={() => switchMode('reset')}
                  className={`block w-full text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500'} transition-colors hover:underline`}
                >
                  {t('auth.forgotPasswordQuestion')}
                </button>
                <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                  <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {t('auth.newToNoteFlow')}{' '}
                  </span>
                  <button
                    type="button"
                    onClick={() => switchMode('signup')}
                    className={`text-sm font-medium ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500'} transition-colors hover:underline`}
                  >
                    {t('auth.createFreeAccountLink')}
                  </button>
                </div>
              </>
            )}

            {mode === 'signup' && (
              <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {t('auth.alreadyHaveAccountQuestion')}{' '}
                </span>
                <button
                  type="button"
                  onClick={() => switchMode('signin')}
                  className={`text-sm font-medium ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500'} transition-colors hover:underline`}
                >
                  {t('auth.loginLink')}
                </button>
              </div>
            )}

            {mode === 'reset' && (
              <button
                type="button"
                onClick={() => switchMode('signin')}
                className={`text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500'} transition-colors hover:underline flex items-center justify-center gap-2`}
              >
                <ArrowRight className="w-4 h-4 rotate-180" />
                {t('auth.backToLogin')}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
