import React, { useState } from 'react';
import {
  Search,
  Plus,
  Menu,
  <PERSON>,
  Moon,
  Grid3X3,
  List,
  Command,

  Focus,
  BarChart3,
  Share2,
  Zap,
  Bell,
  LogOut,
  User,
  Settings,
  ChevronDown,
  Play,
  Pause,
  RotateCcw,
  X
} from 'lucide-react';
import { HeaderProps } from '../types';
import { useAuth } from '../hooks/useAuth';
import { useLanguage } from '../hooks/useLanguage';
import NotificationsPanel from './NotificationsPanel';

import ThemeToggle from './ThemeToggle';

const Header: React.FC<HeaderProps & {
  onMarkNotificationAsRead?: (id: string) => void;
  onMarkAllNotificationsAsRead?: () => void;
  onDeleteNotification?: (id: string) => void;
  onDeleteAllNotifications?: () => void;
  onShowSettings?: () => void;
}> = ({
  darkMode,
  setDarkMode,
  searchTerm,
  setSearchTerm,
  viewMode,
  setViewMode,
  showSidebar,
  setShowSidebar,
  setShowCommandPalette,
  handleNewNote,
  focusMode,
  setFocusMode,
  setShowAnalytics,
  setShowQuickCapture,
  notifications,
  setShowNotifications,
  showNotifications,
  onLogout,
  onMarkNotificationAsRead = () => {},
  onMarkAllNotificationsAsRead = () => {},
  onDeleteNotification = () => {},
  onDeleteAllNotifications = () => {},
  onShowSettings = () => {}
}) => {
  const { userProfile } = useAuth();
  const { t } = useLanguage();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  const unreadNotifications = notifications.filter(n => !n.read).length;



  // Close menus when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserMenu || showMobileMenu) {
        const target = event.target as Element;
        if (!target.closest('[data-menu]')) {
          setShowUserMenu(false);
          setShowMobileMenu(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showUserMenu, showMobileMenu]);

  return (
    <header className={`sticky top-0 z-[100] border-b transition-all shadow-sm ${
      focusMode
        ? darkMode
          ? 'bg-purple-900/95 border-purple-800 backdrop-blur-md'
          : 'bg-purple-50/95 border-purple-200 backdrop-blur-md'
        : darkMode
          ? 'bg-gray-900/95 border-gray-800 backdrop-blur-md'
          : 'bg-white/95 border-gray-200 backdrop-blur-md'
    }`}>
      <div className="flex items-center justify-between px-4 lg:px-6 py-3">
        {/* Left Section */}
        <div className="flex items-center gap-3 lg:gap-4">
          {/* Sidebar Toggle - Hidden in focus mode */}
          {!focusMode && (
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              className={`p-2 rounded-lg transition-all hover:scale-105 ${
                showSidebar
                  ? 'bg-blue-500 text-white shadow-md'
                  : darkMode
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={showSidebar ? t('sidebar.hide') : t('sidebar.show')}
            >
              <Menu className="w-5 h-5" />
            </button>
          )}

          {/* Logo */}
          <div className="flex items-center gap-3">
            <div className={`w-9 h-9 rounded-xl flex items-center justify-center shadow-lg transition-all ${
              focusMode
                ? 'bg-gradient-to-r from-purple-500 to-purple-600'
                : 'bg-gradient-to-r from-blue-500 to-purple-600'
            }`}>
              <span className="text-white font-bold text-lg">N</span>
            </div>
            <div className="hidden sm:block">
              <h1 className={`text-xl font-bold bg-gradient-to-r bg-clip-text text-transparent ${
                focusMode
                  ? 'from-purple-600 to-purple-600'
                  : 'from-blue-600 to-purple-600'
              }`}>
                NoteFlow
              </h1>
              {focusMode ? (
                <div className="flex items-center gap-1">
                  <Focus className="w-3 h-3 text-purple-500" />
                  <span className="text-xs text-purple-500 font-medium">Modo Foco</span>
                </div>
              ) : (
                <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {t('header.subtitle')}
                </p>
              )}
            </div>
          </div>


        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-lg mx-4 lg:mx-8">
          <div className="relative group">
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 transition-colors ${
              searchTerm ? 'text-blue-500' : 'text-gray-400'
            }`} />
            <input
              type="text"
              placeholder="Buscar notas... (Ctrl+K para comando)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-12 py-2.5 rounded-xl border-2 transition-all duration-200 ${
                darkMode
                  ? 'bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500 focus:bg-gray-750'
                  : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:bg-white'
              } focus:outline-none focus:ring-4 focus:ring-blue-500/10 group-hover:border-gray-400`}
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full transition-colors ${
                  darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-500'
                }`}
              >
                <X className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-1 lg:gap-2 flex-shrink-0">

          {/* Quick Actions - Hidden on mobile */}
          <div className="hidden md:flex items-center gap-1">
            {/* Focus Mode - Always visible */}
            <button
              onClick={() => setFocusMode(!focusMode)}
              className={`p-2 rounded-lg transition-all hover:scale-105 ${
                focusMode
                  ? 'bg-purple-500 text-white shadow-md'
                  : darkMode
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={focusMode ? 'Sair do modo foco (Ctrl+Shift+F)' : 'Entrar no modo foco (Ctrl+Shift+F)'}
            >
              <Focus className="w-5 h-5" />
            </button>

            {/* Other actions - Hidden in focus mode */}
            {!focusMode && (
              <>
                <button
                  onClick={() => setShowQuickCapture(true)}
                  className={`p-2 rounded-lg transition-all hover:scale-105 ${
                    darkMode
                      ? 'hover:bg-gray-800 text-gray-300'
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}
                  title="Captura Rápida (Ctrl+Shift+L)"
                >
                  <Zap className="w-5 h-5" />
                </button>

                <button
                  onClick={() => setShowCommandPalette(true)}
                  className={`p-2 rounded-lg transition-all hover:scale-105 ${
                    darkMode
                      ? 'hover:bg-gray-800 text-gray-300'
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}
                  title="Paleta de Comandos (Ctrl+K)"
                >
                  <Command className="w-5 h-5" />
                </button>

                {/* View Mode Toggle */}
                <button
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className={`p-2 rounded-lg transition-all hover:scale-105 ${
                    darkMode
                      ? 'hover:bg-gray-800 text-gray-300'
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}
                  title={`Mudar para ${viewMode === 'grid' ? 'lista' : 'grade'}`}
                >
                  {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid3X3 className="w-5 h-5" />}
                </button>

                <button
                  onClick={() => setShowAnalytics(true)}
                  className={`p-2 rounded-lg transition-all hover:scale-105 ${
                    darkMode
                      ? 'hover:bg-gray-800 text-gray-300'
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}
                  title="Analytics"
                >
                  <BarChart3 className="w-5 h-5" />
                </button>
              </>
            )}
          </div>

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className={`p-2 rounded-lg transition-all hover:scale-105 relative ${
                showNotifications
                  ? 'bg-blue-500 text-white shadow-md'
                  : darkMode
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="Notificações"
            >
              <Bell className="w-5 h-5" />
              {unreadNotifications > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                  {unreadNotifications > 9 ? '9+' : unreadNotifications}
                </span>
              )}
            </button>
          </div>

          {/* Theme Toggle */}
          <ThemeToggle variant="dropdown" />

          {/* User Menu */}
          <div className="relative" data-menu>
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className={`flex items-center gap-2 p-2 rounded-lg transition-all hover:scale-105 ${
                showUserMenu
                  ? 'bg-blue-500 text-white shadow-md'
                  : darkMode
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="Menu do usuário"
            >
              {userProfile?.photoURL ? (
                <img
                  src={userProfile.photoURL}
                  alt="Avatar"
                  className="w-6 h-6 rounded-full"
                />
              ) : (
                <User className="w-5 h-5" />
              )}
              <ChevronDown className={`w-4 h-4 transition-transform ${showUserMenu ? 'rotate-180' : ''}`} />
            </button>

            {/* User Dropdown */}
            {showUserMenu && (
              <div className={`absolute right-0 top-full mt-2 w-64 rounded-xl shadow-lg border z-50 ${
                darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
              }`}>
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-3">
                    {userProfile?.photoURL ? (
                      <img
                        src={userProfile.photoURL}
                        alt="Avatar"
                        className="w-10 h-10 rounded-full"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-white" />
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{userProfile?.displayName || 'Usuário'}</p>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {userProfile?.email}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-2">
                  <button
                    onClick={() => {
                      setShowUserMenu(false);
                      onShowSettings();
                    }}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                      darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                    }`}
                  >
                    <Settings className="w-4 h-4" />
                    <span>Configurações</span>
                  </button>

                  {onLogout && (
                    <button
                      onClick={() => {
                        setShowUserMenu(false);
                        onLogout();
                      }}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors text-red-500 ${
                        darkMode ? 'hover:bg-red-900/20' : 'hover:bg-red-50'
                      }`}
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Sair</span>
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            className={`md:hidden p-2 rounded-lg transition-all hover:scale-105 ${
              showMobileMenu
                ? 'bg-blue-500 text-white shadow-md'
                : darkMode
                  ? 'hover:bg-gray-800 text-gray-300'
                  : 'hover:bg-gray-100 text-gray-600'
            }`}
            title="Menu mobile"
            data-menu
          >
            <Menu className="w-5 h-5" />
          </button>

          {/* New Note Button */}
          <button
            onClick={handleNewNote}
            className="ml-2 px-3 lg:px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:shadow-lg transition-all hover:scale-105 flex items-center gap-2 shadow-md"
            title="Nova Nota (Ctrl+N)"
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline font-medium">Nova Nota</span>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {showMobileMenu && (
        <div className={`md:hidden border-t ${darkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-white'}`} data-menu>
          <div className="p-4 space-y-3">
            {/* Mobile Quick Actions */}
            <div className="grid grid-cols-3 gap-3">
              <button
                onClick={() => {
                  setShowQuickCapture(true);
                  setShowMobileMenu(false);
                }}
                className={`flex flex-col items-center gap-2 p-3 rounded-lg transition-colors ${
                  darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                }`}
              >
                <Zap className="w-5 h-5" />
                <span className="text-xs">Captura</span>
              </button>



              <button
                onClick={() => {
                  setShowAnalytics(true);
                  setShowMobileMenu(false);
                }}
                className={`flex flex-col items-center gap-2 p-3 rounded-lg transition-colors ${
                  darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                }`}
              >
                <BarChart3 className="w-5 h-5" />
                <span className="text-xs">Analytics</span>
              </button>

              <button
                onClick={() => setFocusMode(!focusMode)}
                className={`flex flex-col items-center gap-2 p-3 rounded-lg transition-colors ${
                  focusMode
                    ? 'bg-purple-500 text-white'
                    : darkMode
                      ? 'hover:bg-gray-800'
                      : 'hover:bg-gray-100'
                }`}
              >
                <Focus className="w-5 h-5" />
                <span className="text-xs">Foco</span>
              </button>
            </div>


          </div>
        </div>
      )}

      {/* Notifications Panel */}
      <NotificationsPanel
        show={showNotifications}
        darkMode={darkMode}
        notifications={notifications}
        onClose={() => setShowNotifications(false)}
        onMarkAsRead={onMarkNotificationAsRead}
        onMarkAllAsRead={onMarkAllNotificationsAsRead}
        onDeleteNotification={onDeleteNotification}
        onDeleteAll={onDeleteAllNotifications}
      />
    </header>
  );
};

export default Header;
