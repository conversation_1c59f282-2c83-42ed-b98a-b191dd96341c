import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  Set<PERSON><PERSON>,
  FileText,
  Share2,
  Users,
  Clock,
  Shield,
  Award,
  Database,
  AlertCircle
} from 'lucide-react';
import { Notification } from '../types';
import { notificationsService } from '../services/notificationsService';
import { toast } from 'react-hot-toast';

interface NotificationsPanelProps {
  show: boolean;
  darkMode: boolean;
  notifications: Notification[];
  onClose: () => void;
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteNotification: (id: string) => void;
  onDeleteAll: () => void;
}

const NotificationsPanel: React.FC<NotificationsPanelProps> = ({
  show,
  darkMode,
  notifications,
  onClose,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onDeleteAll
}) => {
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  if (!show) return null;

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'note_created':
      case 'note_updated':
      case 'note_deleted':
        return FileText;
      case 'note_shared':
        return Share2;
      case 'collaboration':
        return Users;
      case 'reminder':
        return Clock;
      case 'system':
        return Shield;
      case 'achievement':
        return Award;
      case 'backup':
        return Database;
      default:
        return Bell;
    }
  };

  // Get notification color based on type
  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'note_created':
        return 'bg-green-500/20 text-green-400';
      case 'note_updated':
        return 'bg-blue-500/20 text-blue-400';
      case 'note_deleted':
        return 'bg-red-500/20 text-red-400';
      case 'note_shared':
        return 'bg-purple-500/20 text-purple-400';
      case 'collaboration':
        return 'bg-orange-500/20 text-orange-400';
      case 'reminder':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'achievement':
        return 'bg-pink-500/20 text-pink-400';
      case 'backup':
        return 'bg-cyan-500/20 text-cyan-400';
      case 'system':
        return 'bg-gray-500/20 text-gray-400';
      default:
        return 'bg-blue-500/20 text-blue-400';
    }
  };

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread' && notification.read) return false;
    if (filter === 'read' && !notification.read) return false;
    if (selectedType !== 'all' && notification.type !== selectedType) return false;
    return true;
  });

  // Get unique notification types
  const notificationTypes = Array.from(new Set(notifications.map(n => n.type)));

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-start justify-end p-4">
      <div className={`w-full max-w-md h-[calc(100vh-2rem)] ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-l-2xl shadow-2xl overflow-hidden flex flex-col animate-fade-in`}>
        {/* Header */}
        <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex-shrink-0`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Bell className="w-6 h-6" />
              <h2 className="text-xl font-bold">Notificações</h2>
              {unreadCount > 0 && (
                <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">
                  {unreadCount}
                </span>
              )}
            </div>
            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-colors ${
                darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Filters */}
          <div className="flex gap-2 mb-4">
            {['all', 'unread', 'read'].map((filterType) => (
              <button
                key={filterType}
                onClick={() => setFilter(filterType as any)}
                className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                  filter === filterType
                    ? 'bg-blue-500 text-white'
                    : darkMode
                      ? 'bg-gray-800 hover:bg-gray-700'
                      : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                {filterType === 'all' ? 'Todas' : filterType === 'unread' ? 'Não lidas' : 'Lidas'}
              </button>
            ))}
          </div>

          {/* Type Filter */}
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className={`w-full p-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-800 border-gray-700'
                : 'bg-white border-gray-300'
            }`}
          >
            <option value="all">Todos os tipos</option>
            {notificationTypes.map(type => (
              <option key={type} value={type}>
                {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </option>
            ))}
          </select>

          {/* Actions */}
          <div className="flex gap-2 mt-3">
            <button
              onClick={onMarkAllAsRead}
              disabled={unreadCount === 0}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                unreadCount === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : darkMode
                    ? 'bg-gray-800 hover:bg-gray-700'
                    : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              <CheckCheck className="w-3 h-3" />
              Marcar todas
            </button>
            <button
              onClick={onDeleteAll}
              disabled={notifications.length === 0}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors text-red-500 ${
                notifications.length === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : darkMode
                    ? 'bg-red-900/20 hover:bg-red-900/30'
                    : 'bg-red-100 hover:bg-red-200'
              }`}
            >
              <Trash2 className="w-3 h-3" />
              Limpar todas
            </button>
          </div>
        </div>

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto min-h-0">
          {filteredNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500 p-8">
              <Bell className="w-12 h-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">Nenhuma notificação</p>
              <p className="text-sm text-center">
                {filter === 'unread'
                  ? 'Você não tem notificações não lidas'
                  : filter === 'read'
                    ? 'Você não tem notificações lidas'
                    : 'Você não tem notificações'
                }
              </p>
            </div>
          ) : (
            <div className="p-3 space-y-2">
              {filteredNotifications.map((notification) => {
                const Icon = getNotificationIcon(notification.type);
                const colorClass = getNotificationColor(notification.type);

                return (
                  <div
                    key={notification.id}
                    className={`p-3 rounded-lg border transition-all cursor-pointer ${
                      !notification.read
                        ? darkMode
                          ? 'bg-blue-500/10 border-blue-500/20'
                          : 'bg-blue-50 border-blue-200'
                        : darkMode
                          ? 'bg-gray-800/50 border-gray-700'
                          : 'bg-gray-50 border-gray-200'
                    } hover:scale-[1.01]`}
                    onClick={() => !notification.read && onMarkAsRead(notification.id)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-1.5 rounded-lg ${colorClass} flex-shrink-0`}>
                        <Icon className="w-3 h-3" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            {notification.title && (
                              <h4 className="font-medium text-sm mb-1 truncate">
                                {notification.title}
                              </h4>
                            )}
                            <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                              <span className="font-medium">{notification.user}</span>{' '}
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {notification.time}
                            </p>
                          </div>
                          <div className="flex items-center gap-1 flex-shrink-0">
                            {!notification.read && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onMarkAsRead(notification.id);
                                }}
                                className="p-1 rounded hover:bg-green-500/20 text-green-500"
                                title="Marcar como lida"
                              >
                                <Check className="w-3 h-3" />
                              </button>
                            )}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onDeleteNotification(notification.id);
                              }}
                              className="p-1 rounded hover:bg-red-500/20 text-red-500"
                              title="Excluir notificação"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationsPanel;
