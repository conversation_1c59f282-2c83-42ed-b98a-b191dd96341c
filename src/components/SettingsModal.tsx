import React, { useState, useEffect } from 'react';
import {
  X,
  Settings,
  User,
  Palette,
  Bell,
  Shield,
  Download,
  Upload,
  Trash2,
  Save,
  Globe,
  Moon,
  Sun,
  Monitor,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Key,
  Database,
  FileText,
  Zap,
  Clock,
  Languages,
  Smartphone
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';
import { toast } from 'react-hot-toast';

interface SettingsModalProps {
  show: boolean;
  darkMode: boolean;
  onClose: () => void;
}

type SettingsTab = 'account' | 'appearance' | 'notifications' | 'privacy' | 'data' | 'advanced';

export const SettingsModal: React.FC<SettingsModalProps> = ({
  show,
  darkMode,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('account');
  const [isLoading, setIsLoading] = useState(false);
  const { user, userProfile, updatePreferences, signOut } = useAuth();
  const { mode, setThemeMode, getThemeInfo } = useTheme();

  // Local state for settings
  const [settings, setSettings] = useState({
    // Account
    displayName: user?.displayName || '',
    email: user?.email || '',

    // Appearance
    theme: userProfile?.preferences?.theme || 'dark',
    language: userProfile?.preferences?.language || 'pt-BR',
    fontSize: 'medium',
    compactMode: false,

    // Notifications
    notifications: userProfile?.preferences?.notifications ?? true,
    emailNotifications: true,
    pushNotifications: true,
    soundEnabled: true,

    // Privacy
    profileVisibility: 'private',
    dataCollection: true,
    analytics: true,

    // Data
    autoSave: userProfile?.preferences?.autoSave ?? true,
    autoBackup: true,
    backupFrequency: 'daily',

    // Advanced
    betaFeatures: false,
    developerMode: false,
    debugMode: false
  });

  useEffect(() => {
    if (userProfile) {
      setSettings(prev => ({
        ...prev,
        theme: userProfile.preferences?.theme || 'dark',
        language: userProfile.preferences?.language || 'pt-BR',
        notifications: userProfile.preferences?.notifications ?? true,
        autoSave: userProfile.preferences?.autoSave ?? true
      }));
    }
  }, [userProfile]);

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // Update user preferences
      await updatePreferences({
        theme: settings.theme,
        language: settings.language,
        notifications: settings.notifications,
        autoSave: settings.autoSave
      });

      // Update theme
      setThemeMode(settings.theme as any);

      toast.success('Configurações salvas com sucesso!');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Erro ao salvar configurações');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportData = () => {
    toast.success('Exportação iniciada! Você receberá um email quando estiver pronta.');
  };

  const handleImportData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        toast.success('Importação iniciada! Processando arquivo...');
      }
    };
    input.click();
  };

  const handleDeleteAccount = () => {
    if (window.confirm('Tem certeza que deseja excluir sua conta? Esta ação não pode ser desfeita.')) {
      toast.error('Funcionalidade em desenvolvimento');
    }
  };

  if (!show) return null;

  const tabs = [
    { id: 'account', label: 'Conta', icon: User },
    { id: 'appearance', label: 'Aparência', icon: Palette },
    { id: 'notifications', label: 'Notificações', icon: Bell },
    { id: 'privacy', label: 'Privacidade', icon: Shield },
    { id: 'data', label: 'Dados', icon: Database },
    { id: 'advanced', label: 'Avançado', icon: Settings }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[200] flex items-center justify-center p-4">
      <div className={`w-full max-w-4xl h-[80vh] rounded-2xl shadow-2xl overflow-hidden ${
        darkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'
      }`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${
          darkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${
              darkMode ? 'bg-blue-500/20' : 'bg-blue-500/10'
            }`}>
              <Settings className="w-5 h-5 text-blue-500" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Configurações</h2>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Personalize sua experiência no NoteFlow
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-colors ${
              darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
            }`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex h-full">
          {/* Sidebar */}
          <div className={`w-64 border-r ${
            darkMode ? 'border-gray-700 bg-gray-800/50' : 'border-gray-200 bg-gray-50'
          } p-4`}>
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as SettingsTab)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? (darkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-500/10 text-blue-600')
                        : (darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              {activeTab === 'account' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Informações da Conta</h3>

                    <div className="space-y-4">
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Nome de Exibição
                        </label>
                        <input
                          type="text"
                          value={settings.displayName}
                          onChange={(e) => setSettings(prev => ({ ...prev, displayName: e.target.value }))}
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-800 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                        />
                      </div>

                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Email
                        </label>
                        <input
                          type="email"
                          value={settings.email}
                          disabled
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-700 border-gray-600 text-gray-400'
                              : 'bg-gray-100 border-gray-300 text-gray-500'
                          } cursor-not-allowed`}
                        />
                        <p className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          O email não pode ser alterado
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className={`pt-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                    <h4 className="text-md font-medium mb-3">Ações da Conta</h4>
                    <div className="space-y-3">
                      <button
                        onClick={() => signOut()}
                        className="flex items-center gap-2 px-4 py-2 text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20 rounded-lg transition-colors"
                      >
                        <Key className="w-4 h-4" />
                        Sair da Conta
                      </button>

                      <button
                        onClick={handleDeleteAccount}
                        className="flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                        Excluir Conta
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'appearance' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Aparência</h3>

                    <div className="space-y-6">
                      {/* Theme Selection */}
                      <div>
                        <label className={`block text-sm font-medium mb-3 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Tema
                        </label>
                        <div className="grid grid-cols-3 gap-3">
                          {[
                            { id: 'light', label: 'Claro', icon: Sun },
                            { id: 'dark', label: 'Escuro', icon: Moon },
                            { id: 'system', label: 'Sistema', icon: Monitor }
                          ].map((theme) => {
                            const Icon = theme.icon;
                            return (
                              <button
                                key={theme.id}
                                onClick={() => setSettings(prev => ({ ...prev, theme: theme.id }))}
                                className={`p-4 rounded-lg border-2 transition-all ${
                                  settings.theme === theme.id
                                    ? 'border-blue-500 bg-blue-500/10'
                                    : (darkMode ? 'border-gray-600 hover:border-gray-500' : 'border-gray-300 hover:border-gray-400')
                                }`}
                              >
                                <Icon className={`w-6 h-6 mx-auto mb-2 ${
                                  settings.theme === theme.id ? 'text-blue-500' : (darkMode ? 'text-gray-400' : 'text-gray-600')
                                }`} />
                                <div className={`text-sm font-medium ${
                                  settings.theme === theme.id ? 'text-blue-500' : (darkMode ? 'text-gray-300' : 'text-gray-700')
                                }`}>
                                  {theme.label}
                                </div>
                              </button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Language Selection */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Idioma
                        </label>
                        <select
                          value={settings.language}
                          onChange={(e) => setSettings(prev => ({ ...prev, language: e.target.value }))}
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-800 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                        >
                          <option value="pt-BR">Português (Brasil)</option>
                          <option value="en-US">English (US)</option>
                          <option value="es-ES">Español</option>
                          <option value="fr-FR">Français</option>
                        </select>
                      </div>

                      {/* Font Size */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Tamanho da Fonte
                        </label>
                        <select
                          value={settings.fontSize}
                          onChange={(e) => setSettings(prev => ({ ...prev, fontSize: e.target.value }))}
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-800 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                        >
                          <option value="small">Pequeno</option>
                          <option value="medium">Médio</option>
                          <option value="large">Grande</option>
                          <option value="extra-large">Extra Grande</option>
                        </select>
                      </div>

                      {/* Compact Mode */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Modo Compacto
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Reduz o espaçamento da interface
                          </div>
                        </div>
                        <button
                          onClick={() => setSettings(prev => ({ ...prev, compactMode: !prev.compactMode }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.compactMode ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.compactMode ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className={`flex items-center justify-between p-4 border-t ${
          darkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Versão 1.0.0 • NoteFlow
          </div>
          <div className="flex gap-2">
            <button
              onClick={onClose}
              className={`px-4 py-2 rounded-lg transition-colors ${
                darkMode ? 'text-gray-300 hover:bg-gray-800' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Cancelar
            </button>
            <button
              onClick={handleSaveSettings}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              Salvar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
