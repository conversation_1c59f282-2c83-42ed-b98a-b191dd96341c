import React, { useState, useEffect } from 'react';
import {
  X,
  User,
  Palette,
  Bell,
  Shield,
  Trash2,
  Save,
  Moon,
  Sun,
  Monitor,
  Key,
  Settings
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';
import { useAppearanceSettings } from '../hooks/useAppearanceSettings';
import { useLanguage } from '../hooks/useLanguage';
import { toast } from 'react-hot-toast';

interface SettingsModalProps {
  show: boolean;
  darkMode: boolean;
  onClose: () => void;
}

type SettingsTab = 'account' | 'appearance' | 'notifications' | 'privacy';

export const SettingsModal: React.FC<SettingsModalProps> = ({
  show,
  darkMode,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('account');
  const [isLoading, setIsLoading] = useState(false);
  const { user, userProfile, updatePreferences, signOut, updateProfile } = useAuth();
  const { mode, setThemeMode, getThemeInfo } = useTheme();
  const { applyFontSize: globalApplyFontSize, applyCompactMode: globalApplyCompactMode } = useAppearanceSettings();
  const { t, currentLanguage, availableLanguages, changeLanguage, isChangingLanguage } = useLanguage();
  const [isApplyingLanguage, setIsApplyingLanguage] = useState(false);

  // Debug log for useLanguage hook
  console.log('🔧 SettingsModal - useLanguage hook result:', {
    t: typeof t,
    currentLanguage,
    availableLanguages: availableLanguages?.length,
    changeLanguage: typeof changeLanguage,
    isChangingLanguage
  });

  // Local state for settings
  const [settings, setSettings] = useState({
    // Account
    displayName: user?.displayName || '',
    email: user?.email || '',

    // Appearance - usar o tema atual do useTheme
    theme: mode,
    language: currentLanguage,
    fontSize: 'medium',
    compactMode: false,

    // Notifications
    notifications: userProfile?.preferences?.notifications ?? true,
    emailNotifications: true,
    pushNotifications: true,
    soundEnabled: true,

    // Privacy
    profileVisibility: 'private',
    dataCollection: true,
    analytics: true
  });

  // Sincronizar com o tema atual do useTheme e idioma
  useEffect(() => {
    console.log('Current theme mode from useTheme:', mode);
    console.log('Current language from useLanguage:', currentLanguage);
    setSettings(prev => ({
      ...prev,
      theme: mode,
      language: currentLanguage
    }));
  }, [mode, currentLanguage]);

  useEffect(() => {
    console.log('User profile updated:', userProfile);
    console.log('User data:', user);

    if (user) {
      setSettings(prev => ({
        ...prev,
        displayName: user.displayName || '',
        email: user.email || ''
      }));
    }

    if (userProfile) {
      // Carregar fontSize do localStorage se disponível, senão usar Firebase
      let currentFontSize = 'medium';
      try {
        const fontSizeFromStorage = localStorage.getItem('noteflow-fontSize');
        if (fontSizeFromStorage) {
          currentFontSize = fontSizeFromStorage;
        } else if (userProfile.preferences?.fontSize) {
          currentFontSize = userProfile.preferences.fontSize;
        }
      } catch (error) {
        console.error('Error loading fontSize from localStorage:', error);
        currentFontSize = userProfile.preferences?.fontSize || 'medium';
      }

      // Carregar compactMode do localStorage se disponível, senão usar Firebase
      let currentCompactMode = false;
      try {
        const compactModeFromStorage = localStorage.getItem('noteflow-compactMode');
        if (compactModeFromStorage) {
          currentCompactMode = JSON.parse(compactModeFromStorage);
        } else if (userProfile.preferences?.compactMode !== undefined) {
          currentCompactMode = userProfile.preferences.compactMode;
        }
      } catch (error) {
        console.error('Error loading compactMode from localStorage:', error);
        currentCompactMode = userProfile.preferences?.compactMode ?? false;
      }

      console.log('📦 Configurações carregadas para o modal:', {
        fontSize: currentFontSize,
        compactMode: currentCompactMode,
        fromLocalStorage: true
      });

      setSettings(prev => ({
        ...prev,
        // Não sobrescrever o tema se já foi sincronizado com useTheme
        theme: prev.theme || userProfile.preferences?.theme || mode,
        language: currentLanguage,
        notifications: userProfile.preferences?.notifications ?? true,
        fontSize: currentFontSize,
        compactMode: currentCompactMode,
        emailNotifications: userProfile.preferences?.emailNotifications ?? true,
        pushNotifications: userProfile.preferences?.pushNotifications ?? true,
        soundEnabled: userProfile.preferences?.soundEnabled ?? true,
        profileVisibility: userProfile.preferences?.profileVisibility || 'private',
        dataCollection: userProfile.preferences?.dataCollection ?? true,
        analytics: userProfile.preferences?.analytics ?? true
      }));
    }
  }, [user, userProfile, mode]);

  // Sincronizar com localStorage quando o modal é aberto
  useEffect(() => {
    if (show) {
      console.log('🔄 Modal aberto, sincronizando com localStorage...');

      // Carregar configurações atuais do localStorage
      try {
        const fontSizeFromStorage = localStorage.getItem('noteflow-fontSize');
        const compactModeFromStorage = localStorage.getItem('noteflow-compactMode');

        if (fontSizeFromStorage || compactModeFromStorage) {
          setSettings(prev => ({
            ...prev,
            fontSize: fontSizeFromStorage || prev.fontSize,
            compactMode: compactModeFromStorage ? JSON.parse(compactModeFromStorage) : prev.compactMode
          }));

          console.log('✅ Modal sincronizado com localStorage:', {
            fontSize: fontSizeFromStorage,
            compactMode: compactModeFromStorage
          });
        }
      } catch (error) {
        console.error('Error syncing modal with localStorage:', error);
      }
    }
  }, [show]);

  // As configurações de aparência agora são aplicadas globalmente pelo useAppearanceSettings

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      console.log('Saving settings:', settings);

      // Update user preferences
      await updatePreferences({
        theme: settings.theme,
        language: settings.language,
        notifications: settings.notifications,
        fontSize: settings.fontSize,
        compactMode: settings.compactMode,
        emailNotifications: settings.emailNotifications,
        pushNotifications: settings.pushNotifications,
        soundEnabled: settings.soundEnabled,
        profileVisibility: settings.profileVisibility,
        dataCollection: settings.dataCollection,
        analytics: settings.analytics
      });

      // Apply appearance settings (tema já foi aplicado no clique)
      // As funções globais já salvam no localStorage automaticamente
      globalApplyFontSize(settings.fontSize);
      globalApplyCompactMode(settings.compactMode);

      // Apply language change if different
      if (settings.language !== currentLanguage) {
        console.log('Applying language change:', settings.language);
        await changeLanguage(settings.language);
      }

      // Update display name if changed
      if (settings.displayName !== user?.displayName && settings.displayName.trim()) {
        try {
          await updateProfile({
            displayName: settings.displayName
          });
          console.log('Display name updated successfully');
        } catch (error) {
          console.error('Error updating display name:', error);
          toast.error('Erro ao atualizar nome de exibição');
        }
      }

      toast.success('Configurações salvas com sucesso!', {
        icon: '⚙️',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Erro ao salvar configurações');
    } finally {
      setIsLoading(false);
    }
  };



  const handleLogout = async () => {
    try {
      console.log('Attempting logout...');
      setIsLoading(true);

      await signOut();

      console.log('Logout successful, closing modal...');
      onClose();

      toast.success('Logout realizado com sucesso!', {
        icon: '👋',
        duration: 2000,
      });
    } catch (error) {
      console.error('Error during logout:', error);
      toast.error('Erro ao fazer logout');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = () => {
    if (window.confirm('Tem certeza que deseja excluir sua conta? Esta ação não pode ser desfeita.')) {
      toast.error('Funcionalidade em desenvolvimento');
    }
  };

  if (!show) return null;

  const tabs = [
    { id: 'account', label: 'Conta', icon: User },
    { id: 'appearance', label: 'Aparência', icon: Palette },
    { id: 'notifications', label: 'Notificações', icon: Bell },
    { id: 'privacy', label: 'Privacidade', icon: Shield }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[200] flex items-center justify-center p-4">
      <div className={`w-full max-w-4xl h-[80vh] rounded-2xl shadow-2xl overflow-hidden ${
        darkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'
      }`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${
          darkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${
              darkMode ? 'bg-blue-500/20' : 'bg-blue-500/10'
            }`}>
              <Settings className="w-5 h-5 text-blue-500" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">{t('settings.settings')}</h2>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('settings.personalizeExperience')}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-colors ${
              darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
            }`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex h-full">
          {/* Sidebar */}
          <div className={`w-64 border-r ${
            darkMode ? 'border-gray-700 bg-gray-800/50' : 'border-gray-200 bg-gray-50'
          } p-4`}>
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as SettingsTab)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? (darkMode ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-500/10 text-blue-600')
                        : (darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              {activeTab === 'account' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Informações da Conta</h3>

                    <div className="space-y-4">
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Nome de Exibição
                        </label>
                        <input
                          type="text"
                          value={settings.displayName}
                          onChange={(e) => setSettings(prev => ({ ...prev, displayName: e.target.value }))}
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-800 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                        />
                      </div>

                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Email
                        </label>
                        <input
                          type="email"
                          value={settings.email}
                          disabled
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-700 border-gray-600 text-gray-400'
                              : 'bg-gray-100 border-gray-300 text-gray-500'
                          } cursor-not-allowed`}
                        />
                        <p className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          O email não pode ser alterado
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className={`pt-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                    <h4 className="text-md font-medium mb-3">Estatísticas da Conta</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                        <div className="text-2xl font-bold text-blue-500">{userProfile?.stats?.totalNotes || 0}</div>
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Notas Criadas</div>
                      </div>
                      <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                        <div className="text-2xl font-bold text-green-500">{userProfile?.stats?.totalWords || 0}</div>
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Palavras Escritas</div>
                      </div>
                    </div>
                  </div>

                  <div className={`pt-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                    <h4 className="text-md font-medium mb-3">Ações da Conta</h4>
                    <div className="space-y-3">
                      <button
                        onClick={() => {
                          console.log('Logout button clicked');
                          handleLogout();
                        }}
                        disabled={isLoading}
                        className="flex items-center gap-2 px-4 py-2 text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20 rounded-lg transition-colors disabled:opacity-50"
                      >
                        <Key className="w-4 h-4" />
                        {isLoading ? 'Saindo...' : 'Sair da Conta'}
                      </button>

                      <button
                        onClick={() => {
                          console.log('Delete account button clicked');
                          handleDeleteAccount();
                        }}
                        disabled={isLoading}
                        className="flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors disabled:opacity-50"
                      >
                        <Trash2 className="w-4 h-4" />
                        Excluir Conta
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'appearance' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Aparência</h3>

                    <div className="space-y-6">
                      {/* Theme Selection */}
                      <div>
                        <label className={`block text-sm font-medium mb-3 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Tema
                        </label>
                        <div className="grid grid-cols-3 gap-3">
                          {[
                            { id: 'light', label: 'Claro', icon: Sun },
                            { id: 'dark', label: 'Escuro', icon: Moon },
                            { id: 'system', label: 'Sistema', icon: Monitor }
                          ].map((theme) => {
                            const Icon = theme.icon;
                            return (
                              <button
                                key={theme.id}
                                onClick={() => {
                                  console.log('Theme button clicked:', theme.id);
                                  console.log('Current settings.theme:', settings.theme);
                                  console.log('Current mode from useTheme:', mode);

                                  // Aplicar tema imediatamente
                                  setThemeMode(theme.id as any);

                                  // Atualizar estado local
                                  setSettings(prev => ({ ...prev, theme: theme.id }));

                                  console.log('Theme applied and state updated');
                                }}
                                className={`p-4 rounded-lg border-2 transition-all ${
                                  settings.theme === theme.id
                                    ? 'border-blue-500 bg-blue-500/10'
                                    : (darkMode ? 'border-gray-600 hover:border-gray-500' : 'border-gray-300 hover:border-gray-400')
                                }`}
                              >
                                <Icon className={`w-6 h-6 mx-auto mb-2 ${
                                  settings.theme === theme.id ? 'text-blue-500' : (darkMode ? 'text-gray-400' : 'text-gray-600')
                                }`} />
                                <div className={`text-sm font-medium ${
                                  settings.theme === theme.id ? 'text-blue-500' : (darkMode ? 'text-gray-300' : 'text-gray-700')
                                }`}>
                                  {theme.label}
                                </div>
                              </button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Language Selection */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          {t('settings.language')}
                        </label>
                        <select
                          value={settings.language}
                          onChange={async (e) => {
                            const newLanguage = e.target.value;
                            console.log('🌐 Language select changed:', newLanguage);

                            // Prevenir múltiplas mudanças simultâneas
                            if (isApplyingLanguage || isChangingLanguage) {
                              console.log('🌐 Language change already in progress, ignoring...');
                              return;
                            }

                            setIsApplyingLanguage(true);

                            // Atualizar estado local
                            setSettings(prev => ({ ...prev, language: newLanguage }));

                            // Aplicar mudança de idioma imediatamente
                            if (changeLanguage) {
                              console.log('🌐 Calling changeLanguage function...');
                              await changeLanguage(newLanguage);
                              console.log('🌐 Language change completed');

                              // Mostrar feedback visual
                              const notification = document.createElement('div');
                              notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse';
                              notification.textContent = '🌐 Aplicando idioma...';
                              document.body.appendChild(notification);

                              // Refresh da página para garantir que todos os componentes sejam atualizados
                              console.log('🔄 Refreshing page to apply language changes...');
                              setTimeout(() => {
                                window.location.reload();
                              }, 800); // Delay para mostrar a notificação
                            } else {
                              console.error('❌ changeLanguage function not available');
                              setIsApplyingLanguage(false);
                            }
                          }}
                          disabled={isChangingLanguage || isApplyingLanguage}
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-800 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          } focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50`}
                        >
                          {availableLanguages.map((lang) => (
                            <option key={lang.code} value={lang.code}>
                              {lang.flag} {lang.name}
                            </option>
                          ))}
                        </select>

                        {/* Debug info - temporary */}
                        <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs">
                          <div>Current Language: {currentLanguage || 'undefined'}</div>
                          <div>Settings Language: {settings.language || 'undefined'}</div>
                          <div>Translation Test: {t ? t('common.save') : 't function not available'}</div>
                          <div>Available Languages: {availableLanguages ? availableLanguages.length : 'undefined'}</div>
                          <div>Is Changing: {isChangingLanguage ? 'Yes' : 'No'}</div>
                          <div>Is Applying: {isApplyingLanguage ? 'Yes' : 'No'}</div>
                          {(isChangingLanguage || isApplyingLanguage) && (
                            <div className="text-blue-500 font-medium animate-pulse">🌐 Processando mudança de idioma...</div>
                          )}
                          <div className="flex gap-1 mt-1">
                            <button
                              onClick={() => {
                                console.log('🔧 Debug: Testing language change directly');
                                console.log('🔧 Current settings.language:', settings.language);
                                console.log('🔧 Current i18n language:', currentLanguage);
                                console.log('🔧 t function:', t);
                                console.log('🔧 changeLanguage function:', changeLanguage);
                                if (changeLanguage) {
                                  changeLanguage(settings.language);
                                } else {
                                  console.error('❌ changeLanguage function not available');
                                }
                              }}
                              className="px-2 py-1 bg-red-500 text-white rounded text-xs"
                            >
                              🔧 Test Hook
                            </button>
                            <button
                              onClick={async () => {
                                console.log('🔧 Testing direct i18n change');
                                const { default: i18n } = await import('../i18n');
                                console.log('🔧 Direct i18n object:', i18n);
                                console.log('🔧 Current language:', i18n.language);
                                await i18n.changeLanguage('en');
                                console.log('🔧 After change:', i18n.language);
                                console.log('🔧 Test translation:', i18n.t('common.save'));
                              }}
                              className="px-2 py-1 bg-blue-500 text-white rounded text-xs"
                            >
                              🔧 Test Direct
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Font Size */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Tamanho da Fonte
                        </label>
                        <select
                          value={settings.fontSize}
                          onChange={(e) => {
                            console.log('Font size changed:', e.target.value);
                            setSettings(prev => ({ ...prev, fontSize: e.target.value }));
                            // Aplicar preview imediato
                            globalApplyFontSize(e.target.value);
                          }}
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-800 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                        >
                          <option value="small">Pequeno</option>
                          <option value="medium">Médio</option>
                          <option value="large">Grande</option>
                          <option value="extra-large">Extra Grande</option>
                        </select>
                      </div>

                      {/* Compact Mode */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Modo Compacto
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Reduz o espaçamento da interface
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            const newCompactMode = !settings.compactMode;
                            console.log('Compact mode toggled:', newCompactMode);
                            setSettings(prev => ({ ...prev, compactMode: newCompactMode }));
                            // Aplicar preview imediato
                            globalApplyCompactMode(newCompactMode);
                          }}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.compactMode ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.compactMode ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'notifications' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Notificações</h3>

                    <div className="space-y-6">
                      {/* General Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Notificações Gerais
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Receber notificações sobre atividades
                          </div>
                        </div>
                        <button
                          onClick={() => setSettings(prev => ({ ...prev, notifications: !prev.notifications }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.notifications ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.notifications ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>

                      {/* Email Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Notificações por Email
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Receber resumos e atualizações por email
                          </div>
                        </div>
                        <button
                          onClick={() => setSettings(prev => ({ ...prev, emailNotifications: !prev.emailNotifications }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.emailNotifications ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>

                      {/* Push Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Notificações Push
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Notificações instantâneas no navegador
                          </div>
                        </div>
                        <button
                          onClick={() => setSettings(prev => ({ ...prev, pushNotifications: !prev.pushNotifications }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.pushNotifications ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.pushNotifications ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>

                      {/* Sound Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Sons de Notificação
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Reproduzir sons para notificações
                          </div>
                        </div>
                        <button
                          onClick={() => setSettings(prev => ({ ...prev, soundEnabled: !prev.soundEnabled }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.soundEnabled ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.soundEnabled ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'privacy' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Privacidade e Segurança</h3>

                    <div className="space-y-6">
                      {/* Profile Visibility */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Visibilidade do Perfil
                        </label>
                        <select
                          value={settings.profileVisibility}
                          onChange={(e) => setSettings(prev => ({ ...prev, profileVisibility: e.target.value }))}
                          className={`w-full px-3 py-2 rounded-lg border ${
                            darkMode
                              ? 'bg-gray-800 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                        >
                          <option value="private">Privado</option>
                          <option value="friends">Apenas Amigos</option>
                          <option value="public">Público</option>
                        </select>
                      </div>

                      {/* Data Collection */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Coleta de Dados
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Permitir coleta de dados para melhorar o serviço
                          </div>
                        </div>
                        <button
                          onClick={() => setSettings(prev => ({ ...prev, dataCollection: !prev.dataCollection }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.dataCollection ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.dataCollection ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>

                      {/* Analytics */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Analytics
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Compartilhar dados de uso anônimos
                          </div>
                        </div>
                        <button
                          onClick={() => setSettings(prev => ({ ...prev, analytics: !prev.analytics }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.analytics ? 'bg-blue-500' : (darkMode ? 'bg-gray-600' : 'bg-gray-300')
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.analytics ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}


            </div>
          </div>
        </div>

        {/* Footer */}
        <div className={`flex items-center justify-between p-4 border-t ${
          darkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Versão 1.0.0 • NoteFlow
          </div>
          <div className="flex gap-2">
            <button
              onClick={onClose}
              className={`px-4 py-2 rounded-lg transition-colors ${
                darkMode ? 'text-gray-300 hover:bg-gray-800' : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              {t('common.cancel')}
            </button>
            <button
              onClick={() => {
                console.log('Save button clicked');
                handleSaveSettings();
              }}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {isLoading ? t('common.saving') : t('common.save')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
