import React, { useState, useEffect } from 'react';
import {
  X, Plus, Edit3, Trash2, Save
} from 'lucide-react';
import { Category } from '../services/categoriesService';

interface CategoryManagerProps {
  show: boolean;
  darkMode: boolean;
  categories: Category[];
  onClose: () => void;
  onSave: (categories: Category[]) => Promise<void>;
}

const AVAILABLE_COLORS = [
  '#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444',
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
];

const AVAILABLE_ICONS = [
  { icon: '👤', name: '<PERSON><PERSON><PERSON>' },
  { icon: '💼', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { icon: '📚', name: 'Est<PERSON><PERSON>' },
  { icon: '🚀', name: '<PERSON>jet<PERSON>' },
  { icon: '💡', name: '<PERSON><PERSON><PERSON>' },
  { icon: '❤️', name: '<PERSON><PERSON>' },
  { icon: '☕', name: 'Café' },
  { icon: '🎵', name: '<PERSON><PERSON><PERSON>' },
  { icon: '📷', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { icon: '💻', name: 'Código' },
  { icon: '🎮', name: 'Jogos' },
  { icon: '📝', name: 'Notas' },
  { icon: '🏠', name: 'Casa' },
  { icon: '🌟', name: 'Estrela' },
  { icon: '🎯', name: 'Meta' },
  { icon: '📊', name: 'Gráfico' }
];

export const CategoryManager: React.FC<CategoryManagerProps> = ({
  show,
  darkMode,
  categories,
  onClose,
  onSave
}) => {
  const [localCategories, setLocalCategories] = useState<Category[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: '',
    color: '#3B82F6',
    icon: '📁'
  });
  const [showColorPicker, setShowColorPicker] = useState<string | null>(null);
  const [showIconPicker, setShowIconPicker] = useState<string | null>(null);

  useEffect(() => {
    if (show) {
      setLocalCategories([...categories]);
    }
  }, [show, categories]);

  const handleAddCategory = () => {
    if (!newCategory.name.trim()) return;

    const category: Category = {
      id: `category-${Date.now()}`,
      name: newCategory.name.trim(),
      color: newCategory.color,
      icon: newCategory.icon,
      userId: '', // Will be set by the parent component
      createdAt: new Date(),
      updatedAt: new Date(),
      description: undefined,
      isDefault: false
    };

    setLocalCategories(prev => [...prev, category]);
    setNewCategory({ name: '', color: '#3B82F6', icon: '📁' });
  };

  const handleEditCategory = (id: string) => {
    setEditingId(id);
  };

  const handleSaveEdit = (id: string, name: string) => {
    if (!name.trim()) return;

    setLocalCategories(prev =>
      prev.map(cat =>
        cat.id === id ? { ...cat, name: name.trim() } : cat
      )
    );
    setEditingId(null);
  };

  const handleDeleteCategory = (id: string) => {
    const category = localCategories.find(cat => cat.id === id);

    // Don't allow deleting default categories
    if (category?.isDefault) {
      alert('Não é possível deletar categorias padrão');
      return;
    }

    setLocalCategories(prev => prev.filter(cat => cat.id !== id));
  };

  const handleColorChange = (id: string, color: string) => {
    setLocalCategories(prev =>
      prev.map(cat =>
        cat.id === id ? { ...cat, color } : cat
      )
    );
    setShowColorPicker(null);
  };

  const handleIconChange = (id: string, icon: string) => {
    setLocalCategories(prev =>
      prev.map(cat =>
        cat.id === id ? { ...cat, icon } : cat
      )
    );
    setShowIconPicker(null);
  };

  const handleSave = async () => {
    await onSave(localCategories);
    onClose();
  };

  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`w-full max-w-2xl max-h-[90vh] overflow-hidden rounded-2xl shadow-2xl ${
        darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold">Gerenciar Categorias</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Add New Category */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">Adicionar Nova Categoria</h3>
            <div className="flex gap-3 items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium mb-2">Nome</label>
                <input
                  type="text"
                  value={newCategory.name}
                  onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Nome da categoria"
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddCategory()}
                />
              </div>

              <div className="relative">
                <label className="block text-sm font-medium mb-2">Cor</label>
                <button
                  onClick={() => setShowColorPicker(showColorPicker === 'new' ? null : 'new')}
                  className="w-10 h-10 rounded-lg border-2 border-gray-300 dark:border-gray-600"
                  style={{ backgroundColor: newCategory.color }}
                />
                {showColorPicker === 'new' && (
                  <div className="absolute top-full mt-2 p-3 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-10">
                    <div className="grid grid-cols-5 gap-2">
                      {AVAILABLE_COLORS.map(color => (
                        <button
                          key={color}
                          onClick={() => {
                            setNewCategory(prev => ({ ...prev, color }));
                            setShowColorPicker(null);
                          }}
                          className="w-8 h-8 rounded-lg border-2 border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="relative">
                <label className="block text-sm font-medium mb-2">Ícone</label>
                <button
                  onClick={() => setShowIconPicker(showIconPicker === 'new' ? null : 'new')}
                  className="w-10 h-10 rounded-lg border-2 border-gray-300 dark:border-gray-600 flex items-center justify-center text-lg hover:bg-gray-100 dark:hover:bg-gray-600"
                >
                  {newCategory.icon}
                </button>
                {showIconPicker === 'new' && (
                  <div className="absolute top-full mt-2 p-3 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-10 w-64">
                    <div className="grid grid-cols-8 gap-2 max-h-40 overflow-y-auto">
                      {AVAILABLE_ICONS.map(({ icon, name }) => (
                        <button
                          key={icon}
                          onClick={() => {
                            setNewCategory(prev => ({ ...prev, icon }));
                            setShowIconPicker(null);
                          }}
                          className="w-8 h-8 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center text-lg transition-colors"
                          title={name}
                        >
                          {icon}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <button
                onClick={handleAddCategory}
                disabled={!newCategory.name.trim()}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
              >
                <Plus className="w-4 h-4" />
                Adicionar
              </button>
            </div>
          </div>

          {/* Categories List */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Categorias Existentes</h3>
            <div className="space-y-3">
              {localCategories.map(category => (
                <CategoryItem
                  key={category.id}
                  category={category}
                  darkMode={darkMode}
                  isEditing={editingId === category.id}
                  showColorPicker={showColorPicker === category.id}
                  showIconPicker={showIconPicker === category.id}
                  onEdit={() => handleEditCategory(category.id)}
                  onSaveEdit={(name) => handleSaveEdit(category.id, name)}
                  onDelete={() => handleDeleteCategory(category.id)}
                  onColorChange={(color) => handleColorChange(category.id, color)}
                  onIconChange={(icon) => handleIconChange(category.id, icon)}
                  onToggleColorPicker={() => setShowColorPicker(showColorPicker === category.id ? null : category.id)}
                  onToggleIconPicker={() => setShowIconPicker(showIconPicker === category.id ? null : category.id)}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2 transition-colors"
          >
            <Save className="w-4 h-4" />
            Salvar Alterações
          </button>
        </div>
      </div>
    </div>
  );
};

interface CategoryItemProps {
  category: Category;
  darkMode: boolean;
  isEditing: boolean;
  showColorPicker: boolean;
  showIconPicker: boolean;
  onEdit: () => void;
  onSaveEdit: (name: string) => void;
  onDelete: () => void;
  onColorChange: (color: string) => void;
  onIconChange: (icon: string) => void;
  onToggleColorPicker: () => void;
  onToggleIconPicker: () => void;
}

const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  darkMode,
  isEditing,
  showColorPicker,
  showIconPicker,
  onEdit,
  onSaveEdit,
  onDelete,
  onColorChange,
  onIconChange,
  onToggleColorPicker,
  onToggleIconPicker
}) => {
  const [editName, setEditName] = useState(category.name);

  const handleSave = () => {
    onSaveEdit(editName);
  };

  return (
    <div className={`p-4 rounded-lg border ${
      darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
    } relative`}>
      <div className="flex items-center gap-3">
        {/* Icon */}
        <div className="relative">
          <button
            onClick={onToggleIconPicker}
            className="w-10 h-10 rounded-lg flex items-center justify-center text-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            style={{ backgroundColor: category.color + '20' }}
          >
            {category.icon}
          </button>
          {showIconPicker && (
            <div className="absolute top-full mt-2 p-3 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-10 w-64">
              <div className="grid grid-cols-8 gap-2 max-h-40 overflow-y-auto">
                {AVAILABLE_ICONS.map(({ icon, name }) => (
                  <button
                    key={icon}
                    onClick={() => onIconChange(icon)}
                    className="w-8 h-8 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center text-lg transition-colors"
                    title={name}
                  >
                    {icon}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Name */}
        <div className="flex-1">
          {isEditing ? (
            <input
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSave()}
              onBlur={handleSave}
              className={`w-full px-2 py-1 rounded border ${
                darkMode
                  ? 'bg-gray-600 border-gray-500 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              autoFocus
            />
          ) : (
            <div className="flex items-center gap-2">
              <span className="font-medium">{category.name}</span>
              {category.isDefault && (
                <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                  Padrão
                </span>
              )}
            </div>
          )}
        </div>

        {/* Color */}
        <div className="relative">
          <button
            onClick={onToggleColorPicker}
            className="w-8 h-8 rounded-lg border-2 border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
            style={{ backgroundColor: category.color }}
          />
          {showColorPicker && (
            <div className="absolute top-full mt-2 right-0 p-3 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-10">
              <div className="grid grid-cols-5 gap-2">
                {AVAILABLE_COLORS.map(color => (
                  <button
                    key={color}
                    onClick={() => onColorChange(color)}
                    className="w-8 h-8 rounded-lg border-2 border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <button
            onClick={onEdit}
            className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
          >
            <Edit3 className="w-4 h-4" />
          </button>
          <button
            onClick={onDelete}
            disabled={category.isDefault}
            className={`p-2 rounded-lg transition-colors ${
              category.isDefault
                ? 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                : 'text-gray-500 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20'
            }`}
            title={category.isDefault ? 'Categorias padrão não podem ser deletadas' : 'Deletar categoria'}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};
