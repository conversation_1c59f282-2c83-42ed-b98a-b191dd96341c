import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit2, 
  Trash2, 
  Save, 
  X, 
  Folder, 
  FolderPlus, 
  <PERSON><PERSON>les, 
  <PERSON>
} from 'lucide-react';

export interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  description?: string;
}

interface CategoryManagerProps {
  show: boolean;
  darkMode: boolean;
  categories: Category[];
  onClose: () => void;
  onSave: (categories: Category[]) => void;
}

const AVAILABLE_ICONS = ['📁', '💼', '🏠', '💡', '📚', '🎯', '⭐', '❤️', '🚀', '🎨', '🔥', '💎', '🌟', '📋', '🎪'];

const AVAILABLE_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1',
  '#14B8A6', '#F43F5E', '#8B5A2B', '#6B7280', '#1F2937'
];

const CategoryManager: React.FC<CategoryManagerProps> = ({ 
  show, 
  darkMode, 
  categories, 
  onClose, 
  onSave 
}) => {
  const [localCategories, setLocalCategories] = useState<Category[]>([]);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [newCategory, setNewCategory] = useState<Partial<Category>>({
    name: '',
    color: '#3B82F6',
    icon: '📁',
    description: ''
  });
  const [showNewForm, setShowNewForm] = useState(false);

  useEffect(() => {
    setLocalCategories([...categories]);
  }, [categories]);

  if (!show) return null;

  const handleAddCategory = () => {
    if (!newCategory.name?.trim()) return;
    
    const category: Category = {
      id: Date.now().toString(),
      name: newCategory.name.trim(),
      color: newCategory.color || '#3B82F6',
      icon: newCategory.icon || '📁',
      description: newCategory.description || ''
    };
    
    setLocalCategories(prev => [...prev, category]);
    setNewCategory({ name: '', color: '#3B82F6', icon: '📁', description: '' });
    setShowNewForm(false);
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
  };

  const handleUpdateCategory = (updates: Partial<Category>) => {
    if (!editingCategory) return;
    
    setLocalCategories(prev => 
      prev.map(cat => 
        cat.id === editingCategory.id 
          ? { ...cat, ...updates }
          : cat
      )
    );
    setEditingCategory(null);
  };

  const handleDeleteCategory = (id: string) => {
    if (localCategories.length <= 1) {
      alert('Deve existir pelo menos uma categoria');
      return;
    }
    setLocalCategories(prev => prev.filter(cat => cat.id !== id));
  };

  const handleSave = () => {
    onSave(localCategories);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className={`w-full max-w-3xl max-h-[85vh] ${darkMode ? 'bg-gray-900 border border-gray-700' : 'bg-white border border-gray-200'} rounded-3xl shadow-2xl overflow-hidden flex flex-col`}>
        {/* Header */}
        <div className={`relative p-6 border-b ${darkMode ? 'border-gray-700 bg-gradient-to-r from-gray-900 to-gray-800' : 'border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50'}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl text-white shadow-lg">
                <Folder className="w-6 h-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Gerenciar Categorias
                </h2>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Organize suas notas com categorias personalizadas
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className={`p-2 rounded-xl transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Add New Category Form */}
          {showNewForm ? (
            <div className="space-y-4 p-6 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl border-2 border-blue-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Nova Categoria</h3>
                <button
                  onClick={() => setShowNewForm(false)}
                  className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-200'}`}
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Icon Selector */}
                <div className="space-y-2">
                  <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Ícone
                  </label>
                  <select
                    value={newCategory.icon}
                    onChange={(e) => setNewCategory(prev => ({ ...prev, icon: e.target.value }))}
                    className={`w-full h-14 text-center text-2xl rounded-xl border-2 transition-all focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    {AVAILABLE_ICONS.map(icon => (
                      <option key={icon} value={icon}>{icon}</option>
                    ))}
                  </select>
                </div>

                {/* Name Input */}
                <div className="md:col-span-2 space-y-2">
                  <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Nome da Categoria
                  </label>
                  <input
                    type="text"
                    placeholder="Ex: Trabalho, Pessoal, Ideias..."
                    value={newCategory.name}
                    onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                    className={`w-full px-4 py-3 rounded-xl border-2 transition-all focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-300 hover:border-gray-400'
                    }`}
                  />
                </div>
              </div>

              {/* Color Palette */}
              <div className="space-y-3">
                <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Cor da Categoria
                </label>
                <div className="grid grid-cols-5 md:grid-cols-10 gap-3">
                  {AVAILABLE_COLORS.map(color => (
                    <button
                      key={color}
                      onClick={() => setNewCategory(prev => ({ ...prev, color }))}
                      className={`relative w-12 h-12 rounded-2xl border-3 transition-all duration-200 hover:scale-110 ${
                        newCategory.color === color
                          ? 'border-white shadow-xl scale-110 ring-2 ring-blue-500'
                          : 'border-gray-300 hover:border-gray-400 shadow-md'
                      }`}
                      style={{ backgroundColor: color }}
                    >
                      {newCategory.color === color && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Check className="w-6 h-6 text-white drop-shadow-lg" />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Descrição (opcional)
                </label>
                <input
                  type="text"
                  placeholder="Adicione uma descrição para esta categoria..."
                  value={newCategory.description}
                  onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                  className={`w-full px-4 py-3 rounded-xl border-2 transition-all focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-300 hover:border-gray-400'
                  }`}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                <button
                  onClick={handleAddCategory}
                  disabled={!newCategory.name?.trim()}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-medium transition-all duration-200 hover:shadow-lg hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center justify-center gap-2"
                >
                  <Plus className="w-5 h-5" />
                  Adicionar Categoria
                </button>
                <button
                  onClick={() => setShowNewForm(false)}
                  className={`px-6 py-3 rounded-xl border-2 font-medium transition-all duration-200 hover:scale-105 ${
                    darkMode
                      ? 'border-gray-600 hover:bg-gray-700 text-gray-300 hover:text-white'
                      : 'border-gray-300 hover:bg-gray-100 text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Cancelar
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setShowNewForm(true)}
              className="w-full p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-2xl text-center hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 group"
            >
              <div className="flex flex-col items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl text-white group-hover:scale-110 transition-transform duration-200">
                  <FolderPlus className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Adicionar Nova Categoria</h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Crie uma nova categoria para organizar suas notas
                  </p>
                </div>
              </div>
            </button>
          )}

          {/* Existing Categories */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-500" />
              Categorias Existentes ({localCategories.length})
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {localCategories.map((category) => (
                <div key={category.id}>
                  {editingCategory?.id === category.id ? (
                    <EditCategoryForm
                      category={category}
                      darkMode={darkMode}
                      onSave={handleUpdateCategory}
                      onCancel={() => setEditingCategory(null)}
                    />
                  ) : (
                    <div className={`p-4 rounded-2xl border-2 transition-all duration-200 hover:scale-105 hover:shadow-lg ${
                      darkMode 
                        ? 'bg-gray-800 border-gray-700 hover:border-gray-600' 
                        : 'bg-white border-gray-200 hover:border-gray-300'
                    }`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div 
                            className="w-12 h-12 rounded-xl flex items-center justify-center text-white shadow-lg text-xl"
                            style={{ backgroundColor: category.color }}
                          >
                            {category.icon}
                          </div>
                          <div>
                            <h4 className="font-semibold text-lg">{category.name}</h4>
                            {category.description && (
                              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                {category.description}
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEditCategory(category)}
                            className={`p-2 rounded-lg transition-all hover:scale-110 ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                            title="Editar categoria"
                          >
                            <Edit2 className="w-4 h-4" />
                          </button>
                          
                          {localCategories.length > 1 && (
                            <button
                              onClick={() => handleDeleteCategory(category.id)}
                              className={`p-2 rounded-lg transition-all hover:scale-110 text-red-500 ${darkMode ? 'hover:bg-red-900/20' : 'hover:bg-red-100'}`}
                              title="Deletar categoria"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className={`p-6 border-t ${darkMode ? 'border-gray-700 bg-gray-900/50' : 'border-gray-200 bg-gray-50'}`}>
          <div className="flex items-center justify-between">
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {localCategories.length} categoria{localCategories.length !== 1 ? 's' : ''} configurada{localCategories.length !== 1 ? 's' : ''}
            </p>
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className={`px-6 py-3 rounded-xl border-2 font-medium transition-all duration-200 hover:scale-105 ${
                  darkMode
                    ? 'border-gray-600 hover:bg-gray-700 text-gray-300 hover:text-white'
                    : 'border-gray-300 hover:bg-gray-100 text-gray-600 hover:text-gray-800'
                }`}
              >
                Cancelar
              </button>
              <button
                onClick={handleSave}
                className="px-8 py-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white rounded-xl font-medium hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2"
              >
                <Save className="w-5 h-5" />
                Salvar Alterações
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Component for editing category
const EditCategoryForm: React.FC<{
  category: Category;
  darkMode: boolean;
  onSave: (updates: Partial<Category>) => void;
  onCancel: () => void;
}> = ({ category, darkMode, onSave, onCancel }) => {
  const [name, setName] = useState(category.name);
  const [color, setColor] = useState(category.color);
  const [icon, setIcon] = useState(category.icon);
  const [description, setDescription] = useState(category.description || '');

  const handleSave = () => {
    if (!name.trim()) return;
    onSave({ name: name.trim(), color, icon, description });
  };

  return (
    <div className="space-y-6 p-6 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl border-2 border-blue-200 dark:border-gray-600">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Icon Selector */}
        <div className="space-y-2">
          <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Ícone
          </label>
          <select
            value={icon}
            onChange={(e) => setIcon(e.target.value)}
            className={`w-full h-14 text-center text-2xl rounded-xl border-2 transition-all focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-300 hover:border-gray-400'
            }`}
          >
            {AVAILABLE_ICONS.map(iconOption => (
              <option key={iconOption} value={iconOption}>{iconOption}</option>
            ))}
          </select>
        </div>

        {/* Name Input */}
        <div className="md:col-span-2 space-y-2">
          <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Nome da Categoria
          </label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className={`w-full px-4 py-3 rounded-xl border-2 transition-all focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-300 hover:border-gray-400'
            }`}
          />
        </div>
      </div>

      {/* Color Palette */}
      <div className="space-y-3">
        <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          Cor da Categoria
        </label>
        <div className="grid grid-cols-5 md:grid-cols-10 gap-3">
          {AVAILABLE_COLORS.map(colorOption => (
            <button
              key={colorOption}
              onClick={() => setColor(colorOption)}
              className={`relative w-12 h-12 rounded-2xl border-3 transition-all duration-200 hover:scale-110 ${
                color === colorOption
                  ? 'border-white shadow-xl scale-110 ring-2 ring-blue-500'
                  : 'border-gray-300 hover:border-gray-400 shadow-md'
              }`}
              style={{ backgroundColor: colorOption }}
            >
              {color === colorOption && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check className="w-6 h-6 text-white drop-shadow-lg" />
                </div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <label className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          Descrição (opcional)
        </label>
        <input
          type="text"
          placeholder="Adicione uma descrição para esta categoria..."
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className={`w-full px-4 py-3 rounded-xl border-2 transition-all focus:ring-2 focus:ring-blue-500 ${
            darkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-300 hover:border-gray-400'
          }`}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-2">
        <button
          onClick={handleSave}
          disabled={!name.trim()}
          className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-medium transition-all duration-200 hover:shadow-lg hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center justify-center gap-2"
        >
          <Save className="w-5 h-5" />
          Salvar Alterações
        </button>
        <button
          onClick={onCancel}
          className={`px-6 py-3 rounded-xl border-2 font-medium transition-all duration-200 hover:scale-105 ${
            darkMode
              ? 'border-gray-600 hover:bg-gray-700 text-gray-300 hover:text-white'
              : 'border-gray-300 hover:bg-gray-100 text-gray-600 hover:text-gray-800'
          }`}
        >
          Cancelar
        </button>
      </div>
    </div>
  );
};

export default CategoryManager;
