import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Pin,
  Lock,
  MoreVertical,
  Edit,
  Trash2,
  Share2,
  Copy,
  Calendar,
  Shield,
  AlertCircle
} from 'lucide-react';
import { Note } from '../types';
import { MOODS, CATEGORIES } from '../constants';
import { useLanguage } from '../hooks/useLanguage';



interface NoteCardProps {
  note: Note;
  darkMode: boolean;
  viewMode: 'grid' | 'list';
  copiedId: string | null;
  categories?: Array<{ id: string; name: string; icon: string; color: string; }>;
  onEdit: (note: Note) => void;
  onDelete: (noteId: string) => void;
  onToggleStar: (noteId: string) => void;
  onTogglePin: (noteId: string) => void;
  onToggleLock: (noteId: string) => void;
  onTogglePublic: (noteId: string) => void;
  onCopy: (note: Note) => void;
  onExport: (note: Note) => void;
  onShare: (note: Note) => void;
}

interface ConfirmDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  noteTitle: string;
  darkMode?: boolean;
}

const ConfirmDeleteModal: React.FC<ConfirmDeleteModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  noteTitle,
  darkMode = false
}) => {
  const { t } = useLanguage();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div
        className={`
          relative max-w-md w-full rounded-xl shadow-xl border transition-all duration-200
          ${darkMode
            ? 'bg-gray-800 border-gray-700 text-white'
            : 'bg-white border-gray-200 text-gray-900'
          }
        `}
        style={{
          animation: isOpen ? 'modalEnter 200ms ease-out' : undefined
        }}
      >
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">{t('notes.confirmDelete')}</h3>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {t('notes.actionCannotBeUndone')}
              </p>
            </div>
          </div>

          <p className={`mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            {t('notes.confirmDeleteMessage', { title: noteTitle })}
          </p>

          <div className="flex gap-3 justify-end">
            <button
              onClick={onClose}
              disabled={isDeleting}
              className={`
                px-4 py-2 rounded-lg border transition-all duration-150 font-medium
                ${darkMode
                  ? 'border-gray-600 text-gray-300 hover:bg-gray-700 focus:ring-gray-500'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-200'
                }
                focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed
              `}
            >
              {t('common.cancel')}
            </button>
            <button
              onClick={handleConfirm}
              disabled={isDeleting}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-150 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  {t('notes.deleting')}
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4" />
                  {t('common.delete')}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const NoteCard: React.FC<NoteCardProps> = ({
  note,
  darkMode,
  viewMode,
  copiedId,
  categories,
  onEdit,
  onDelete,
  onToggleStar,
  onTogglePin,
  onToggleLock,
  onTogglePublic,
  onCopy,
  onExport,
  onShare
}) => {
  const { t } = useLanguage();
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const isGridView = viewMode === 'grid';

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowActions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleAction = async (action: string, callback: () => void | Promise<void>) => {
    setActionLoading(action);
    try {
      await callback();
    } finally {
      setActionLoading(null);
      setShowActions(false);
    }
  };

  const handleEdit = () => {
    onEdit(note);
  };

  const handleDelete = async () => {
    await handleAction('delete', () => onDelete(note.id));
    setShowDeleteModal(false);
  };

  const getMoodConfig = () => {
    if (!note.mood) return null;
    return MOODS.find(mood => mood.id === note.mood);
  };

  const getCategoryInfo = () => {
    if (!note.category) return null;

    // Primeiro tenta buscar nas categorias do Firebase
    if (categories && categories.length > 0) {
      const category = categories.find(cat => cat.id === note.category);
      if (category) return {
        name: `${category.icon} ${category.name}`,
        color: category.color,
        icon: category.icon
      };
    }

    // Fallback para categorias das constantes
    const constantCategory = CATEGORIES.find(cat => cat.id === note.category);
    if (constantCategory) return {
      name: `${constantCategory.icon} ${constantCategory.name}`,
      color: constantCategory.color,
      icon: constantCategory.icon
    };

    // Se não encontrar, retorna valores padrão
    return {
      name: note.category,
      color: '#3B82F6',
      icon: '📝'
    };
  };

  const getModernCardStyle = () => {
    // Usar a cor da nota salva no BD, com fallback para cor da categoria
    const noteColor = note.color;
    const categoryInfo = getCategoryInfo();
    const cardColor = noteColor || categoryInfo?.color || '#3B82F6';

    // Converter hex para RGB para criar gradientes
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 59, g: 130, b: 246 }; // fallback blue
    };

    const rgb = hexToRgb(cardColor);

    if (darkMode) {
      return {
        background: `linear-gradient(135deg,
          rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1) 0%,
          rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.05) 100%)`,
        borderColor: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.3)`,
        boxShadow: `0 4px 20px rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`,
      };
    } else {
      return {
        background: `linear-gradient(135deg,
          rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.08) 0%,
          rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.03) 100%)`,
        borderColor: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.2)`,
        boxShadow: `0 4px 20px rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.08)`,
      };
    }
  };

  const getNoteAccentStyle = () => {
    // Usar a cor da nota salva no BD, com fallback para cor da categoria
    const noteColor = note.color;
    const categoryInfo = getCategoryInfo();
    const cardColor = noteColor || categoryInfo?.color || '#3B82F6';

    return {
      backgroundColor: cardColor,
      boxShadow: `0 0 20px ${cardColor}40`,
    };
  };

  const moodConfig = getMoodConfig();
  const categoryInfo = getCategoryInfo();
  const categoryDisplay = categoryInfo?.name;
  const modernCardStyle = getModernCardStyle();
  const noteAccentStyle = getNoteAccentStyle();
  const isLocked = note.locked && note.lockCode;
  const previewText = note.content?.slice(0, 120) + (note.content?.length > 120 ? '...' : '') || '';

  // Status indicator based on note properties
  const getStatusIndicator = () => {
    if (isLocked) return { color: 'bg-amber-400', label: t('notes.protected') };
    if (note.starred) return { color: 'bg-yellow-400', label: t('notes.favorite') };
    if (note.pinned) return { color: 'bg-blue-400', label: t('notes.pinned') };
    return null;
  };

  const statusIndicator = getStatusIndicator();

  return (
    <>
      <div
        className={`
          group relative rounded-xl border transition-all duration-300 cursor-pointer overflow-hidden
          ${isPressed ? 'scale-[0.98]' : 'scale-100'}
          ${isGridView ? 'aspect-[4/3]' : 'min-h-[120px]'}
          hover:scale-[1.02] hover:-translate-y-1
        `}
        style={{
          ...modernCardStyle,
          transform: `scale(${isPressed ? 0.98 : isHovered ? 1.02 : 1}) translateY(${isHovered ? '-4px' : '0px'})`,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          border: `1px solid ${modernCardStyle.borderColor || (darkMode ? '#374151' : '#E5E7EB')}`
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        onClick={handleEdit}
      >
        {/* Note color accent bar */}
        <div
          className="absolute top-0 left-0 w-full h-1 z-10"
          style={noteAccentStyle}
        />

        {/* Status indicator */}
        {statusIndicator && (
          <div className="absolute top-3 left-3 z-10">
            <div
              className={`w-2 h-2 rounded-full ${statusIndicator.color}`}
              title={statusIndicator.label}
            />
          </div>
        )}

        {/* Action buttons overlay */}
        <div className={`
          absolute top-3 right-3 z-30 flex gap-1 transition-all duration-200
          ${isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-1'}
        `}>
          {/* Star button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleAction('star', () => onToggleStar(note.id));
            }}
            disabled={actionLoading === 'star'}
            className={`
              p-1.5 rounded-lg transition-all duration-150
              ${note.starred
                ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400'
                : (darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-white/80 text-gray-600 hover:bg-white')
              }
              hover:scale-105 backdrop-blur-sm shadow-sm
            `}
          >
            {actionLoading === 'star' ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              <Star className={`w-4 h-4 ${note.starred ? 'fill-current' : ''}`} />
            )}
          </button>

          {/* Pin button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleAction('pin', () => onTogglePin(note.id));
            }}
            disabled={actionLoading === 'pin'}
            className={`
              p-1.5 rounded-lg transition-all duration-150
              ${note.pinned
                ? 'bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400'
                : (darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-white/80 text-gray-600 hover:bg-white')
              }
              hover:scale-105 backdrop-blur-sm shadow-sm
            `}
          >
            {actionLoading === 'pin' ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              <Pin className={`w-4 h-4 ${note.pinned ? 'fill-current' : ''}`} />
            )}
          </button>

          {/* More actions button */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowActions(!showActions);
              }}
              className={`
                p-1.5 rounded-lg transition-all duration-150
                ${darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-white/80 text-gray-600 hover:bg-white'}
                hover:scale-105 backdrop-blur-sm shadow-sm
              `}
            >
              <MoreVertical className="w-4 h-4" />
            </button>

            {/* Actions dropdown */}
            {showActions && (
              <div className={`
                absolute top-full right-0 mt-1 w-48 rounded-lg shadow-lg border z-40
                ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
                py-1 backdrop-blur-sm
              `}>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction('edit', () => onEdit(note));
                  }}
                  disabled={actionLoading === 'edit'}
                  className={`
                    w-full text-left px-3 py-2 text-sm transition-colors duration-150 flex items-center gap-2
                    ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-50'}
                  `}
                >
                  <Edit className="w-4 h-4" />
                  {t('common.edit')}
                </button>

                {onShare && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAction('share', () => onShare(note));
                    }}
                    disabled={actionLoading === 'share'}
                    className={`
                      w-full text-left px-3 py-2 text-sm transition-colors duration-150 flex items-center gap-2
                      ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-50'}
                    `}
                  >
                    <Share2 className="w-4 h-4" />
                    {t('notes.share')}
                  </button>
                )}

                {onCopy && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAction('copy', () => onCopy(note));
                    }}
                    disabled={actionLoading === 'copy'}
                    className={`
                      w-full text-left px-3 py-2 text-sm transition-colors duration-150 flex items-center gap-2
                      ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-50'}
                    `}
                  >
                    <Copy className="w-4 h-4" />
                    {t('notes.copy')}
                  </button>
                )}



                <div className={`my-1 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`} />

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDeleteModal(true);
                    setShowActions(false);
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-150 flex items-center gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  {t('common.delete')}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Protected note overlay with blur */}
        {isLocked && (
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-md flex items-center justify-center z-20 rounded-xl"
            style={{
              clipPath: 'polygon(0% 0%, 85% 0%, 85% 60px, 100% 60px, 100% 100%, 0% 100%)'
            }}
          >
            <div className={`text-center p-4 rounded-lg ${
              darkMode ? 'bg-gray-800/90 text-white' : 'bg-white/90 text-gray-900'
            } backdrop-blur-sm shadow-lg border ${
              darkMode ? 'border-gray-600' : 'border-gray-300'
            }`}>
              <Shield className="w-8 h-8 mx-auto mb-2 text-orange-500" />
              <p className="text-sm font-medium">{t('notes.protectedNote')}</p>
              <p className="text-xs opacity-70 mt-1">{t('notes.clickToEnterCode')}</p>
            </div>
          </div>
        )}

        {/* Card content */}
        <div className={`p-4 h-full flex flex-col ${isLocked ? 'filter blur-sm' : ''}`}>
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className={`
                font-semibold text-lg leading-tight mb-1 truncate
                ${darkMode ? 'text-white' : 'text-gray-900'}
              `}>
                {note.title || t('notes.untitledNote')}
              </h3>

              {/* Metadata */}
              <div className="flex items-center gap-3 text-xs">
                <div className={`flex items-center gap-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  <Calendar className="w-3 h-3" />
                  <span>{new Date(note.updatedAt).toLocaleDateString('pt-BR')}</span>
                </div>

                {/* Category display - show category or "Sem categoria" */}
                {categoryDisplay && categoryInfo ? (
                  <div className="flex items-center gap-1">
                    <div
                      className="w-3 h-3 rounded-full flex items-center justify-center text-[8px]"
                      style={{
                        backgroundColor: `${note.color || categoryInfo.color}20`,
                        color: note.color || categoryInfo.color,
                        border: `1px solid ${note.color || categoryInfo.color}40`
                      }}
                    >
                      {categoryInfo.icon}
                    </div>
                    <span
                      className="truncate max-w-20 text-xs font-medium"
                      style={{ color: note.color || categoryInfo.color }}
                    >
                      {categoryInfo.name.replace(categoryInfo.icon, '').trim()}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-1">
                    <div
                      className={`w-3 h-3 rounded-full flex items-center justify-center text-[8px] ${
                        darkMode ? 'bg-gray-600 text-gray-400 border-gray-500' : 'bg-gray-200 text-gray-500 border-gray-300'
                      } border`}
                    >
                      📝
                    </div>
                    <span className={`truncate max-w-20 text-xs font-medium ${
                      darkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {t('categories.noCategorySelected')}
                    </span>
                  </div>
                )}

                {moodConfig && (
                  <div className="flex items-center gap-1">
                    <span className="text-sm">{moodConfig.emoji}</span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {moodConfig.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content preview */}
          {!isLocked && previewText && (
            <div className="flex-1 mb-3">
              <p className={`
                text-sm leading-relaxed line-clamp-3
                ${darkMode ? 'text-gray-300' : 'text-gray-600'}
              `}>
                {previewText}
              </p>
            </div>
          )}

          {/* Tags */}
          {!isLocked && note.tags && note.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {note.tags.slice(0, isGridView ? 2 : 4).map((tag, index) => (
                <span
                  key={index}
                  className={`
                    px-2 py-1 rounded-md text-xs font-medium
                    ${darkMode
                      ? 'bg-gray-700 text-gray-300'
                      : 'bg-gray-100 text-gray-700'
                    }
                  `}
                >
                  {tag}
                </span>
              ))}
              {note.tags.length > (isGridView ? 2 : 4) && (
                <span className={`
                  px-2 py-1 rounded-md text-xs
                  ${darkMode ? 'text-gray-400' : 'text-gray-500'}
                `}>
                  +{note.tags.length - (isGridView ? 2 : 4)}
                </span>
              )}
            </div>
          )}

          {/* Footer indicators */}
          <div className="flex items-center justify-between mt-auto pt-2">
            <div className="flex items-center gap-2">
              {note.wordCount && (
                <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {note.wordCount} {t('notes.words')}
                </span>
              )}
            </div>

            <div className="flex items-center gap-1">
              {note.starred && (
                <Star className="w-3 h-3 text-yellow-500 fill-current" />
              )}
              {note.pinned && (
                <Pin className="w-3 h-3 text-blue-500 fill-current" />
              )}
              {isLocked && (
                <Lock className="w-3 h-3 text-amber-500" />
              )}
            </div>
          </div>
        </div>


      </div>

      {/* Delete confirmation modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        noteTitle={note.title || t('notes.untitledNote')}
        darkMode={darkMode}
      />
    </>
  );
};

export default NoteCard;