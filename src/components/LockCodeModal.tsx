import React, { useState, useEffect } from 'react';
import { X, Lock, Unlock, Eye, EyeOff, Shield, Key, Trash2 } from 'lucide-react';

interface LockCodeModalProps {
  show: boolean;
  darkMode: boolean;
  onClose: () => void;
  onConfirm: (code: string) => Promise<boolean> | boolean;
  mode: 'set' | 'verify' | 'delete'; // 'set' para definir código, 'verify' para verificar, 'delete' para deletar
  noteTitle?: string;
}

export const LockCodeModal: React.FC<LockCodeModalProps> = ({
  show,
  darkMode,
  onClose,
  onConfirm,
  mode,
  noteTitle
}) => {
  const [code, setCode] = useState('');
  const [confirmCode, setConfirmCode] = useState('');
  const [showCode, setShowCode] = useState(false);
  const [error, setError] = useState('');
  const [attempts, setAttempts] = useState(0);

  useEffect(() => {
    if (show) {
      setCode('');
      setConfirmCode('');
      setError('');
      setAttempts(0);
      setShowCode(false);
    }
  }, [show]);

  if (!show) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (mode === 'set') {
      // Validações para definir código
      if (!code.trim()) {
        setError('Digite um código');
        return;
      }

      if (code.length < 4) {
        setError('O código deve ter pelo menos 4 caracteres');
        return;
      }

      if (code !== confirmCode) {
        setError('Os códigos não coincidem');
        return;
      }

      onConfirm(code);
    } else {
      // Verificação de código
      if (!code.trim()) {
        setError('Digite o código');
        return;
      }

      // Simular tentativas limitadas
      if (attempts >= 3) {
        setError('Muitas tentativas incorretas. Tente novamente mais tarde.');
        return;
      }

      const result = await onConfirm(code);
      if (result === false) {
        // Código incorreto
        setAttempts(prev => prev + 1);
        setError(`Código incorreto. Tentativas restantes: ${3 - attempts - 1}`);
        setCode('');
      }
    }
  };

  const handleCodeChange = (value: string) => {
    setCode(value);
    setError('');
  };

  const handleWrongCode = () => {
    setAttempts(prev => prev + 1);
    setError(`Código incorreto. Tentativas restantes: ${3 - attempts - 1}`);
    setCode('');
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className={`w-full max-w-md ${darkMode ? 'bg-gray-900' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}>
        {/* Header */}
        <div className={`p-6 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${
                mode === 'set' ? 'bg-blue-500/20' :
                mode === 'delete' ? 'bg-red-500/20' : 'bg-orange-500/20'
              }`}>
                {mode === 'set' ? (
                  <Shield className="w-5 h-5 text-blue-500" />
                ) : mode === 'delete' ? (
                  <Trash2 className="w-5 h-5 text-red-500" />
                ) : (
                  <Key className="w-5 h-5 text-orange-500" />
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold">
                  {mode === 'set' ? 'Definir Código de Bloqueio' :
                   mode === 'delete' ? 'Excluir Nota Protegida' : 'Nota Bloqueada'}
                </h3>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {mode === 'set'
                    ? 'Proteja sua nota com um código'
                    : mode === 'delete'
                    ? `Digite o código para excluir "${noteTitle}"`
                    : `Digite o código para acessar "${noteTitle}"`
                  }
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-all hover:scale-110 ${
                darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
              }`}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Código */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {mode === 'set' ? 'Código de Bloqueio' : 'Código'}
            </label>
            <div className="relative">
              <input
                type={showCode ? 'text' : 'password'}
                value={code}
                onChange={(e) => handleCodeChange(e.target.value)}
                placeholder={mode === 'set' ? 'Digite um código seguro' : 'Digite o código'}
                className={`w-full px-4 py-3 pr-12 rounded-lg border transition-all ${
                  darkMode
                    ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                autoFocus
                maxLength={20}
              />
              <button
                type="button"
                onClick={() => setShowCode(!showCode)}
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded transition-colors ${
                  darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {showCode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          {/* Confirmar código (apenas no modo 'set') */}
          {mode === 'set' && (
            <div>
              <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Confirmar Código
              </label>
              <input
                type={showCode ? 'text' : 'password'}
                value={confirmCode}
                onChange={(e) => setConfirmCode(e.target.value)}
                placeholder="Digite o código novamente"
                className={`w-full px-4 py-3 rounded-lg border transition-all ${
                  darkMode
                    ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                maxLength={20}
              />
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20">
              <p className="text-red-500 text-sm">{error}</p>
            </div>
          )}

          {/* Dicas de segurança (apenas no modo 'set') */}
          {mode === 'set' && (
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-500/10' : 'bg-blue-50'} border border-blue-500/20`}>
              <h4 className="text-blue-500 text-sm font-medium mb-1">Dicas de Segurança:</h4>
              <ul className="text-blue-500 text-xs space-y-1">
                <li>• Use pelo menos 4 caracteres</li>
                <li>• Combine letras, números e símbolos</li>
                <li>• Não use informações pessoais óbvias</li>
                <li>• Lembre-se do código - não há recuperação</li>
              </ul>
            </div>
          )}

          {/* Tentativas restantes (apenas no modo 'verify') */}
          {mode === 'verify' && attempts > 0 && (
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-orange-500/10' : 'bg-orange-50'} border border-orange-500/20`}>
              <p className="text-orange-500 text-sm">
                ⚠️ Tentativas restantes: {3 - attempts}
              </p>
            </div>
          )}

          {/* Buttons */}
          <div className="flex gap-3 pt-2">
            <button
              type="button"
              onClick={onClose}
              className={`flex-1 px-4 py-3 rounded-lg border transition-all ${
                darkMode
                  ? 'border-gray-600 text-gray-300 hover:bg-gray-800'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={attempts >= 3}
              className={`flex-1 px-4 py-3 rounded-lg transition-all ${
                attempts >= 3
                  ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
                  : mode === 'set'
                    ? 'bg-blue-500 hover:bg-blue-600 text-white'
                    : mode === 'delete'
                    ? 'bg-red-500 hover:bg-red-600 text-white'
                    : 'bg-orange-500 hover:bg-orange-600 text-white'
              } font-medium`}
            >
              {mode === 'set' ? 'Definir Código' :
               mode === 'delete' ? 'Excluir Nota' : 'Desbloquear'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Hook para gerenciar tentativas de desbloqueio
export const useLockCodeAttempts = () => {
  const [attempts, setAttempts] = useState<Record<string, number>>({});

  const addAttempt = (noteId: string) => {
    setAttempts(prev => ({
      ...prev,
      [noteId]: (prev[noteId] || 0) + 1
    }));
  };

  const resetAttempts = (noteId: string) => {
    setAttempts(prev => {
      const newAttempts = { ...prev };
      delete newAttempts[noteId];
      return newAttempts;
    });
  };

  const getAttempts = (noteId: string) => attempts[noteId] || 0;

  const isBlocked = (noteId: string) => getAttempts(noteId) >= 3;

  return {
    addAttempt,
    resetAttempts,
    getAttempts,
    isBlocked
  };
};
