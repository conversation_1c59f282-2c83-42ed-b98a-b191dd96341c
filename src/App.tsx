import React, { useState } from 'react';
import { Toaster, toast } from 'react-hot-toast';

// Import components
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import NoteCard from './components/NoteCard';

import { QuickCaptureModal, CommandPaletteModal, TemplatesModal } from './components/Modals';
import { AuthModal } from './components/AuthModal';
import { NoteEditor } from './components/NoteEditor';
import { CategoryManager } from './components/CategoryManagerModal';
import { Homepage } from './components/Homepage';
import { AnalyticsModal } from './components/AnalyticsModal';
import { LoginTransition } from './components/LoginTransition';
import { LockCodeModal } from './components/LockCodeModal';


// Import hooks and utilities
import { useAuth } from './hooks/useAuth';
import { useNotesFirebase } from './hooks/useNotesFirebase';
import { useSimpleTheme } from './hooks/useSimpleTheme';
import { copyNoteToClipboard, exportNote } from './utils';
import { Note } from './types';

// Import Firebase config
import './config/firebase';

const NotesApp = () => {
  const { user, loading: authLoading, signOut } = useAuth();
  const { isDark: darkMode } = useSimpleTheme();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showLoginTransition, setShowLoginTransition] = useState(false);
  const [justLoggedIn, setJustLoggedIn] = useState(false);
  // const [focusModeInitialized, setFocusModeInitialized] = useState(false); // Disabled

  // Debug: Log tema atual (removido para evitar re-renders desnecessários)
  // React.useEffect(() => {
  //   console.log('🎨 App.tsx - Tema atual:', { darkMode, themeMode });
  //   console.log('🎨 App.tsx - Classe dark no document:', document.documentElement.classList.contains('dark'));
  // }, [darkMode, themeMode]);

  // Lock code modal states
  const [showLockCodeModal, setShowLockCodeModal] = useState(false);
  const [lockCodeMode, setLockCodeMode] = useState<'set' | 'verify'>('set');
  const [noteToLock, setNoteToLock] = useState<string | null>(null);
  const [unlockedNotes, setUnlockedNotes] = useState<Set<string>>(new Set());

  // Use custom hook for state management
  const {
    // Core states
    showSidebar,
    showQuickCapture,
    showCommandPalette,
    showTemplates,
    showAnalytics,
    showCategoryManager,
    viewMode,
    searchTerm,
    filterTag,
    filterCategory,
    focusMode,
    notifications,
    showNotifications,
    copiedId,
    quickCaptureText,

    // Editor states
    isEditing,
    showNewNote,
    noteTitle,
    noteContent,
    noteCategory,
    noteTags,
    noteColor,
    noteMood,
    noteIsPublic,

    // Data
    notes: sortedNotes,
    allNotes, // Todas as notas sem filtros
    allTags,
    categories,

    // Setters
    setShowSidebar,
    setShowQuickCapture,
    setShowCommandPalette,
    setShowTemplates,
    setShowCategoryManager,
    setViewMode,
    setSearchTerm,
    setFilterTag,
    setFilterCategory,
    setFocusMode,
    setShowAnalytics,
    setShowNotifications,
    setQuickCaptureText,
    setCopiedId,

    // Editor setters
    setNoteTitle,
    setNoteContent,
    setNoteCategory,
    setNoteTags,
    setNoteColor,
    setNoteMood,
    setNoteIsPublic,
    setIsEditing,
    setShowNewNote,

    // Handlers
    handleNewNote,
    handleUseTemplate,
    handleEditNote,
    handleSaveNote,
    handleDeleteNote,
    handleToggleStar,
    handleTogglePin,
    handleToggleLock,
    handleUnlockNote,
    handleTogglePublic,
    handleQuickCapture,
    handleSaveCategories
  } = useNotesFirebase();

  // Don't automatically show auth modal anymore - let homepage handle it
  // React.useEffect(() => {
  //   if (!authLoading && !user) {
  //     setShowAuthModal(true);
  //   }
  // }, [user, authLoading]);

  // Theme is now managed by useTheme hook

  // Detect login and show transition animation
  React.useEffect(() => {
    if (!authLoading && user && justLoggedIn) {
      setShowLoginTransition(true);
      setJustLoggedIn(false);
    }
  }, [user, authLoading, justLoggedIn]);

  // Global keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs or textareas
      const target = e.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return;
      }

      const isCtrlOrCmd = e.ctrlKey || e.metaKey;

      if (isCtrlOrCmd) {
        switch (e.key.toLowerCase()) {
          case 'n':
            e.preventDefault();
            handleNewNote();
            break;
          case 'k':
            e.preventDefault();
            setShowCommandPalette(true);
            break;
          case 'b':
            e.preventDefault();
            setShowSidebar(!showSidebar);
            break;
          case 'f':
            e.preventDefault();
            // Focus search input
            const searchInput = document.querySelector('input[placeholder*="Buscar"]') as HTMLInputElement;
            if (searchInput) {
              searchInput.focus();
            }
            break;
        }

        // Ctrl+Shift combinations
        if (e.shiftKey) {
          switch (e.key.toLowerCase()) {
            case 'l':
              e.preventDefault();
              setShowQuickCapture(true);
              break;
            case 'f':
              e.preventDefault();
              setFocusMode(!focusMode);
              break;
          }
        }
      }

      // Escape key or any key during login transition
      if (e.key === 'Escape') {
        e.preventDefault();
        // Close any open modals
        setShowCommandPalette(false);
        setShowQuickCapture(false);
        setShowAnalytics(false);
        setShowCategoryManager(false);
        setShowTemplates(false);
        setShowAuthModal(false);
        setIsEditing(false);
        setShowNewNote(false);
      }

      // Any key during login transition
      if (showLoginTransition) {
        e.preventDefault();
        setShowLoginTransition(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    handleNewNote,
    showSidebar,
    setShowSidebar,
    setShowCommandPalette,
    setShowQuickCapture,
    setShowAnalytics,
    setShowCategoryManager,
    setShowTemplates,
    setShowAuthModal,
    setIsEditing,
    setShowNewNote,
    showLoginTransition,
    focusMode,
    setFocusMode
  ]);

  // FOCUS MODE TEMPORARILY DISABLED TO FIX REFRESH ISSUE
  // const [sidebarWasOpenBeforeFocus, setSidebarWasOpenBeforeFocus] = React.useState(false);

  // React.useEffect(() => {
  //   if (focusMode) {
  //     // Entering focus mode
  //     if (showSidebar) {
  //       setSidebarWasOpenBeforeFocus(true);
  //       setShowSidebar(false);
  //     }
  //   } else {
  //     // Exiting focus mode
  //     if (sidebarWasOpenBeforeFocus) {
  //       setShowSidebar(true);
  //       setSidebarWasOpenBeforeFocus(false);
  //     }
  //   }
  // }, [focusMode]); // Only depend on focusMode

  // FOCUS MODE NOTIFICATIONS DISABLED TO FIX REFRESH ISSUE
  // React.useEffect(() => {
  //   if (!focusModeInitialized) {
  //     setFocusModeInitialized(true);
  //     return;
  //   }

  //   if (focusMode) {
  //     toast.success('Modo Foco ativado!', {
  //       icon: '🎯',
  //       duration: 2000,
  //     });
  //   } else {
  //     toast.success('Modo Foco desativado!', {
  //       icon: '✨',
  //       duration: 2000,
  //     });
  //   }
  // }, [focusMode, focusModeInitialized]);

  // Show loading screen while checking auth
  if (authLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl font-bold text-white">N</span>
          </div>
          <h1 className="text-2xl font-bold mb-2">NoteFlow</h1>
          <p className="text-gray-500">Carregando...</p>
        </div>
      </div>
    );
  }

  const handleCopyNote = (note: any) => {
    copyNoteToClipboard(note);
    setCopiedId(note.id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  const handleExportNote = (note: any) => {
    exportNote(note);
  };

  const handleShareNote = (note: any) => {
    // Implementar compartilhamento
    console.log('Compartilhar nota:', note);
  };

  const handleLogin = () => {
    setShowAuthModal(true);
  };

  const handleRegister = () => {
    setShowAuthModal(true);
  };

  const handleLogout = () => {
    signOut();
  };

  // Lock code functions
  const handleLockWithCode = (noteId: string) => {
    const note = sortedNotes?.find((n: any) => n.id === noteId);
    if (!note) return;

    if (note.locked) {
      // Se já está bloqueada, verificar código para desbloquear
      setNoteToLock(noteId);
      setLockCodeMode('verify');
      setShowLockCodeModal(true);
    } else {
      // Se não está bloqueada, definir código para bloquear
      setNoteToLock(noteId);
      setLockCodeMode('set');
      setShowLockCodeModal(true);
    }
  };

  const handleLockCodeConfirm = async (code: string): Promise<boolean> => {
    if (!noteToLock) return false;

    if (lockCodeMode === 'set') {
      // Definir código e bloquear nota
      await handleToggleLock(noteToLock, code);

      // Mostrar notificação de sucesso
      toast.success('Nota protegida com código!', {
        icon: '🛡️',
        duration: 3000,
      });

      setShowLockCodeModal(false);
      setNoteToLock(null);
      return true;
    } else {
      // Verificar código para desbloquear
      const isCorrect = await handleUnlockNote(noteToLock, code);
      if (isCorrect) {
        // Adicionar à lista de notas desbloqueadas temporariamente
        setUnlockedNotes(prev => new Set([...prev, noteToLock]));
        setShowLockCodeModal(false);

        // Mostrar notificação de sucesso
        toast.success('Nota desbloqueada com sucesso!', {
          icon: '🔓',
          duration: 2000,
        });

        // Abrir a nota para edição
        const noteToEdit = sortedNotes?.find((n: any) => n.id === noteToLock);
        if (noteToEdit) {
          handleEditNote(noteToEdit);
        }

        setNoteToLock(null);
        return true;
      } else {
        // Código incorreto
        return false;
      }
    }
  };

  const handleEditNoteWithLock = (note: any) => {
    if (note.locked && note.lockCode && !unlockedNotes.has(note.id)) {
      // Nota bloqueada com código, solicitar código
      setNoteToLock(note.id);
      setLockCodeMode('verify');
      setShowLockCodeModal(true);
    } else {
      // Nota desbloqueada ou não bloqueada
      handleEditNote(note);
    }
  };

  // Função para bloquear nota novamente quando o editor for fechado
  const handleCloseEditor = () => {
    // Verificar se há notas desbloqueadas para re-bloquear
    if (unlockedNotes.size > 0) {
      // Re-bloquear todas as notas que estavam temporariamente desbloqueadas
      setUnlockedNotes(new Set());

      // Mostrar notificação de que as notas foram bloqueadas novamente
      toast.success('Notas bloqueadas novamente com segurança', {
        icon: '🔒',
        duration: 3000,
      });
    }

    setIsEditing(false);
    setShowNewNote(false);
  };

  // Show homepage if user is not logged in AND not showing login transition
  if (!authLoading && !user && !showLoginTransition) {
    return (
      <>
        <Homepage
          darkMode={darkMode}
          onLogin={handleLogin}
          onRegister={handleRegister}
        />

        {/* Auth Modal */}
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          darkMode={darkMode}
          onLoginSuccess={() => setJustLoggedIn(true)}
        />

        {/* Login Transition */}
        <LoginTransition
          show={showLoginTransition}
          darkMode={darkMode}
          userProfile={user}
          onComplete={() => setShowLoginTransition(false)}
        />

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: darkMode ? '#374151' : '#ffffff',
              color: darkMode ? '#ffffff' : '#000000',
              border: darkMode ? '1px solid #4B5563' : '1px solid #E5E7EB',
            },
          }}
        />
      </>
    );
  }

  return (
    <div className={`min-h-screen transition-all duration-300 ${darkMode ? 'dark bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <Header
        darkMode={darkMode}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        viewMode={viewMode}
        setViewMode={setViewMode}
        showSidebar={showSidebar}
        setShowSidebar={setShowSidebar}
        setShowCommandPalette={setShowCommandPalette}
        handleNewNote={handleNewNote}
        focusMode={focusMode}
        setFocusMode={setFocusMode}
        setShowAnalytics={setShowAnalytics}
        setShowQuickCapture={setShowQuickCapture}
        notifications={notifications}
        setShowNotifications={setShowNotifications}
        showNotifications={showNotifications}
        onLogout={handleLogout}
      />

      <div className="flex">
        {/* Sidebar - Hidden in focus mode */}
        {!focusMode && (
          <Sidebar
            darkMode={darkMode}
            showSidebar={showSidebar}
            setShowSidebar={setShowSidebar}
            filterCategory={filterCategory}
            setFilterCategory={setFilterCategory}
            filterTag={filterTag}
            setFilterTag={setFilterTag}
            notes={allNotes || []} // Usar todas as notas para contagens corretas
            allTags={allTags || []}
            categories={categories || []}
            onManageCategories={() => setShowCategoryManager(true)}
            onEditNote={handleEditNote}
          />
        )}

        {/* Main Content */}
        <main className={`flex-1 transition-all duration-300 ${
          focusMode
            ? 'ml-0'
            : showSidebar
              ? 'md:ml-80'
              : 'ml-0'
        }`}>
          <div className="p-6">
            {!sortedNotes || sortedNotes.length === 0 ? (
              <div className="text-center py-20">
                <h2 className="text-2xl font-bold mb-4">Nenhuma nota encontrada</h2>
                <p className="text-gray-500 mb-8">Comece criando sua primeira nota!</p>
                <button
                  onClick={handleNewNote}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all hover:scale-105"
                >
                  Criar Nova Nota
                </button>
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-4'}>
                {sortedNotes && sortedNotes.map((note: any) => (
                  <NoteCard
                    key={note.id}
                    note={note}
                    darkMode={darkMode}
                    viewMode={viewMode}
                    copiedId={copiedId}
                    categories={categories}
                    onEdit={handleEditNoteWithLock}
                    onDelete={handleDeleteNote}
                    onToggleStar={handleToggleStar}
                    onTogglePin={handleTogglePin}
                    onToggleLock={handleLockWithCode}
                    onTogglePublic={handleTogglePublic}
                    onCopy={handleCopyNote}
                    onExport={handleExportNote}
                    onShare={handleShareNote}
                  />
                ))}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Modals */}
      <QuickCaptureModal
        show={showQuickCapture}
        darkMode={darkMode}
        quickCaptureText={quickCaptureText}
        setQuickCaptureText={setQuickCaptureText}
        onClose={() => setShowQuickCapture(false)}
        onSave={handleQuickCapture}
      />

      <CommandPaletteModal
        show={showCommandPalette}
        darkMode={darkMode}
        onClose={() => setShowCommandPalette(false)}
        onNewNote={handleNewNote}
        onQuickCapture={() => setShowQuickCapture(true)}
        onFocusMode={() => setFocusMode(!focusMode)}
        onAnalytics={() => setShowAnalytics(true)}

      />

      <TemplatesModal
        show={showTemplates}
        darkMode={darkMode}
        onClose={() => setShowTemplates(false)}
        onUseTemplate={handleUseTemplate}
      />

      {/* Category Manager */}
      <CategoryManager
        show={showCategoryManager}
        darkMode={darkMode}
        categories={categories}
        onClose={() => setShowCategoryManager(false)}
        onSave={handleSaveCategories}
      />

      {/* Note Editor */}
      <NoteEditor
        show={isEditing}
        darkMode={darkMode}
        isNewNote={showNewNote}
        noteTitle={noteTitle}
        noteContent={noteContent}
        noteCategory={noteCategory}
        noteTags={noteTags}
        noteColor={noteColor}
        noteMood={noteMood}
        noteIsPublic={noteIsPublic}
        categories={categories}
        onClose={handleCloseEditor}
        onSave={handleSaveNote}
        setNoteTitle={setNoteTitle}
        setNoteContent={setNoteContent}
        setNoteCategory={setNoteCategory}
        setNoteTags={setNoteTags}
        setNoteColor={setNoteColor}
        setNoteMood={setNoteMood}
        setNoteIsPublic={setNoteIsPublic}
      />

      {/* Analytics Modal */}
      <AnalyticsModal
        show={showAnalytics}
        darkMode={darkMode}
        onClose={() => setShowAnalytics(false)}
        notes={sortedNotes || []}
        userProfile={user}
      />

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        darkMode={darkMode}
        onLoginSuccess={() => setJustLoggedIn(true)}
      />

      {/* Login Transition */}
      <LoginTransition
        show={showLoginTransition}
        darkMode={darkMode}
        userProfile={user}
        onComplete={() => setShowLoginTransition(false)}
      />

      {/* Lock Code Modal */}
      <LockCodeModal
        show={showLockCodeModal}
        darkMode={darkMode}
        mode={lockCodeMode}
        noteTitle={noteToLock ? sortedNotes?.find((n: Note) => n.id === noteToLock)?.title : undefined}
        onClose={() => {
          setShowLockCodeModal(false);
          setNoteToLock(null);
        }}
        onConfirm={handleLockCodeConfirm}
      />

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: darkMode ? '#374151' : '#ffffff',
            color: darkMode ? '#ffffff' : '#000000',
            border: darkMode ? '1px solid #4B5563' : '1px solid #E5E7EB',
          },
        }}
      />
    </div>
  );
};

export default NotesApp;
