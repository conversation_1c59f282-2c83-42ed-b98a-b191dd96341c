import { useState, useEffect, useMemo } from 'react';
import { Note, Collaborator, Notification, Template } from '../../types';
import { INITIAL_NOTE, INITIAL_COLLABORATORS, INITIAL_NOTIFICATIONS } from '../../constants';
import { calculateStats, filterNotes, sortNotes, getAllTags } from '../../utils';

export const useNotesApp = () => {
  // Core states
  const [notes, setNotes] = useState<Note[]>([INITIAL_NOTE]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [showNewNote, setShowNewNote] = useState<boolean>(false);
  const [darkMode, setDarkMode] = useState<boolean>(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterTag, setFilterTag] = useState<string>('');
  const [filterCategory, setFilterCategory] = useState<string>('');
  const [showSidebar, setShowSidebar] = useState<boolean>(true);
  const [showCommandPalette, setShowCommandPalette] = useState<boolean>(false);
  const [copiedId, setCopiedId] = useState<number | null>(null);

  // Note editing states
  const [noteTitle, setNoteTitle] = useState<string>('');
  const [noteContent, setNoteContent] = useState<string>('');
  const [noteTags, setNoteTags] = useState<string[]>([]);
  const [noteColor, setNoteColor] = useState<string>('#3B82F6');
  const [noteCategory, setNoteCategory] = useState<string>('pessoal');
  const [noteMood, setNoteMood] = useState<string>('neutral');
  const [noteConnections, setNoteConnections] = useState<number[]>([]);

  // Advanced features states
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [showMindMap, setShowMindMap] = useState<boolean>(false);
  const [pomodoroActive, setPomodoroActive] = useState<boolean>(false);
  const [pomodoroTime, setPomodoroTime] = useState<number>(25 * 60);
  const [focusMode, setFocusMode] = useState<boolean>(false);
  const [showAnalytics, setShowAnalytics] = useState<boolean>(false);
  const [dailyStreak, setDailyStreak] = useState<number>(7);
  const [totalWords, setTotalWords] = useState<number>(1337);
  const [showTemplates, setShowTemplates] = useState<boolean>(false);
  const [showQuickCapture, setShowQuickCapture] = useState<boolean>(false);
  const [quickCaptureText, setQuickCaptureText] = useState<string>('');

  // Collaboration states
  const [showShareModal, setShowShareModal] = useState<boolean>(false);
  const [shareEmail, setShareEmail] = useState<string>('');
  const [sharePermission, setSharePermission] = useState<string>('view');
  const [collaborators, setCollaborators] = useState<Collaborator[]>(INITIAL_COLLABORATORS);
  const [activeUsers, setActiveUsers] = useState<Collaborator[]>([]);
  const [showComments, setShowComments] = useState<boolean>(false);
  const [commentText, setCommentText] = useState<string>('');
  const [showVersionHistory, setShowVersionHistory] = useState<boolean>(false);
  const [notifications, setNotifications] = useState<Notification[]>(INITIAL_NOTIFICATIONS);
  const [showNotifications, setShowNotifications] = useState<boolean>(false);

  // Computed values
  const filteredNotes = useMemo(() => {
    return filterNotes(notes, searchTerm, filterTag, filterCategory);
  }, [notes, searchTerm, filterTag, filterCategory]);

  const allTags = useMemo(() => {
    return getAllTags(notes);
  }, [notes]);

  const sortedNotes = useMemo(() => {
    return sortNotes(filteredNotes);
  }, [filteredNotes]);

  const unreadNotifications = notifications.filter(n => !n.read).length;

  // Helper functions
  const addNotification = (type: string, user: string, message: string, time: string) => {
    const newNotification: Notification = {
      id: Date.now(),
      type,
      user,
      message,
      time,
      read: false
    };
    setNotifications(prev => [newNotification, ...prev]);
  };

  const handleNewNote = () => {
    setSelectedNote(null);
    setIsEditing(true);
    setShowNewNote(true);
    setNoteTitle('');
    setNoteContent('');
    setNoteTags([]);
    setNoteColor('#3B82F6');
    setNoteCategory('pessoal');
    setNoteMood('neutral');
    setNoteConnections([]);
  };

  const handleUseTemplate = (template: Template) => {
    setSelectedNote(null);
    setIsEditing(true);
    setShowNewNote(true);
    setNoteTitle(template.name);
    setNoteContent(template.content);
    setNoteTags([template.id]);
    setNoteColor('#3B82F6');
    setNoteCategory('pessoal');
    setNoteMood('neutral');
    setShowTemplates(false);
  };

  const handleSaveNote = () => {
    if (!noteTitle.trim()) return;

    const stats = calculateStats(noteContent);

    if (selectedNote) {
      setNotes(notes.map(note =>
        note.id === selectedNote.id
          ? {
              ...note,
              title: noteTitle,
              content: noteContent,
              tags: noteTags,
              color: noteColor,
              category: noteCategory,
              mood: noteMood,
              updatedAt: new Date().toISOString(),
              wordCount: stats.words,
              readTime: stats.readTime,
              version: (note.version || 1) + 1,
              connections: noteConnections
            }
          : note
      ));

      if (selectedNote.collaborators && selectedNote.collaborators.length > 0) {
        selectedNote.collaborators.forEach((collab: Collaborator) => {
          if (collab.id !== 1) {
            addNotification('edit', 'You', 'editou uma nota compartilhada', 'agora');
          }
        });
      }
    } else {
      const newNote: Note = {
        id: Date.now(),
        title: noteTitle,
        content: noteContent,
        tags: noteTags,
        color: noteColor,
        category: noteCategory,
        mood: noteMood,
        starred: false,
        pinned: false,
        locked: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        wordCount: stats.words,
        readTime: stats.readTime,
        connections: noteConnections,
        todos: [],
        audioNote: null,
        collaborators: [],
        comments: [],
        version: 1,
        isPublic: false,
        permissions: 'owner'
      };
      setNotes([newNote, ...notes]);
      setTotalWords(prev => prev + stats.words);
    }

    setIsEditing(false);
    setShowNewNote(false);
    setNoteConnections([]);
  };

  const handleDeleteNote = (id: number) => {
    const noteToDelete = notes.find(n => n.id === id);
    if (noteToDelete && noteToDelete.permissions !== 'owner') {
      alert('Você não tem permissão para deletar esta nota');
      return;
    }

    setNotes(notes.filter(note => note.id !== id));
    if (selectedNote && selectedNote.id === id) {
      setSelectedNote(null);
      setIsEditing(false);
    }
  };

  const handleEditNote = (note: Note) => {
    if (note.locked && note.permissions !== 'owner') return;

    setSelectedNote(note);
    setIsEditing(true);
    setNoteTitle(note.title);
    setNoteContent(note.content);
    setNoteTags(note.tags);
    setNoteColor(note.color);
    setNoteCategory(note.category);
    setNoteMood(note.mood || 'neutral');
    setNoteConnections(note.connections || []);
  };

  const handleToggleStar = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, starred: !note.starred } : note
    ));
  };

  const handleTogglePin = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, pinned: !note.pinned } : note
    ));
  };

  const handleToggleLock = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, locked: !note.locked } : note
    ));
  };

  const handleTogglePublic = (id: number) => {
    setNotes(notes.map(note =>
      note.id === id ? { ...note, isPublic: !note.isPublic } : note
    ));

    const note = notes.find(n => n.id === id);
    if (note) {
      addNotification('share', 'You', `tornou a nota "${note.title}" ${note.isPublic ? 'privada' : 'pública'}`, 'agora');
    }
  };

  const handleQuickCapture = () => {
    if (!quickCaptureText.trim()) return;

    const newNote: Note = {
      id: Date.now(),
      title: quickCaptureText.split('\n')[0].substring(0, 50) || 'Quick Note',
      content: `<p>${quickCaptureText}</p>`,
      tags: ['quick-capture'],
      color: '#10B981',
      category: 'pessoal',
      starred: false,
      pinned: false,
      locked: false,
      mood: 'neutral',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      wordCount: quickCaptureText.split(' ').length,
      readTime: 1,
      connections: [],
      todos: [],
      audioNote: null,
      collaborators: [],
      comments: [],
      version: 1,
      isPublic: false,
      permissions: 'owner'
    };

    setNotes([newNote, ...notes]);
    setQuickCaptureText('');
    setShowQuickCapture(false);
  };

  return {
    // States
    notes,
    searchTerm,
    selectedNote,
    isEditing,
    showNewNote,
    darkMode,
    viewMode,
    filterTag,
    filterCategory,
    showSidebar,
    showCommandPalette,
    copiedId,
    noteTitle,
    noteContent,
    noteTags,
    noteColor,
    noteCategory,
    noteMood,
    noteConnections,
    isRecording,
    recordingTime,
    showMindMap,
    pomodoroActive,
    pomodoroTime,
    focusMode,
    showAnalytics,
    dailyStreak,
    totalWords,
    showTemplates,
    showQuickCapture,
    quickCaptureText,
    showShareModal,
    shareEmail,
    sharePermission,
    collaborators,
    activeUsers,
    showComments,
    commentText,
    showVersionHistory,
    notifications,
    showNotifications,
    
    // Computed values
    filteredNotes,
    allTags,
    sortedNotes,
    unreadNotifications,
    
    // Setters
    setSearchTerm,
    setSelectedNote,
    setIsEditing,
    setShowNewNote,
    setDarkMode,
    setViewMode,
    setFilterTag,
    setFilterCategory,
    setShowSidebar,
    setShowCommandPalette,
    setCopiedId,
    setNoteTitle,
    setNoteContent,
    setNoteTags,
    setNoteColor,
    setNoteCategory,
    setNoteMood,
    setNoteConnections,
    setIsRecording,
    setRecordingTime,
    setShowMindMap,
    setPomodoroActive,
    setPomodoroTime,
    setFocusMode,
    setShowAnalytics,
    setDailyStreak,
    setTotalWords,
    setShowTemplates,
    setShowQuickCapture,
    setQuickCaptureText,
    setShowShareModal,
    setShareEmail,
    setSharePermission,
    setCollaborators,
    setActiveUsers,
    setShowComments,
    setCommentText,
    setShowVersionHistory,
    setNotifications,
    setShowNotifications,
    
    // Handlers
    handleNewNote,
    handleUseTemplate,
    handleSaveNote,
    handleDeleteNote,
    handleEditNote,
    handleToggleStar,
    handleTogglePin,
    handleToggleLock,
    handleTogglePublic,
    handleQuickCapture,
    addNotification
  };
};
