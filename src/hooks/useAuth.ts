import { useState, useEffect } from 'react'
import {
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth'
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore'
import { auth, db } from '../config/firebase'
import toast from 'react-hot-toast'

// User type based on Firebase User
interface User {
  uid: string
  email: string
  displayName: string
  photoURL?: string
}

interface UserProfile {
  uid: string
  email: string
  displayName: string
  photoURL?: string
  preferences: {
    theme: 'light' | 'dark'
    language: string
    notifications: boolean
    autoSave: boolean
    defaultCategory: string
  }
  stats: {
    totalNotes: number
    totalWords: number
    dailyStreak: number
    lastActiveAt: Date
  }
}

export interface AuthState {
  user: User | null
  userProfile: UserProfile | null
  loading: boolean
  error: Error | null
}

export interface AuthActions {
  signUp: (email: string, password: string, displayName: string) => Promise<void>
  signIn: (email: string, password: string) => Promise<void>
  signInWithGoogle: () => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updateProfile: (data: Partial<UserProfile>) => Promise<void>
  updatePreferences: (preferences: Partial<UserProfile['preferences']>) => Promise<void>
  refreshProfile: () => Promise<void>
}

export function useAuth(): AuthState & AuthActions {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Firebase auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      try {
        if (firebaseUser) {
          // User is signed in
          const user: User = {
            uid: firebaseUser.uid,
            email: firebaseUser.email || '',
            displayName: firebaseUser.displayName || 'Usuário',
            photoURL: firebaseUser.photoURL || undefined
          }

          // Try to get user profile from Firestore, but handle permission errors gracefully
          let profile: UserProfile
          try {
            const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid))

            if (userDoc.exists()) {
              profile = userDoc.data() as UserProfile
            } else {
              // Create default profile
              profile = {
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                photoURL: user.photoURL,
                preferences: {
                  theme: 'dark',
                  language: 'pt-BR',
                  notifications: true,
                  autoSave: true,
                  defaultCategory: 'pessoal'
                },
                stats: {
                  totalNotes: 0,
                  totalWords: 0,
                  dailyStreak: 0,
                  lastActiveAt: new Date()
                }
              }

              // Try to save to Firestore, but don't fail if permissions are missing
              try {
                await setDoc(doc(db, 'users', user.uid), profile)
              } catch (firestoreError) {
                console.warn('Could not save user profile to Firestore:', firestoreError)
                // Continue with local profile
              }
            }
          } catch (firestoreError) {
            console.warn('Could not access Firestore, using default profile:', firestoreError)
            // Create a default profile if Firestore is not accessible
            profile = {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
              photoURL: user.photoURL,
              preferences: {
                theme: 'dark',
                language: 'pt-BR',
                notifications: true,
                autoSave: true,
                defaultCategory: 'pessoal'
              },
              stats: {
                totalNotes: 0,
                totalWords: 0,
                dailyStreak: 0,
                lastActiveAt: new Date()
              }
            }
          }

          setUser(user)
          setUserProfile(profile)
        } else {
          // User is signed out
          setUser(null)
          setUserProfile(null)
        }
      } catch (err) {
        console.error('Auth state change error:', err)
        // Don't set error for permission issues, just log them
        if (err instanceof Error && !err.message.includes('permissions')) {
          setError(err)
        }
      } finally {
        setLoading(false)
      }
    })

    return () => unsubscribe()
  }, [])

  const signUp = async (email: string, password: string, displayName: string) => {
    setLoading(true)
    try {
      // Create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)

      // Update profile with display name
      await updateProfile(userCredential.user, { displayName })

      // Create user profile in Firestore
      const profile: UserProfile = {
        uid: userCredential.user.uid,
        email: userCredential.user.email || '',
        displayName: displayName,
        preferences: {
          theme: 'dark',
          language: 'pt-BR',
          notifications: true,
          autoSave: true,
          defaultCategory: 'pessoal'
        },
        stats: {
          totalNotes: 0,
          totalWords: 0,
          dailyStreak: 0,
          lastActiveAt: new Date()
        }
      }

      // Try to save profile to Firestore, but don't fail if permissions are missing
      try {
        await setDoc(doc(db, 'users', userCredential.user.uid), profile)
      } catch (firestoreError) {
        console.warn('Could not save user profile to Firestore:', firestoreError)
        // Continue without failing
      }

      toast.success('Conta criada com sucesso!')
    } catch (err: any) {
      console.error('Sign up error:', err)
      setError(err)
      toast.error(err.message || 'Erro ao criar conta')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      // Firebase login
      const userCredential = await signInWithEmailAndPassword(auth, email, password)

      // Try to update last active timestamp, but don't fail if Firestore is not accessible
      try {
        await updateDoc(doc(db, 'users', userCredential.user.uid), {
          'stats.lastActiveAt': serverTimestamp()
        })
      } catch (firestoreError) {
        console.warn('Could not update last active timestamp:', firestoreError)
        // Continue without failing
      }

      toast.success('Login realizado com sucesso!')
    } catch (err: any) {
      console.error('Sign in error:', err)
      setError(err)
      toast.error(err.message || 'Erro ao fazer login')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signInWithGoogle = async () => {
    setLoading(true)
    try {
      // Sign in with Google
      const provider = new GoogleAuthProvider()
      const userCredential = await signInWithPopup(auth, provider)

      // Try to check if user profile exists and create/update, but handle permission errors gracefully
      try {
        const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid))
        if (!userDoc.exists()) {
          const profile: UserProfile = {
            uid: userCredential.user.uid,
            email: userCredential.user.email || '',
            displayName: userCredential.user.displayName || 'Google User',
            photoURL: userCredential.user.photoURL || undefined,
            preferences: {
              theme: 'dark',
              language: 'pt-BR',
              notifications: true,
              autoSave: true,
              defaultCategory: 'pessoal'
            },
            stats: {
              totalNotes: 0,
              totalWords: 0,
              dailyStreak: 0,
              lastActiveAt: new Date()
            }
          }

          try {
            await setDoc(doc(db, 'users', userCredential.user.uid), profile)
          } catch (firestoreError) {
            console.warn('Could not save Google user profile to Firestore:', firestoreError)
          }
        } else {
          // Update last active timestamp
          try {
            await updateDoc(doc(db, 'users', userCredential.user.uid), {
              'stats.lastActiveAt': serverTimestamp()
            })
          } catch (firestoreError) {
            console.warn('Could not update last active timestamp for Google user:', firestoreError)
          }
        }
      } catch (firestoreError) {
        console.warn('Could not access Firestore for Google user:', firestoreError)
        // Continue without failing
      }

      toast.success('Login com Google realizado com sucesso!')
    } catch (err: any) {
      console.error('Google sign in error:', err)
      setError(err)
      toast.error(err.message || 'Erro ao fazer login com Google')
      throw err
    } finally {
      setLoading(false)
    }
  }



  const signOut = async () => {
    setLoading(true)
    try {
      // Firebase logout
      await firebaseSignOut(auth)
      toast.success('Logout realizado com sucesso!')
    } catch (err: any) {
      console.error('Sign out error:', err)
      setError(err)
      toast.error(err.message || 'Erro ao fazer logout')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      // TODO: Implement password reset with Firebase
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Email de recuperação enviado!')
    } catch (err: any) {
      setError(err)
      toast.error(err.message || 'Erro ao enviar email de recuperação')
      throw err
    }
  }

  const updateProfile = async (data: Partial<UserProfile>) => {
    if (!user || !userProfile) return

    try {
      const updatedProfile = { ...userProfile, ...data }

      // Update in Firestore
      await updateDoc(doc(db, 'users', user.uid), data)

      setUserProfile(updatedProfile)
      toast.success('Perfil atualizado com sucesso!')
    } catch (err: any) {
      setError(err)
      toast.error(err.message || 'Erro ao atualizar perfil')
      throw err
    }
  }

  const updatePreferences = async (preferences: Partial<UserProfile['preferences']>) => {
    if (!userProfile) return

    try {
      const updatedProfile = {
        ...userProfile,
        preferences: { ...userProfile.preferences, ...preferences }
      }

      // Update in Firestore
      await updateDoc(doc(db, 'users', userProfile.uid), {
        preferences: updatedProfile.preferences
      })

      setUserProfile(updatedProfile)
      toast.success('Preferências atualizadas!')
    } catch (err: any) {
      setError(err)
      toast.error(err.message || 'Erro ao atualizar preferências')
      throw err
    }
  }

  const refreshProfile = async () => {
    if (!user) return

    try {
      // Refresh profile from Firestore
      const userDoc = await getDoc(doc(db, 'users', user.uid))
      if (userDoc.exists()) {
        setUserProfile(userDoc.data() as UserProfile)
      }
    } catch (err: any) {
      setError(err)
      throw err
    }
  }

  return {
    user,
    userProfile,
    loading,
    error,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    resetPassword,
    updateProfile,
    updatePreferences,
    refreshProfile
  }
}
