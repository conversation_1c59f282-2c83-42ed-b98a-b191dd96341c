import { useState, useCallback } from 'react'

export type ThemeMode = 'light' | 'dark' | 'system'

interface ThemeState {
  mode: ThemeMode
  isDark: boolean
}

// Simple localStorage functions
const loadFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue
  
  try {
    const item = window.localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    return defaultValue
  }
}

const saveToLocalStorage = <T>(key: string, value: T): void => {
  if (typeof window === 'undefined') return
  
  try {
    window.localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    // Ignore errors
  }
}

export const useSimpleTheme = () => {
  // Simple system preference detection
  const getSystemPreference = useCallback(() => {
    if (typeof window === 'undefined') return false
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  }, [])

  // Calculate if should be dark
  const calculateIsDark = useCallback((mode: ThemeMode): boolean => {
    switch (mode) {
      case 'dark':
        return true;
      case 'light':
        return false;
      case 'system':
        return getSystemPreference();
      default:
        return false;
    }
  }, [getSystemPreference]);

  // Simple state - no complex initialization
  const [themeState, setThemeState] = useState<ThemeState>(() => {
    const savedMode = loadFromLocalStorage<ThemeMode>('theme-mode', 'light');
    const initialIsDark = calculateIsDark(savedMode);

    // Apply theme immediately
    if (typeof window !== 'undefined') {
      if (initialIsDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }

    return {
      mode: savedMode,
      isDark: initialIsDark
    };
  });

  // Simple theme mode setter
  const setThemeMode = useCallback((mode: ThemeMode) => {
    const newIsDark = calculateIsDark(mode);

    // Apply theme immediately
    if (typeof window !== 'undefined') {
      if (newIsDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }

    // Save to localStorage
    saveToLocalStorage('theme-mode', mode);

    // Update state
    setThemeState({
      mode,
      isDark: newIsDark
    });
  }, [calculateIsDark]);

  // Simple toggle
  const toggleTheme = useCallback(() => {
    setThemeMode(themeState.isDark ? 'light' : 'dark');
  }, [themeState.isDark, setThemeMode]);

  // Simple cycle
  const cycleTheme = useCallback(() => {
    const modes: ThemeMode[] = ['light', 'dark', 'system'];
    const currentIndex = modes.indexOf(themeState.mode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setThemeMode(modes[nextIndex]);
  }, [themeState.mode, setThemeMode]);

  return {
    mode: themeState.mode,
    isDark: themeState.isDark,
    setThemeMode,
    toggleTheme,
    cycleTheme,
    getThemeInfo: () => ({
      mode: themeState.mode,
      isDark: themeState.isDark,
      systemPreference: getSystemPreference()
    })
  };
};
