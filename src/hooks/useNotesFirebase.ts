import { useState, useEffect, useMemo, useCallback } from 'react'
import { Note, Template } from '../types'
import { TEMPLATES, DEFAULT_NOTE_CONTENT } from '../constants'
import {
  calculateStats,
  generateAISuggestions,
  filterNotes,
  sortNotes,
  getAllTags
} from '../utils'
import { useAuth } from './useAuth'
import {
  getUserCategories,
  subscribeToUserCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  createDefaultCategories,
  userHasCategories,
  Category as FirebaseCategory,
  CreateCategoryData
} from '../services/categoriesService'
import { NotesService, CreateNoteData, UpdateNoteData } from '../services/notesService'

// Mock notifications
const MOCK_NOTIFICATIONS = [
  {
    id: '1',
    type: 'collaboration' as const,
    user: '<PERSON>',
    message: 'comentou na sua nota "Projeto React"',
    time: '2 min atrás',
    read: false,
    noteId: '1'
  },
  {
    id: '2',
    type: 'system' as const,
    user: '<PERSON><PERSON><PERSON>',
    message: 'Backup automático realizado com sucesso',
    time: '1h atrás',
    read: true
  }
]

export const useNotesFirebase = () => {
  const { user, userProfile } = useAuth()

  // Initialize notes service
  const notesService = useMemo(() => new NotesService(), [])

  // Core states
  const [notes, setNotes] = useState<Note[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedNote, setSelectedNote] = useState<Note | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [showNewNote, setShowNewNote] = useState(false)

  // UI states
  const [showSidebar, setShowSidebar] = useState(true)

  const [showQuickCapture, setShowQuickCapture] = useState(false)
  const [showCommandPalette, setShowCommandPalette] = useState(false)
  const [showTemplates, setShowTemplates] = useState(false)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showCategoryManager, setShowCategoryManager] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // Filter states
  const [filterCategory, setFilterCategory] = useState('')
  const [filterTag, setFilterTag] = useState('')

  // Editor states
  const [noteTitle, setNoteTitle] = useState('')
  const [noteContent, setNoteContent] = useState('')
  const [noteCategory, setNoteCategory] = useState('') // Sem categoria por padrão
  const [noteTags, setNoteTags] = useState<string[]>([])
  const [noteColor, setNoteColor] = useState('#3B82F6')
  const [noteMood, setNoteMood] = useState<string>('')
  const [noteIsPublic, setNoteIsPublic] = useState(false)

  // Productivity states
  const [focusMode, setFocusMode] = useState(false)

  // Quick capture
  const [quickCaptureText, setQuickCaptureText] = useState('')

  // Categories
  const [categories, setCategories] = useState<FirebaseCategory[]>([])

  // Other states
  const [copiedId, setCopiedId] = useState<string | null>(null)
  const [notifications] = useState(MOCK_NOTIFICATIONS)

  // Load notes from Firebase when user is available
  useEffect(() => {
    if (!user) {
      setNotes([])
      setLoading(false)
      return
    }

    setLoading(true)

    // Subscribe to real-time notes updates
    const unsubscribe = notesService.subscribeToNotes(
      { userId: user.uid },
      (firestoreNotes) => {
        // Convert Firestore notes to app notes format
        const appNotes: Note[] = firestoreNotes.map(note => {
          // Helper function to convert timestamp to Date
          const convertTimestamp = (timestamp: any): Date => {
            if (!timestamp) return new Date()
            if (timestamp.toDate && typeof timestamp.toDate === 'function') {
              return timestamp.toDate()
            }
            if (timestamp.seconds) {
              return new Date(timestamp.seconds * 1000)
            }
            if (timestamp instanceof Date) {
              return timestamp
            }
            return new Date(timestamp)
          }

          return {
            id: note.id!,
            title: note.title || '',
            content: note.content || '', // Will be decrypted when accessed if locked
            tags: note.tags || [],
            color: note.color || '#3B82F6',
            starred: note.starred || false,
            pinned: note.pinned || false,
            createdAt: convertTimestamp(note.createdAt),
            updatedAt: convertTimestamp(note.updatedAt),
            category: note.category || '', // Permitir categoria vazia
            locked: note.locked || false,
            lockCode: note.lockCodeHash, // Store hash for verification
            mood: note.mood !== undefined ? note.mood : '', // Preservar string vazia, só usar fallback se undefined
            wordCount: note.wordCount || 0,
            readTime: note.readTime || 1,
            connections: note.connections || [],
            todos: note.todos || [],
            audioNote: note.audioNote || null,
            collaborators: note.collaborators || [],
            comments: note.comments || [],
            version: note.version || 1,
            isPublic: note.isPublic || false,
            permissions: note.permissions || 'owner',
            userId: note.userId
          }
        })

        setNotes(appNotes)
        setLoading(false)
      },
      (error) => {
        console.error('Error loading notes:', error)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [user, notesService])

  // Load user preferences (comentado para permitir notas sem categoria)
  // useEffect(() => {
  //   if (userProfile?.preferences) {
  //     setNoteCategory(userProfile.preferences.defaultCategory)
  //   }
  // }, [userProfile])

  // Load and subscribe to categories
  useEffect(() => {
    if (!user) return

    const initializeCategories = async () => {
      try {
        // Check if user has categories
        const hasCategories = await userHasCategories(user.uid)

        if (!hasCategories) {
          // Create default categories for new user
          await createDefaultCategories(user.uid)
        }

        // Subscribe to real-time updates
        const unsubscribe = subscribeToUserCategories(user.uid, (categories) => {
          setCategories(categories)
        })

        return unsubscribe
      } catch (error) {
        console.error('Error initializing categories:', error)
      }
    }

    const unsubscribePromise = initializeCategories()

    return () => {
      unsubscribePromise.then(unsubscribe => {
        if (unsubscribe) unsubscribe()
      })
    }
  }, [user])

  // Computed values
  const filteredNotes = useMemo(() => {
    return filterNotes(notes || [], searchTerm, filterTag, filterCategory)
  }, [notes, searchTerm, filterTag, filterCategory])

  const sortedNotes = useMemo(() => {
    return sortNotes(filteredNotes || [])
  }, [filteredNotes])

  const allTags = useMemo(() => {
    return getAllTags(notes || [])
  }, [notes])



  // Note management
  const handleNewNote = useCallback(() => {
    setSelectedNote(null)
    setNoteTitle('')
    setNoteContent('')
    setNoteCategory('') // Sem categoria por padrão
    setNoteTags([])
    setNoteColor('#3B82F6')
    setNoteMood('')
    setNoteIsPublic(false)
    setIsEditing(true)
    setShowNewNote(true)
  }, [])

  const handleEditNote = useCallback((note: Note) => {
    setSelectedNote(note)
    setNoteTitle(note.title)
    setNoteContent(note.content)
    setNoteCategory(note.category || '') // Permitir categoria vazia
    setNoteTags(note.tags)
    setNoteColor(note.color)
    setNoteMood(note.mood || '') // Garantir que seja sempre string
    setNoteIsPublic(note.isPublic || false)
    setIsEditing(true)
    setShowNewNote(false)
  }, [])

  const handleSaveNote = useCallback(async () => {
    if (!user) {
      console.error('Você precisa estar logado para salvar notas')
      return
    }

    if (!noteTitle.trim()) {
      console.error('Título é obrigatório')
      return
    }

    // Debug logs removed to prevent unnecessary re-renders

    try {
      if (selectedNote) {
        // Update existing note
        const updateData: UpdateNoteData = {
          title: noteTitle.trim(),
          content: noteContent,
          category: noteCategory || '', // Garantir que seja string vazia se undefined/null
          tags: noteTags,
          color: noteColor,
          mood: noteMood,
          isPublic: noteIsPublic
        }

        // Debug logs removed
        await notesService.updateNote(selectedNote.id, updateData)
      } else {
        // Create new note
        const createData: CreateNoteData = {
          title: noteTitle.trim(),
          content: noteContent,
          category: noteCategory || '', // Garantir que seja string vazia se undefined/null
          tags: noteTags,
          color: noteColor,
          mood: noteMood,
          isPublic: noteIsPublic
        }

        // Debug logs removed
        await notesService.createNote(user.uid, createData)
      }

      // Reset form
      setIsEditing(false)
      setShowNewNote(false)
      setSelectedNote(null)
      setNoteTitle('')
      setNoteContent('')
      setNoteCategory('') // Reset categoria para vazio
      setNoteTags([])
      setNoteColor('#3B82F6') // Reset cor padrão
      setNoteMood('') // Reset humor
      setNoteIsPublic(false) // Reset privacidade
    } catch (error) {
      console.error('Error saving note:', error)
    }
  }, [user, selectedNote, noteTitle, noteContent, noteCategory, noteTags, noteColor, noteMood, noteIsPublic])

  const handleDeleteNote = useCallback(async (noteId: string) => {
    if (!user) return

    try {
      await notesService.deleteNote(noteId)
    } catch (error) {
      console.error('Error deleting note:', error)
    }
  }, [user, notesService])

  const handleToggleStar = useCallback(async (noteId: string) => {
    if (!user) return

    try {
      const note = notes.find(n => n.id === noteId)
      if (note) {
        await notesService.updateNote(noteId, { starred: !note.starred })
      }
    } catch (error) {
      console.error('Error toggling star:', error)
    }
  }, [user, notes, notesService])

  const handleTogglePin = useCallback(async (noteId: string) => {
    if (!user) return

    try {
      const note = notes.find(n => n.id === noteId)
      if (note) {
        await notesService.updateNote(noteId, { pinned: !note.pinned })
      }
    } catch (error) {
      console.error('Error toggling pin:', error)
    }
  }, [user, notes, notesService])

  const handleToggleLock = useCallback(async (noteId: string, lockCode?: string) => {
    if (!user) return

    try {
      const note = notes.find(n => n.id === noteId)
      if (!note) return

      if (!note.locked && lockCode) {
        // Bloquear nota com código
        await notesService.updateNote(noteId, {
          locked: true,
          lockCode: lockCode
        })
      } else if (note.locked) {
        // Desbloquear nota
        await notesService.updateNote(noteId, {
          locked: false,
          lockCode: undefined
        })
      }
    } catch (error) {
      console.error('Error toggling lock:', error)
    }
  }, [user, notes, notesService])

  const handleUnlockNote = useCallback(async (noteId: string, inputCode: string) => {
    if (!user) return false

    try {
      const note = notes.find(n => n.id === noteId)
      if (!note || !note.locked || !note.lockCode) return false

      // Verificar código usando o serviço (que fará hash comparison)
      return await notesService.verifyLockCode(noteId, inputCode)
    } catch (error) {
      console.error('Error unlocking note:', error)
      return false
    }
  }, [user, notes, notesService])

  const handleTogglePublic = useCallback(async (noteId: string) => {
    if (!user) return

    try {
      const note = notes.find(n => n.id === noteId)
      if (note) {
        await notesService.updateNote(noteId, { isPublic: !note.isPublic })
      }
    } catch (error) {
      console.error('Error toggling public:', error)
    }
  }, [user, notes, notesService])

  const handleQuickCapture = useCallback(async () => {
    if (!user || !quickCaptureText.trim()) return

    try {
      const createData: CreateNoteData = {
        title: `Quick Note - ${new Date().toLocaleDateString()}`,
        content: quickCaptureText,
        category: '', // Sem categoria para quick capture
        tags: ['quick-capture'],
        color: '#10B981',
        mood: ''
      }

      await notesService.createNote(user.uid, createData)
      setQuickCaptureText('')
      setShowQuickCapture(false)
    } catch (error) {
      console.error('Error creating quick note:', error)
    }
  }, [user, quickCaptureText, notesService])

  const handleUseTemplate = useCallback((template: Template) => {
    setNoteTitle(template.name)
    setNoteContent(template.content)
    setNoteCategory('') // Sem categoria por padrão para templates
    setNoteTags([template.id])
    setNoteColor('#3B82F6') // Cor padrão
    setNoteMood('') // Sem humor por padrão
    setNoteIsPublic(false) // Privado por padrão
    setIsEditing(true)
    setShowNewNote(true)
    setShowTemplates(false)
  }, [])

  const handleSaveCategories = useCallback(async (newCategories: FirebaseCategory[]) => {
    if (!user) return

    try {
      // Get current categories from Firebase
      const currentCategories = await getUserCategories(user.uid)

      // Find categories to create, update, and delete
      const categoriesToCreate = newCategories.filter(newCat =>
        !newCat.id || !currentCategories.find(current => current.id === newCat.id)
      )

      const categoriesToUpdate = newCategories.filter(newCat =>
        newCat.id && currentCategories.find(current => current.id === newCat.id)
      )

      const categoriesToDelete = currentCategories.filter(current =>
        !newCategories.find(newCat => newCat.id === current.id)
      )

      // Create new categories
      for (const category of categoriesToCreate) {
        const categoryData: CreateCategoryData = {
          name: category.name,
          color: category.color,
          icon: category.icon,
          description: category.description
        }
        await createCategory(user.uid, categoryData)
      }

      // Update existing categories
      for (const category of categoriesToUpdate) {
        if (category.id) {
          await updateCategory(category.id, {
            name: category.name,
            color: category.color,
            icon: category.icon,
            description: category.description
          })
        }
      }

      // Delete removed categories (but not default ones)
      for (const category of categoriesToDelete) {
        if (!category.isDefault) {
          await deleteCategory(category.id)
        }
      }

      console.log('Categories saved successfully')
    } catch (error) {
      console.error('Error saving categories:', error)
    }
  }, [user])

  const refreshNotes = useCallback(async () => {
    if (!user) return

    setRefreshing(true)
    // Simulate refresh
    setTimeout(() => setRefreshing(false), 1000)
  }, [user])

  return {
    // Data
    notes: sortedNotes,
    allNotes: notes, // Todas as notas sem filtros para contagens
    loading,
    refreshing,
    allTags,
    categories,
    notifications,

    // UI states
    showSidebar,

    showQuickCapture,
    showCommandPalette,
    showTemplates,
    showAnalytics,
    showNotifications,
    showCategoryManager,
    viewMode,

    // Filter states
    searchTerm,
    filterCategory,
    filterTag,

    // Editor states
    selectedNote,
    isEditing,
    showNewNote,
    noteTitle,
    noteContent,
    noteCategory,
    noteTags,
    noteColor,
    noteMood,
    noteIsPublic,

    // Productivity states
    focusMode,

    // Quick capture
    quickCaptureText,

    // Other states
    copiedId,

    // Setters
    setShowSidebar,

    setShowQuickCapture,
    setShowCommandPalette,
    setShowTemplates,
    setShowAnalytics,
    setShowNotifications,
    setShowCategoryManager,
    setViewMode,
    setSearchTerm,
    setFilterCategory,
    setFilterTag,
    setIsEditing,
    setShowNewNote,
    setNoteTitle,
    setNoteContent,
    setNoteCategory,
    setNoteTags,
    setNoteColor,
    setNoteMood,
    setNoteIsPublic,
    setFocusMode,
    setQuickCaptureText,
    setCopiedId,

    // Handlers
    handleNewNote,
    handleEditNote,
    handleSaveNote,
    handleDeleteNote,
    handleToggleStar,
    handleTogglePin,
    handleToggleLock,
    handleUnlockNote,
    handleTogglePublic,
    handleQuickCapture,
    handleUseTemplate,
    handleSaveCategories,
    refreshNotes
  }
}
