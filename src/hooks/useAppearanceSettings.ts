import { useEffect } from 'react';
import { useAuth } from './useAuth';

export const useAppearanceSettings = () => {
  const { userProfile } = useAuth();

  // Função para aplicar tamanho da fonte
  const applyFontSize = (fontSize: string) => {
    const root = document.documentElement;
    
    // Remove classes anteriores
    root.classList.remove('font-small', 'font-medium', 'font-large', 'font-extra-large');
    
    // Aplica nova classe
    switch (fontSize) {
      case 'small':
        root.classList.add('font-small');
        root.style.fontSize = '14px';
        break;
      case 'medium':
        root.classList.add('font-medium');
        root.style.fontSize = '16px';
        break;
      case 'large':
        root.classList.add('font-large');
        root.style.fontSize = '18px';
        break;
      case 'extra-large':
        root.classList.add('font-extra-large');
        root.style.fontSize = '20px';
        break;
      default:
        root.style.fontSize = '16px';
    }
    
    console.log('🎨 Font size applied globally:', fontSize);
  };

  // Função para aplicar modo compacto
  const applyCompactMode = (isCompact: boolean) => {
    const root = document.documentElement;
    
    if (isCompact) {
      root.classList.add('compact-mode');
      root.style.setProperty('--spacing-scale', '0.75');
    } else {
      root.classList.remove('compact-mode');
      root.style.setProperty('--spacing-scale', '1');
    }
    
    console.log('🎨 Compact mode applied globally:', isCompact);
  };

  // Aplicar configurações automaticamente quando o userProfile carrega
  useEffect(() => {
    if (userProfile?.preferences) {
      const { fontSize, compactMode } = userProfile.preferences;
      
      console.log('🎨 Aplicando configurações de aparência do Firebase:', {
        fontSize,
        compactMode
      });
      
      // Aplicar tamanho da fonte
      if (fontSize) {
        applyFontSize(fontSize);
      } else {
        // Aplicar padrão se não houver configuração
        applyFontSize('medium');
      }
      
      // Aplicar modo compacto
      if (compactMode !== undefined) {
        applyCompactMode(compactMode);
      } else {
        // Aplicar padrão se não houver configuração
        applyCompactMode(false);
      }
    }
  }, [userProfile?.preferences]);

  // Aplicar configurações padrão na primeira carga (antes do Firebase carregar)
  useEffect(() => {
    // Só aplicar se não há userProfile ainda (primeira carga)
    if (!userProfile) {
      console.log('🎨 Aplicando configurações padrão (primeira carga)');
      applyFontSize('medium');
      applyCompactMode(false);
    }
  }, [userProfile]);

  return {
    applyFontSize,
    applyCompactMode
  };
};
