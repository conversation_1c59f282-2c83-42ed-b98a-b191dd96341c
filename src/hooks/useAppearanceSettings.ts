import { useEffect } from 'react';
import { useAuth } from './useAuth';

export const useAppearanceSettings = () => {
  const { userProfile, user } = useAuth();

  // Função para aplicar tamanho da fonte
  const applyFontSize = (fontSize: string) => {
    console.log('🎨 Aplicando tamanho da fonte:', fontSize);

    const root = document.documentElement;

    // Remove classes anteriores
    root.classList.remove('font-small', 'font-medium', 'font-large', 'font-extra-large');

    // Aplica nova classe e estilo inline
    switch (fontSize) {
      case 'small':
        root.classList.add('font-small');
        root.style.fontSize = '14px';
        break;
      case 'medium':
        root.classList.add('font-medium');
        root.style.fontSize = '16px';
        break;
      case 'large':
        root.classList.add('font-large');
        root.style.fontSize = '18px';
        break;
      case 'extra-large':
        root.classList.add('font-extra-large');
        root.style.fontSize = '20px';
        break;
      default:
        root.style.fontSize = '16px';
    }

    // Salvar no localStorage
    try {
      localStorage.setItem('noteflow-fontSize', fontSize);
      console.log('💾 Font size saved to localStorage:', fontSize);
    } catch (error) {
      console.error('Error saving fontSize to localStorage:', error);
    }

    console.log('✅ Font size applied:', fontSize, 'DOM fontSize:', root.style.fontSize);
  };

  // Função para aplicar modo compacto
  const applyCompactMode = (isCompact: boolean) => {
    console.log('🎨 Aplicando modo compacto:', isCompact);

    const root = document.documentElement;

    if (isCompact) {
      root.classList.add('compact-mode');
      root.style.setProperty('--spacing-scale', '0.75');
    } else {
      root.classList.remove('compact-mode');
      root.style.setProperty('--spacing-scale', '1');
    }

    // Salvar no localStorage
    try {
      localStorage.setItem('noteflow-compactMode', JSON.stringify(isCompact));
      console.log('💾 Compact mode saved to localStorage:', isCompact);
    } catch (error) {
      console.error('Error saving compactMode to localStorage:', error);
    }

    console.log('✅ Compact mode applied:', isCompact);
  };

  // Aplicar configurações do localStorage na inicialização
  useEffect(() => {
    console.log('🚀 Inicializando configurações de aparência...');

    // Carregar do localStorage
    let savedFontSize = 'medium';
    let savedCompactMode = false;

    try {
      const fontSizeFromStorage = localStorage.getItem('noteflow-fontSize');
      if (fontSizeFromStorage) {
        savedFontSize = fontSizeFromStorage;
      }

      const compactModeFromStorage = localStorage.getItem('noteflow-compactMode');
      if (compactModeFromStorage) {
        savedCompactMode = JSON.parse(compactModeFromStorage);
      }
    } catch (error) {
      console.error('Error loading from localStorage:', error);
    }

    console.log('📦 Configurações do localStorage carregadas:', {
      fontSize: savedFontSize,
      compactMode: savedCompactMode
    });

    // Aplicar imediatamente
    applyFontSize(savedFontSize);
    applyCompactMode(savedCompactMode);

  }, []); // Executar apenas uma vez na inicialização

  // Aplicar configurações do Firebase quando disponíveis
  useEffect(() => {
    if (userProfile?.preferences && user) {
      const { fontSize, compactMode } = userProfile.preferences;

      console.log('🔥 Aplicando configurações do Firebase:', {
        fontSize,
        compactMode,
        fullPreferences: userProfile.preferences
      });

      // Aplicar tamanho da fonte do Firebase
      if (fontSize) {
        applyFontSize(fontSize);
      }

      // Aplicar modo compacto do Firebase
      if (compactMode !== undefined) {
        applyCompactMode(compactMode);
      }

      console.log('✅ Configurações do Firebase aplicadas');
    }
  }, [userProfile?.preferences, user]);

  return {
    applyFontSize,
    applyCompactMode
  };
};
