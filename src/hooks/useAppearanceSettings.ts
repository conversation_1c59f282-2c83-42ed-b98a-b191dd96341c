import { useEffect, useState } from 'react';
import { useAuth } from './useAuth';
import { loadFromLocalStorage, saveToLocalStorage } from '../utils';

export const useAppearanceSettings = () => {
  const { userProfile, user } = useAuth();
  const [hasAppliedSettings, setHasAppliedSettings] = useState(false);

  // Função para aplicar tamanho da fonte
  const applyFontSize = (fontSize: string, saveToLocal: boolean = true) => {
    const root = document.documentElement;

    console.log('🎨 Aplicando tamanho da fonte:', fontSize);

    // Remove classes anteriores
    root.classList.remove('font-small', 'font-medium', 'font-large', 'font-extra-large');

    // Aplica nova classe
    switch (fontSize) {
      case 'small':
        root.classList.add('font-small');
        root.style.fontSize = '14px';
        break;
      case 'medium':
        root.classList.add('font-medium');
        root.style.fontSize = '16px';
        break;
      case 'large':
        root.classList.add('font-large');
        root.style.fontSize = '18px';
        break;
      case 'extra-large':
        root.classList.add('font-extra-large');
        root.style.fontSize = '20px';
        break;
      default:
        root.style.fontSize = '16px';
    }

    // Salvar no localStorage como backup
    if (saveToLocal) {
      saveToLocalStorage('appearance-fontSize', fontSize);
    }

    console.log('✅ Font size applied:', fontSize, 'Classes:', root.className);
  };

  // Função para aplicar modo compacto
  const applyCompactMode = (isCompact: boolean, saveToLocal: boolean = true) => {
    const root = document.documentElement;

    console.log('🎨 Aplicando modo compacto:', isCompact);

    if (isCompact) {
      root.classList.add('compact-mode');
      root.style.setProperty('--spacing-scale', '0.75');
    } else {
      root.classList.remove('compact-mode');
      root.style.setProperty('--spacing-scale', '1');
    }

    // Salvar no localStorage como backup
    if (saveToLocal) {
      saveToLocalStorage('appearance-compactMode', isCompact);
    }

    console.log('✅ Compact mode applied:', isCompact);
  };

  // Aplicar configurações do localStorage imediatamente (antes do Firebase)
  useEffect(() => {
    if (!hasAppliedSettings) {
      console.log('🎨 Aplicando configurações do localStorage (primeira carga)');

      // Carregar do localStorage
      const savedFontSize = loadFromLocalStorage('appearance-fontSize', 'medium');
      const savedCompactMode = loadFromLocalStorage('appearance-compactMode', false);

      console.log('📦 Configurações do localStorage:', {
        fontSize: savedFontSize,
        compactMode: savedCompactMode
      });

      // Aplicar imediatamente
      applyFontSize(savedFontSize, false);
      applyCompactMode(savedCompactMode, false);

      setHasAppliedSettings(true);
    }
  }, [hasAppliedSettings]);

  // Aplicar configurações do Firebase quando disponíveis (sobrescreve localStorage)
  useEffect(() => {
    if (userProfile?.preferences && user) {
      const { fontSize, compactMode } = userProfile.preferences;

      console.log('🔥 Aplicando configurações do Firebase (sobrescreve localStorage):', {
        fontSize,
        compactMode,
        userProfile: userProfile.preferences
      });

      // Aplicar tamanho da fonte
      const finalFontSize = fontSize || 'medium';
      applyFontSize(finalFontSize, true);

      // Aplicar modo compacto
      const finalCompactMode = compactMode !== undefined ? compactMode : false;
      applyCompactMode(finalCompactMode, true);

      console.log('✅ Configurações do Firebase aplicadas com sucesso');
    }
  }, [userProfile?.preferences, user]);

  return {
    applyFontSize,
    applyCompactMode
  };
};
