import { useState, useEffect, useCallback } from 'react';
import { saveToLocalStorage, loadFromLocalStorage } from '../utils';
import { toast } from 'react-hot-toast';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
}

export const useTheme = () => {
  // Detectar preferência do sistema
  const getSystemPreference = useCallback((): boolean => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }, []);

  // Função para aplicar tema imediatamente
  const applyThemeToDOM = useCallback((isDark: boolean) => {
    if (typeof window === 'undefined') return;

    console.log('🎨 Aplicando tema ao DOM:', isDark ? 'dark' : 'light');

    // Aplicar classe dark
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Verificar se foi aplicado
    const applied = document.documentElement.classList.contains('dark');
    console.log('✅ Classe dark aplicada:', applied);

    // Forçar re-render dos estilos
    document.documentElement.style.colorScheme = isDark ? 'dark' : 'light';

    // Forçar re-render do CSS Tailwind
    const style = document.createElement('style');
    style.textContent = `
      html.dark { color-scheme: dark; }
      html:not(.dark) { color-scheme: light; }
    `;
    document.head.appendChild(style);
    setTimeout(() => document.head.removeChild(style), 10);

    // Trigger reflow forçado
    document.documentElement.offsetHeight;

    console.log('🔄 Forçando re-render completo');
  }, []);

  // Calcular se deve usar dark mode baseado no modo
  const calculateIsDark = useCallback((mode: ThemeMode): boolean => {
    switch (mode) {
      case 'light':
        return false;
      case 'dark':
        return true;
      case 'system':
        return getSystemPreference();
      default:
        return false;
    }
  }, [getSystemPreference]);

  // Estado inicial
  const [themeState, setThemeState] = useState<ThemeState>(() => {
    const savedMode = loadFromLocalStorage<ThemeMode>('theme-mode', 'system');
    const initialIsDark = calculateIsDark(savedMode);

    // Apply theme immediately during initialization
    if (initialIsDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    return {
      mode: savedMode,
      isDark: initialIsDark
    };
  });

  // Apply theme on initialization (moved to state initialization)

  // Listener para mudanças na preferência do sistema
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      setThemeState(prev => {
        if (prev.mode === 'system') {
          const newIsDark = e.matches;

          console.log('🎨 Sistema mudou tema para:', newIsDark ? 'dark' : 'light');

          // Aplicar tema imediatamente
          if (newIsDark) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }

          // Refresh para garantir aplicação completa quando sistema muda
          console.log('🔄 Refresh por mudança do sistema...');

          // Notificação de mudança do sistema
          toast.success(`Sistema mudou para tema ${newIsDark ? 'escuro' : 'claro'}`, {
            icon: newIsDark ? '🌙' : '☀️',
            duration: 1500,
          });

          setTimeout(() => {
            window.location.reload();
          }, 800);

          return {
            ...prev,
            isDark: newIsDark
          };
        }
        return prev;
      });
    };

    // Adicionar listener
    mediaQuery.addEventListener('change', handleSystemThemeChange);

    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []); // Remove dependencies to prevent loops

  // Aplicar classe dark ao documento quando o estado muda
  useEffect(() => {
    if (themeState.isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [themeState.isDark]);

  // Função para alterar o modo do tema
  const setThemeMode = useCallback((mode: ThemeMode, forceRefresh: boolean = true) => {
    const newIsDark = calculateIsDark(mode);

    console.log('🎨 Alterando tema para:', mode, 'isDark:', newIsDark);

    // Aplicar tema imediatamente
    if (newIsDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Salvar no localStorage
    saveToLocalStorage('theme-mode', mode);

    // Atualizar estado
    setThemeState({
      mode,
      isDark: newIsDark
    });

    // Refresh automático após mudança de tema para garantir aplicação completa
    if (forceRefresh) {
      console.log('🔄 Aplicando refresh para garantir tema...');

      // Mostrar notificação de que o tema está sendo aplicado
      toast.success(`Tema ${mode === 'light' ? 'claro' : mode === 'dark' ? 'escuro' : 'sistema'} aplicado!`, {
        icon: mode === 'light' ? '☀️' : mode === 'dark' ? '🌙' : '💻',
        duration: 1500,
      });

      setTimeout(() => {
        window.location.reload();
      }, 800); // Delay maior para mostrar a notificação
    }

  }, [calculateIsDark]);

  // Função para alternar entre light/dark (mantém system se estiver ativo)
  const toggleTheme = useCallback((forceRefresh: boolean = true) => {
    if (themeState.mode === 'system') {
      // Se está em system, vai para o oposto do sistema
      const systemIsDark = getSystemPreference();
      setThemeMode(systemIsDark ? 'light' : 'dark', forceRefresh);
    } else {
      // Se está em light/dark, alterna
      setThemeMode(themeState.isDark ? 'light' : 'dark', forceRefresh);
    }
  }, [themeState.mode, themeState.isDark, getSystemPreference, setThemeMode]);

  // Função para ciclar entre todos os modos
  const cycleTheme = useCallback((forceRefresh: boolean = true) => {
    const modes: ThemeMode[] = ['light', 'dark', 'system'];
    const currentIndex = modes.indexOf(themeState.mode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setThemeMode(modes[nextIndex], forceRefresh);
  }, [themeState.mode, setThemeMode]);

  // Função para obter informações do tema atual
  const getThemeInfo = useCallback(() => {
    const systemPreference = getSystemPreference();

    return {
      mode: themeState.mode,
      isDark: themeState.isDark,
      systemPreference,
      isUsingSystemTheme: themeState.mode === 'system',
      effectiveTheme: themeState.isDark ? 'dark' : 'light'
    };
  }, [themeState, getSystemPreference]);

  // Função para mudança de tema sem refresh (para casos especiais)
  const setThemeModeNoRefresh = useCallback((mode: ThemeMode) => {
    setThemeMode(mode, false);
  }, [setThemeMode]);

  return {
    // Estado atual
    mode: themeState.mode,
    isDark: themeState.isDark,

    // Funções principais (com refresh)
    setThemeMode,
    toggleTheme,
    cycleTheme,

    // Função especial sem refresh
    setThemeModeNoRefresh,

    // Utilitários
    getThemeInfo,
    isLight: !themeState.isDark,
    isSystem: themeState.mode === 'system',
    systemPreference: getSystemPreference()
  };
};

export default useTheme;
