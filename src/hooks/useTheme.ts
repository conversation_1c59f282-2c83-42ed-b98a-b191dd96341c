import { useState, useEffect, useCallback } from 'react';
import { saveToLocalStorage, loadFromLocalStorage } from '../utils';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
}

export const useTheme = () => {
  // Detectar preferência do sistema
  const getSystemPreference = useCallback((): boolean => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }, []);

  // Função para aplicar tema imediatamente
  const applyThemeToDOM = useCallback((isDark: boolean) => {
    if (typeof window === 'undefined') return;

    console.log('🎨 Aplicando tema ao DOM:', isDark ? 'dark' : 'light');

    // Aplicar classe dark
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Verificar se foi aplicado
    const applied = document.documentElement.classList.contains('dark');
    console.log('✅ Classe dark aplicada:', applied);

    // Forçar re-render dos estilos
    document.documentElement.style.colorScheme = isDark ? 'dark' : 'light';

    // Forçar re-render do CSS Tailwind
    const style = document.createElement('style');
    style.textContent = `
      html.dark { color-scheme: dark; }
      html:not(.dark) { color-scheme: light; }
    `;
    document.head.appendChild(style);
    setTimeout(() => document.head.removeChild(style), 10);

    // Trigger reflow forçado
    document.documentElement.offsetHeight;

    console.log('🔄 Forçando re-render completo');
  }, []);

  // Calcular se deve usar dark mode baseado no modo
  const calculateIsDark = useCallback((mode: ThemeMode): boolean => {
    switch (mode) {
      case 'light':
        return false;
      case 'dark':
        return true;
      case 'system':
        return getSystemPreference();
      default:
        return false;
    }
  }, [getSystemPreference]);

  // Estado inicial
  const [themeState, setThemeState] = useState<ThemeState>(() => {
    const savedMode = loadFromLocalStorage<ThemeMode>('theme-mode', 'system');
    const initialIsDark = calculateIsDark(savedMode);

    console.log('🚀 Inicializando tema:', savedMode, 'isDark:', initialIsDark);

    return {
      mode: savedMode,
      isDark: initialIsDark
    };
  });

  // Aplicar tema na inicialização
  useEffect(() => {
    console.log('🚀 Aplicando tema inicial:', themeState.isDark);
    applyThemeToDOM(themeState.isDark);
  }, [applyThemeToDOM]); // Executar quando applyThemeToDOM estiver disponível

  // Listener para mudanças na preferência do sistema
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      setThemeState(prev => {
        if (prev.mode === 'system') {
          const newIsDark = e.matches;

          // Aplicar tema imediatamente
          applyThemeToDOM(newIsDark);

          return {
            ...prev,
            isDark: newIsDark
          };
        }
        return prev;
      });
    };

    // Adicionar listener
    mediaQuery.addEventListener('change', handleSystemThemeChange);

    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [applyThemeToDOM]);

  // Aplicar classe dark ao documento (backup - caso a aplicação imediata falhe)
  useEffect(() => {
    // Aplicar com um pequeno delay para garantir que o DOM está pronto
    const timeoutId = setTimeout(() => {
      if (themeState.isDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }, 10);

    return () => clearTimeout(timeoutId);
  }, [themeState.isDark]);

  // Função para alterar o modo do tema
  const setThemeMode = useCallback((mode: ThemeMode) => {
    const newIsDark = calculateIsDark(mode);

    // Aplicar tema imediatamente
    applyThemeToDOM(newIsDark);

    // Salvar no localStorage
    saveToLocalStorage('theme-mode', mode);

    // Atualizar estado
    setThemeState({
      mode,
      isDark: newIsDark
    });

  }, [calculateIsDark, applyThemeToDOM, setThemeState]);

  // Função para alternar entre light/dark (mantém system se estiver ativo)
  const toggleTheme = useCallback(() => {
    if (themeState.mode === 'system') {
      // Se está em system, vai para o oposto do sistema
      const systemIsDark = getSystemPreference();
      setThemeMode(systemIsDark ? 'light' : 'dark');
    } else {
      // Se está em light/dark, alterna
      setThemeMode(themeState.isDark ? 'light' : 'dark');
    }
  }, [themeState.mode, themeState.isDark, getSystemPreference, setThemeMode]);

  // Função para ciclar entre todos os modos
  const cycleTheme = useCallback(() => {
    const modes: ThemeMode[] = ['light', 'dark', 'system'];
    const currentIndex = modes.indexOf(themeState.mode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setThemeMode(modes[nextIndex]);
  }, [themeState.mode, setThemeMode]);

  // Função para obter informações do tema atual
  const getThemeInfo = useCallback(() => {
    const systemPreference = getSystemPreference();

    return {
      mode: themeState.mode,
      isDark: themeState.isDark,
      systemPreference,
      isUsingSystemTheme: themeState.mode === 'system',
      effectiveTheme: themeState.isDark ? 'dark' : 'light'
    };
  }, [themeState, getSystemPreference]);

  return {
    // Estado atual
    mode: themeState.mode,
    isDark: themeState.isDark,

    // Funções
    setThemeMode,
    toggleTheme,
    cycleTheme,
    getThemeInfo,

    // Utilitários
    isLight: !themeState.isDark,
    isSystem: themeState.mode === 'system',
    systemPreference: getSystemPreference()
  };
};

export default useTheme;
