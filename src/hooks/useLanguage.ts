import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from './useAuth';
import { languageOptions, getLanguageName, getLanguageFlag } from '../i18n';

export const useLanguage = () => {
  const { i18n, t } = useTranslation();
  const { userProfile, updatePreferences } = useAuth();
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  // Get current language
  const currentLanguage = i18n.language;

  // Change language function
  const changeLanguage = async (languageCode: string) => {
    try {
      setIsChangingLanguage(true);
      console.log('🌐 Changing language to:', languageCode);

      // Change i18n language
      await i18n.changeLanguage(languageCode);

      // Save to localStorage
      localStorage.setItem('noteflow-language', languageCode);

      // Update user preferences in Firebase if user is logged in
      if (userProfile) {
        await updatePreferences({
          ...userProfile.preferences,
          language: languageCode
        });
        console.log('✅ Language saved to Firebase:', languageCode);
      }

      console.log('✅ Language changed successfully to:', languageCode);
    } catch (error) {
      console.error('❌ Error changing language:', error);
    } finally {
      setIsChangingLanguage(false);
    }
  };

  // Apply language from Firebase when user profile loads
  useEffect(() => {
    if (userProfile?.preferences?.language) {
      const savedLanguage = userProfile.preferences.language;
      console.log('🔥 Applying language from Firebase:', savedLanguage);
      
      if (savedLanguage !== currentLanguage) {
        i18n.changeLanguage(savedLanguage);
      }
    }
  }, [userProfile?.preferences?.language, currentLanguage, i18n]);

  // Get language info
  const getCurrentLanguageInfo = () => {
    const option = languageOptions.find(lang => lang.code === currentLanguage);
    return {
      code: currentLanguage,
      name: option?.name || currentLanguage,
      flag: option?.flag || '🌐'
    };
  };

  return {
    // Current language info
    currentLanguage,
    currentLanguageInfo: getCurrentLanguageInfo(),
    
    // Available languages
    availableLanguages: languageOptions,
    
    // Functions
    changeLanguage,
    getLanguageName,
    getLanguageFlag,
    
    // Translation function
    t,
    
    // State
    isChangingLanguage
  };
};
