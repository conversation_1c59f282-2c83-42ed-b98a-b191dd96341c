import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from './useAuth';
import { languageOptions, getLanguageName, getLanguageFlag } from '../i18n';

export const useLanguage = () => {
  const { i18n, t } = useTranslation();
  const { userProfile, updatePreferences } = useAuth();
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  // Get current language
  const currentLanguage = i18n.language;

  // Change language function
  const changeLanguage = async (languageCode: string) => {
    try {
      setIsChangingLanguage(true);
      console.log('🌐 Starting language change to:', languageCode);
      console.log('🌐 Current language before change:', i18n.language);
      console.log('🌐 Available resources:', Object.keys(i18n.options.resources || {}));

      // Change i18n language
      await i18n.changeLanguage(languageCode);
      console.log('🌐 i18n.changeLanguage completed, new language:', i18n.language);

      // Save to localStorage
      localStorage.setItem('noteflow-language', languageCode);
      console.log('🌐 Language saved to localStorage:', languageCode);

      // Test translation after change
      console.log('🌐 Test translation after change:', i18n.t('common.save'));

      // Update user preferences in Firebase if user is logged in
      if (userProfile) {
        await updatePreferences({
          ...userProfile.preferences,
          language: languageCode
        });
        console.log('✅ Language saved to Firebase:', languageCode);
      }

      console.log('✅ Language changed successfully to:', languageCode);
    } catch (error) {
      console.error('❌ Error changing language:', error);
    } finally {
      setIsChangingLanguage(false);
    }
  };

  // Apply language from Firebase when user profile loads
  useEffect(() => {
    if (userProfile?.preferences?.language) {
      const savedLanguage = userProfile.preferences.language;
      console.log('🔥 Applying language from Firebase:', savedLanguage);

      if (savedLanguage !== currentLanguage) {
        i18n.changeLanguage(savedLanguage);
      }
    }
  }, [userProfile?.preferences?.language, currentLanguage, i18n]);

  // Force re-render when language changes
  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      console.log('🌐 Language changed event received:', lng);
      // This will trigger a re-render of components using this hook
    };

    i18n.on('languageChanged', handleLanguageChanged);

    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [i18n]);

  // Get language info
  const getCurrentLanguageInfo = () => {
    const option = languageOptions.find(lang => lang.code === currentLanguage);
    return {
      code: currentLanguage,
      name: option?.name || currentLanguage,
      flag: option?.flag || '🌐'
    };
  };

  return {
    // Current language info
    currentLanguage,
    currentLanguageInfo: getCurrentLanguageInfo(),

    // Available languages
    availableLanguages: languageOptions,

    // Functions
    changeLanguage,
    getLanguageName,
    getLanguageFlag,

    // Translation function
    t,

    // State
    isChangingLanguage
  };
};
