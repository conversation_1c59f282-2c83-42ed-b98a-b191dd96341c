import { useState, useEffect, useCallback } from 'react';
import { Notification } from '../types';
import { notificationsService, CreateNotificationData } from '../services/notificationsService';
import { toast } from 'react-hot-toast';

interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  createNotification: (data: CreateNotificationData) => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  deleteAllNotifications: () => Promise<void>;
  refreshNotifications: () => void;
}

export const useNotifications = (userId: string | null): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Subscribe to notifications when user is available
  useEffect(() => {
    if (!userId) {
      setNotifications([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const unsubscribe = notificationsService.subscribeToNotifications(
      userId,
      (newNotifications) => {
        setNotifications(newNotifications);
        setLoading(false);
        setError(null);
      },
      (error) => {
        console.error('Error in notifications subscription:', error);
        setError('Erro ao carregar notificações');
        setLoading(false);
        toast.error('Erro ao carregar notificações');
      }
    );

    return unsubscribe;
  }, [userId]);

  // Create notification
  const createNotification = useCallback(async (data: CreateNotificationData) => {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    try {
      const notificationId = await notificationsService.createNotification(userId, data);
      return notificationId;
    } catch (error) {
      console.error('Error creating notification:', error);
      toast.error('Erro ao criar notificação');
      throw error;
    }
  }, [userId]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await notificationsService.markAsRead(notificationId);
      // The subscription will automatically update the state
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Erro ao marcar notificação como lida');
      throw error;
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    try {
      await notificationsService.markAllAsRead(userId);
      toast.success('Todas as notificações foram marcadas como lidas');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Erro ao marcar todas as notificações como lidas');
      throw error;
    }
  }, [userId]);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await notificationsService.deleteNotification(notificationId);
      toast.success('Notificação excluída');
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('Erro ao excluir notificação');
      throw error;
    }
  }, []);

  // Delete all notifications
  const deleteAllNotifications = useCallback(async () => {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    try {
      await notificationsService.deleteAllNotifications(userId);
      toast.success('Todas as notificações foram excluídas');
    } catch (error) {
      console.error('Error deleting all notifications:', error);
      toast.error('Erro ao excluir todas as notificações');
      throw error;
    }
  }, [userId]);

  // Refresh notifications (force reload)
  const refreshNotifications = useCallback(() => {
    if (userId) {
      setLoading(true);
      // The subscription will automatically refresh
    }
  }, [userId]);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  // Helper methods for common notification types
  const notifyNoteCreated = useCallback(async (noteTitle: string, noteId: string) => {
    await createNotification({
      type: 'note_created',
      title: 'Nova nota criada',
      message: `Nota "${noteTitle}" foi criada com sucesso`,
      noteId,
      actionUrl: `/note/${noteId}`
    });
  }, [createNotification]);

  const notifyNoteUpdated = useCallback(async (noteTitle: string, noteId: string) => {
    await createNotification({
      type: 'note_updated',
      title: 'Nota atualizada',
      message: `Nota "${noteTitle}" foi atualizada`,
      noteId,
      actionUrl: `/note/${noteId}`
    });
  }, [createNotification]);

  const notifyNoteShared = useCallback(async (noteTitle: string, noteId: string, sharedWith: string) => {
    await createNotification({
      type: 'note_shared',
      title: 'Nota compartilhada',
      message: `Nota "${noteTitle}" foi compartilhada com ${sharedWith}`,
      noteId,
      actionUrl: `/note/${noteId}`
    });
  }, [createNotification]);

  const notifyBackupCompleted = useCallback(async () => {
    await createNotification({
      type: 'backup',
      title: 'Backup realizado',
      message: 'Backup automático das suas notas foi realizado com sucesso',
      metadata: { timestamp: new Date().toISOString() }
    });
  }, [createNotification]);

  const notifyAchievement = useCallback(async (achievement: string, description: string) => {
    await createNotification({
      type: 'achievement',
      title: 'Conquista desbloqueada!',
      message: `${achievement}: ${description}`,
      metadata: { achievement }
    });
  }, [createNotification]);

  const notifyReminder = useCallback(async (title: string, message: string, noteId?: string) => {
    await createNotification({
      type: 'reminder',
      title,
      message,
      noteId,
      actionUrl: noteId ? `/note/${noteId}` : undefined
    });
  }, [createNotification]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    createNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications,
    refreshNotifications,
    // Helper methods
    notifyNoteCreated,
    notifyNoteUpdated,
    notifyNoteShared,
    notifyBackupCompleted,
    notifyAchievement,
    notifyReminder
  } as UseNotificationsReturn & {
    notifyNoteCreated: (noteTitle: string, noteId: string) => Promise<void>;
    notifyNoteUpdated: (noteTitle: string, noteId: string) => Promise<void>;
    notifyNoteShared: (noteTitle: string, noteId: string, sharedWith: string) => Promise<void>;
    notifyBackupCompleted: () => Promise<void>;
    notifyAchievement: (achievement: string, description: string) => Promise<void>;
    notifyReminder: (title: string, message: string, noteId?: string) => Promise<void>;
  };
};
