# 🚀 NoteFlow - Sistema de Notas Ultra Moderno

Um sistema completo de notas com AI integrada, colaboração em tempo real e recursos avançados de produtividade.

## ✨ Características Principais

### 🎯 **Core Features**
- ✅ **Interface Moderna**: Design limpo e responsivo com modo escuro/claro
- ✅ **Busca Inteligente**: Pesquisa por título, conteúdo e tags
- ✅ **Organização Avançada**: Categorias, tags, favoritos e fixação
- ✅ **Editor Rico**: Formatação de texto com preview em tempo real

### 🤖 **AI & Automação**
- ✅ **Sugestões Inteligentes**: AI sugere tags baseadas no conteúdo
- ✅ **Quick Capture**: Captura rápida de ideias com atalhos
- ✅ **Templates**: Modelos pré-definidos para diferentes tipos de notas

### 🎮 **Produtividade & Gamificação**
- ✅ **Pomodoro Timer**: Timer integrado para sessões focadas
- ✅ **Modo Foco**: Interface minimalista para concentração
- ✅ **Analytics**: Estatísticas de produtividade e insights
- ✅ **Streak Counter**: Acompanhamento de dias consecutivos

### 🧠 **Recursos Avançados**
- ✅ **Mind Map**: Visualização de conexões entre notas
- ✅ **Conexões**: Link entre notas relacionadas
- ✅ **Versionamento**: Histórico de alterações
- ✅ **Mood Tracking**: Acompanhamento de humor nas notas

### 👥 **Colaboração**
- ✅ **Compartilhamento**: Notas públicas/privadas
- ✅ **Colaboradores**: Múltiplos usuários por nota
- ✅ **Comentários**: Sistema de comentários em tempo real
- ✅ **Notificações**: Alertas de atividades

## 🛠️ Tecnologias Utilizadas

- **Frontend**: React 18 + TypeScript
- **Backend**: Firebase (Firestore + Functions + Auth)
- **Hosting**: Firebase Hosting
- **Database**: Cloud Firestore
- **Authentication**: Firebase Auth (Email, Google, GitHub)
- **Storage**: Firebase Cloud Storage
- **Functions**: Firebase Cloud Functions (Node.js)
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Build Tool**: Vite
- **State Management**: Custom Hooks + Firebase Hooks
- **Notifications**: React Hot Toast
- **PWA**: Service Worker + Web App Manifest
- **Testing**: Vitest + Testing Library
- **Architecture**: Component-based modular design

## 📁 Estrutura do Projeto

```
noteflow-app/
├── src/
│   ├── components/           # Componentes React reutilizáveis
│   │   ├── Header.tsx       # Cabeçalho da aplicação
│   │   ├── Sidebar.tsx      # Barra lateral de navegação
│   │   ├── NoteCard.tsx     # Card individual de nota
│   │   ├── MindMap.tsx      # Visualização Mind Map
│   │   ├── Modals.tsx       # Modais (Quick Capture, Command Palette, etc.)
│   │   └── AuthModal.tsx    # Modal de autenticação
│   ├── hooks/               # Custom hooks
│   │   ├── useAuth.ts       # Hook de autenticação Firebase
│   │   └── useNotesFirebase.ts # Hook principal com Firestore
│   ├── services/            # Serviços
│   │   ├── authService.ts   # Serviço de autenticação
│   │   └── notesService.ts  # Serviço de notas Firestore
│   ├── config/
│   │   └── firebase.ts      # Configuração Firebase
│   ├── test/                # Testes
│   │   ├── setup.ts         # Configuração dos testes
│   │   ├── utils.test.ts    # Testes das funções utilitárias
│   │   └── components/      # Testes de componentes
│   ├── types.ts             # Definições de tipos TypeScript
│   ├── constants.ts         # Constantes da aplicação
│   ├── utils.ts             # Funções utilitárias
│   ├── App.tsx              # Componente principal
│   ├── main.tsx             # Ponto de entrada
│   └── index.css            # Estilos globais
├── functions/               # Firebase Cloud Functions
│   ├── src/
│   │   ├── index.ts         # Functions principais
│   │   ├── ai.ts            # Functions de AI
│   │   ├── analytics.ts     # Functions de analytics
│   │   └── notifications.ts # Functions de notificações
│   └── package.json         # Dependências das functions
├── public/                  # Arquivos públicos
│   ├── manifest.json        # PWA Manifest
│   ├── sw.js               # Service Worker
│   └── icons/              # Ícones PWA
├── firebase.json            # Configuração Firebase
├── firestore.rules         # Regras de segurança Firestore
├── firestore.indexes.json  # Índices Firestore
├── storage.rules           # Regras de segurança Storage
├── .env.example            # Exemplo de variáveis de ambiente
└── FIREBASE_DEPLOY.md      # Guia de deploy Firebase
```

## 🚀 Instalação e Execução

### Pré-requisitos
- Node.js 18+
- npm ou yarn
- Conta Google (para Firebase)
- Firebase CLI: `npm install -g firebase-tools`

### Passos para instalação

1. **Clone o repositório**
```bash
git clone <repository-url>
cd noteflow-app
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure o Firebase**
```bash
# Login no Firebase
firebase login

# Inicialize o projeto (se necessário)
firebase init

# Configure as variáveis de ambiente
cp .env.example .env
# Edite o .env com suas configurações do Firebase
```

4. **Execute em desenvolvimento (com emuladores)**
```bash
# Terminal 1: Emuladores Firebase
npm run firebase:emulators

# Terminal 2: Aplicação React
npm run dev
```

5. **Acesse a aplicação**
```
http://localhost:3000
```

### Deploy para Produção

Siga o guia completo em [FIREBASE_DEPLOY.md](./FIREBASE_DEPLOY.md)

```bash
# Build e deploy
npm run firebase:deploy
```

## 📝 Scripts Disponíveis

### **Desenvolvimento**
- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Gera build de produção
- `npm run preview` - Preview do build de produção
- `npm run lint` - Executa linting do código
- `npm run type-check` - Verifica tipos TypeScript

### **Testes**
- `npm run test` - Executa testes
- `npm run test:ui` - Interface visual dos testes
- `npm run test:coverage` - Relatório de cobertura
- `npm run test:watch` - Testes em modo watch

### **Firebase**
- `npm run firebase:emulators` - Inicia emuladores Firebase
- `npm run firebase:deploy` - Deploy completo
- `npm run firebase:deploy:hosting` - Deploy apenas hosting
- `npm run firebase:deploy:functions` - Deploy apenas functions
- `npm run firebase:deploy:firestore` - Deploy apenas firestore

## 🎯 Como Usar

### Criando Notas
1. Clique em "Nova Nota" ou use `Ctrl+N`
2. Digite o título e conteúdo
3. Adicione tags e selecione categoria
4. Escolha cor e mood (opcional)
5. Salve com `Ctrl+S`

### Quick Capture
- Use `Ctrl+Shift+L` para captura rápida
- Digite sua ideia e pressione Enter
- A nota será criada automaticamente

### Command Palette
- Pressione `Ctrl+K` para abrir
- Digite comandos para ações rápidas
- Navegue com setas e Enter

### Mind Map
- Clique no ícone de Mind Map
- Visualize conexões entre notas
- Clique em uma nota para editá-la

### Colaboração
1. Clique no ícone de compartilhar na nota
2. Digite email do colaborador
3. Defina permissões (view/edit)
4. Envie convite

## 🔧 Personalização

### Temas
- Alterne entre modo claro/escuro
- Personalize cores das notas
- Configure preferências de layout

### Produtividade
- Configure timer Pomodoro
- Ative modo foco
- Defina metas diárias

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🎉 Próximas Features

- [ ] Sincronização em nuvem
- [ ] App mobile
- [ ] Integração com calendário
- [ ] OCR para imagens
- [ ] Gravação de áudio
- [ ] Exportação para PDF/Word
- [ ] Plugin para navegadores
- [ ] API pública

---

**Desenvolvido com ❤️ pela equipe NoteFlow**
